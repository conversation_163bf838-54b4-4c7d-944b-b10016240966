{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Automated Error Analysis\n", "\n", "This notebook uses LLMs to analyze and summarize the errors in the eval results across a single common dataset."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "pd.set_option('display.max_colwidth', None)\n", "pd.set_option('display.max_rows', None)\n", "import matplotlib.pyplot as plt\n", "import os\n", "from openai import OpenAI\n", "import os\n", "import json\n", "import warnings\n", "from eval.eval import get_all_minimal_queries\n", "warnings.filterwarnings('ignore')\n", "\n", "from defog_utils.utils_sql import SqlFeatures, get_sql_features\n", "\n", "openai = OpenAI(api_key=os.environ.get(\"OPENAI_API_KEY\"))\n", "model_4_latest = \"gpt-4o\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. Extract all results for a single dataset name"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["results_dir_path = f\"results/sqlcoder_8b_fullft_ds_003_llama3_mgn1_b1_0900_b2_0990\"\n", "keyword = \"api\" # Provide the dataset name e.g. 001, 002 etc"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["56 CSV files found in the directory\n"]}], "source": ["def list_csv_files(path):\n", "    # Get all file names in the directory\n", "    file_names = os.listdir(path)\n", "\n", "    # Filter for only .csv files\n", "    csv_files = [f for f in file_names if f.endswith('.csv')]\n", "\n", "    # Sort files alphabetically\n", "    csv_files.sort()\n", "\n", "    # Create dictionary with numbered keys\n", "    csv_dict = {i: csv_files[i] for i in range(len(csv_files))}\n", "\n", "    return csv_dict\n", "\n", "csv_files = list_csv_files(results_dir_path)\n", "print(f\"{len(csv_files)} CSV files found in the directory\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Results to analyze:\n", "c1000_api_advanced.csv\n", "c1000_api_advanced_cot.csv\n", "c1000_api_basic.csv\n", "c1000_api_basic_cot.csv\n", "c1000_api_idk.csv\n", "c1000_api_idk_cot.csv\n", "c1000_api_v1.csv\n", "c1000_api_v1_cot.csv\n", "c400_api_advanced.csv\n", "c400_api_advanced_cot.csv\n", "c400_api_basic.csv\n", "c400_api_basic_cot.csv\n", "c400_api_idk.csv\n", "c400_api_idk_cot.csv\n", "c400_api_v1.csv\n", "c400_api_v1_cot.csv\n", "c500_api_advanced.csv\n", "c500_api_advanced_cot.csv\n", "c500_api_basic.csv\n", "c500_api_basic_cot.csv\n", "c500_api_idk.csv\n", "c500_api_idk_cot.csv\n", "c500_api_v1.csv\n", "c500_api_v1_cot.csv\n", "c600_api_advanced.csv\n", "c600_api_advanced_cot.csv\n", "c600_api_basic.csv\n", "c600_api_basic_cot.csv\n", "c600_api_idk.csv\n", "c600_api_idk_cot.csv\n", "c600_api_v1.csv\n", "c600_api_v1_cot.csv\n", "c700_api_advanced.csv\n", "c700_api_advanced_cot.csv\n", "c700_api_basic.csv\n", "c700_api_basic_cot.csv\n", "c700_api_idk.csv\n", "c700_api_idk_cot.csv\n", "c700_api_v1.csv\n", "c700_api_v1_cot.csv\n", "c800_api_advanced.csv\n", "c800_api_advanced_cot.csv\n", "c800_api_basic.csv\n", "c800_api_basic_cot.csv\n", "c800_api_idk.csv\n", "c800_api_idk_cot.csv\n", "c800_api_v1.csv\n", "c800_api_v1_cot.csv\n", "c900_api_advanced.csv\n", "c900_api_advanced_cot.csv\n", "c900_api_basic.csv\n", "c900_api_basic_cot.csv\n", "c900_api_idk.csv\n", "c900_api_idk_cot.csv\n", "c900_api_v1.csv\n", "c900_api_v1_cot.csv\n"]}], "source": ["# Get indices of csv_files with these keywords\n", "keywords = [keyword]\n", "keywords_to_exclude = []\n", "selected_models = [i for i, s in enumerate(csv_files.values()) if all(xs in s for xs in keywords)]\n", "if keywords_to_exclude:\n", "    selected_models = [i for i in selected_models if not any(xs in csv_files[i] for xs in keywords_to_exclude)]\n", "\n", "# Print selected models\n", "print(\"Results to analyze:\")\n", "for i in selected_models:\n", "    print(f\"{csv_files[i]}\")\n", "\n", "# Load results from csv file into dataframe\n", "dfs = {}\n", "for id in selected_models:\n", "    file_name = csv_files[id]\n", "    model = file_name.replace('.csv', '')\n", "    csv_path = os.path.join(results_dir_path, file_name)\n", "    dfs[model] = pd.read_csv(csv_path, comment='#')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>db_name</th>\n", "      <th>query_category</th>\n", "      <th>question</th>\n", "      <th>instructions</th>\n", "      <th>query</th>\n", "      <th>full_instructions</th>\n", "      <th>db_type</th>\n", "      <th>generated_query</th>\n", "      <th>reason</th>\n", "      <th>error_msg</th>\n", "      <th>...</th>\n", "      <th>question_0</th>\n", "      <th>query_0</th>\n", "      <th>question_1</th>\n", "      <th>query_1</th>\n", "      <th>cot_instructions</th>\n", "      <th>logprobs</th>\n", "      <th>model</th>\n", "      <th>true_queries</th>\n", "      <th>error_msg_short</th>\n", "      <th>eval</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>broker</td>\n", "      <td>instructions_cte_join</td>\n", "      <td>For customers with at least 5 total transactions, what is their transaction success rate? Return the customer name and success rate, ordered from lowest to highest success rate.</td>\n", "      <td>\\nFollow the instructions below to generate the query:\\nTo get the success rate of transactions per customer, join customer and transaction tables, group by customer, and calculate the percentage of successful transactions.\\n</td>\n", "      <td>WITH cust_tx_stats AS (SELECT c.sbCustId, c.sbCustName, COUNT(t.sbTxId) AS total_tx, SUM(CASE WHEN t.sbTxStatus = 'success' THEN 1 ELSE 0 END) AS success_tx FROM sbCustomer c JOIN sbTransaction t ON c.sbCustId = t.sbTxCustId GROUP BY c.sbCustId, c.sbCustName) SELECT sbCustName, success_tx::float/total_tx AS success_rate FROM cust_tx_stats WHERE total_tx &gt;= 5 ORDER BY success_rate</td>\n", "      <td>CR = customer rank by total transaction amount, with different rankings based on transaction amounts MoMC = month-over-month change in average closing price based on previous month's averages for each ticker each month To get the success rate of transactions per customer, join customer and transaction tables, group by customer, and calculate the percentage of successful transactions. Always join transactions with customers before using the transactions table. TAC = Total Active Customers who joined after a certain date</td>\n", "      <td>postgres</td>\n", "      <td>WITH customer_transactions AS ( SELECT c.sbCustName, t.sbTxStatus FROM sbCustomer c JOIN sbTransaction t ON c.sbCustId = t.sbTxCustId ) SELECT ct.sbCustName, CAST ( SUM ( CASE WHEN ct.sbTxStatus = 'success' THEN 1 ELSE 0 END ) AS FLOAT ) / NULLIF ( COUNT ( ct.sbTxStatus ) , 0 ) AS success_rate FROM customer_transactions ct GROUP BY ct.sbCustName HAVING COUNT ( ct.sbTxStatus ) &gt;= 5 ORDER BY success_rate ASC;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[]</td>\n", "      <td>c1000_api</td>\n", "      <td>[WITH cust_tx_stats AS (SELECT c.sbCustId, c.sbCustName, COUNT(t.sbTxId) AS total_tx, SUM(CASE WHEN t.sbTxStatus = 'success' THEN 1 ELSE 0 END) AS success_tx FROM sbCustomer c JOIN sbTransaction t ON c.sbCustId = t.sbTxCustId GROUP BY c.sbCustId, c.sbCustName) SELECT sbCustName, success_tx::float/total_tx AS success_rate FROM cust_tx_stats WHERE total_tx &gt;= 5 ORDER BY success_rate]</td>\n", "      <td>NaN</td>\n", "      <td>advanced</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 32 columns</p>\n", "</div>"], "text/plain": ["  db_name         query_category  \\\n", "0  broker  instructions_cte_join   \n", "\n", "                                                                                                                                                                            question  \\\n", "0  For customers with at least 5 total transactions, what is their transaction success rate? Return the customer name and success rate, ordered from lowest to highest success rate.   \n", "\n", "                                                                                                                                                                                                                        instructions  \\\n", "0  \\nFollow the instructions below to generate the query:\\nTo get the success rate of transactions per customer, join customer and transaction tables, group by customer, and calculate the percentage of successful transactions.\\n   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                            query  \\\n", "0  WITH cust_tx_stats AS (SELECT c.sbCustId, c.sbCustName, COUNT(t.sbTxId) AS total_tx, SUM(CASE WHEN t.sbTxStatus = 'success' THEN 1 ELSE 0 END) AS success_tx FROM sbCustomer c JOIN sbTransaction t ON c.sbCustId = t.sbTxCustId GROUP BY c.sbCustId, c.sbCustName) SELECT sbCustName, success_tx::float/total_tx AS success_rate FROM cust_tx_stats WHERE total_tx >= 5 ORDER BY success_rate   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              full_instructions  \\\n", "0  CR = customer rank by total transaction amount, with different rankings based on transaction amounts MoMC = month-over-month change in average closing price based on previous month's averages for each ticker each month To get the success rate of transactions per customer, join customer and transaction tables, group by customer, and calculate the percentage of successful transactions. Always join transactions with customers before using the transactions table. TAC = Total Active Customers who joined after a certain date   \n", "\n", "    db_type  \\\n", "0  postgres   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                                              generated_query  \\\n", "0  WITH customer_transactions AS ( SELECT c.sbCustName, t.sbTxStatus FROM sbCustomer c JOIN sbTransaction t ON c.sbCustId = t.sbTxCustId ) SELECT ct.sbCustName, CAST ( SUM ( CASE WHEN ct.sbTxStatus = 'success' THEN 1 ELSE 0 END ) AS FLOAT ) / NULLIF ( COUNT ( ct.sbTxStatus ) , 0 ) AS success_rate FROM customer_transactions ct GROUP BY ct.sbCustName HAVING COUNT ( ct.sbTxStatus ) >= 5 ORDER BY success_rate ASC;   \n", "\n", "   reason error_msg  ...  question_0  query_0  question_1  query_1  \\\n", "0     NaN       NaN  ...         NaN      NaN         NaN      NaN   \n", "\n", "   cot_instructions  logprobs      model  \\\n", "0               NaN        []  c1000_api   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                       true_queries  \\\n", "0  [WITH cust_tx_stats AS (SELECT c.sbCustId, c.sbCustName, COUNT(t.sbTxId) AS total_tx, SUM(CASE WHEN t.sbTxStatus = 'success' THEN 1 ELSE 0 END) AS success_tx FROM sbCustomer c JOIN sbTransaction t ON c.sbCustId = t.sbTxCustId GROUP BY c.sbCustId, c.sbCustName) SELECT sbCustName, success_tx::float/total_tx AS success_rate FROM cust_tx_stats WHERE total_tx >= 5 ORDER BY success_rate]   \n", "\n", "   error_msg_short      eval  \n", "0              NaN  advanced  \n", "\n", "[1 rows x 32 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["#Combine all dataframes of selected models into one\n", "all_dfs = []\n", "for model in dfs:\n", "    temp_df = dfs[model]\n", "    temp_df['model'] = model\n", "    all_dfs.append(temp_df)\n", "df = pd.concat(all_dfs)\n", "# Apply get_all_minimal_queries to all queries\n", "df['true_queries'] = df['query'].apply(get_all_minimal_queries)\n", "df['error_msg_short'] = df['error_msg'].str.split(\"\\n\\n\").str[0].str.replace(\"QUERY EXECUTION ERROR:\", \"\")\n", "\n", "# Split model column by the last underscore \n", "df['eval'] = df['model'].str.rsplit(pat='_', n=1).str[1]\n", "df['model'] = df['model'].str.rsplit(pat='_', n=1).str[0]\n", "df.head(1)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. <PERSON><PERSON><PERSON> Correctness\n", "#### 2.1 Compute Correctness by Category"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot the correctness by category\n", "df_category_correct = df.pivot_table(\"correct\", \"query_category\", aggfunc=\"mean\").sort_values('correct', ascending=False)\n", "df_category_correct.plot(kind='barh', color='skyblue', figsize=(10, 6))\n", "plt.title('Correctness by SQL category')\n", "plt.xlabel('Correctness')\n", "plt.ylabel('Category')\n", "# add labels\n", "for i, v in enumerate(df_category_correct['correct']):\n", "    plt.text(v, i, f\"{v*100:.2f}%\", color='black', va='center')\n", "ax = plt.gca()\n", "for spine in ['right', 'top']:\n", "    ax.spines[spine].set_visible(False)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'instructions_cte_window': 0.33035714285714285,\n", " 'keywords_ratio': 0.4017857142857143,\n", " 'cat_c': 0.6387755102040816,\n", " 'date_functions': 0.6828571428571428,\n", " 'instructions_date_join': 0.6875,\n", " 'basic_join_group_order_limit': 0.7142857142857143,\n", " 'instruct': 0.7408163265306122,\n", " 'instructions_cte_join': 0.7678571428571429,\n", " 'basic_join_date_group_order_limit': 0.7678571428571429,\n", " 'basic_join_distinct': 0.8214285714285714,\n", " 'instructions_string_matching': 0.8482142857142857,\n", " 'table_join': 0.8653061224489796,\n", " 'cat_b': 0.8755102040816326,\n", " 'cat_a': 0.8938775510204081,\n", " 'ratio': 0.9122448979591836,\n", " 'group_by': 0.9224489795918367,\n", " 'keywords_aggregate': 0.9553571428571429,\n", " 'basic_group_order_limit': 0.9642857142857143,\n", " 'order_by': 0.9714285714285714,\n", " 'basic_left_join': 0.9910714285714286}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Convert df to dict\n", "category_corr_dict = df_category_correct.sort_values('correct', ascending=True).to_dict()['correct']\n", "category_corr_dict"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2.2 Compute Correctness by SQL Features\n", "\n", "We parse the expected query to get its features and see which features are more error-prone."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sql_num_columns</th>\n", "      <th>sql_num_tables</th>\n", "      <th>sql_table_alias</th>\n", "      <th>sql_joins</th>\n", "      <th>sql_join_same</th>\n", "      <th>sql_join_left</th>\n", "      <th>sql_has_null</th>\n", "      <th>sql_distinct</th>\n", "      <th>sql_cte</th>\n", "      <th>sql_union</th>\n", "      <th>...</th>\n", "      <th>sql_generate_timeseries</th>\n", "      <th>sql_string_concat</th>\n", "      <th>sql_string_exact_match</th>\n", "      <th>sql_string_case_insensitive_match</th>\n", "      <th>sql_string_like_match</th>\n", "      <th>sql_string_ilike_match</th>\n", "      <th>sql_string_substring</th>\n", "      <th>sql_string_regex</th>\n", "      <th>sql_sorry</th>\n", "      <th>correct</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>8</td>\n", "      <td>3</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 49 columns</p>\n", "</div>"], "text/plain": ["   sql_num_columns  sql_num_tables  sql_table_alias  sql_joins  sql_join_same  \\\n", "0                8               3             True          1          False   \n", "\n", "   sql_join_left  sql_has_null  sql_distinct  sql_cte  sql_union  ...  \\\n", "0          False         False         False        1      False  ...   \n", "\n", "   sql_generate_timeseries  sql_string_concat  sql_string_exact_match  \\\n", "0                    False              False                    True   \n", "\n", "   sql_string_case_insensitive_match  sql_string_like_match  \\\n", "0                              False                  False   \n", "\n", "   sql_string_ilike_match  sql_string_substring  sql_string_regex  sql_sorry  \\\n", "0                   False                 False             False      False   \n", "\n", "   correct  \n", "0        1  \n", "\n", "[1 rows x 49 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"sql_features\"] = df[\"query\"].apply(lambda x: get_sql_features(x).to_dict())\n", "df_sql_features = pd.json_normalize(df[\"sql_features\"])\n", "# add correct column\n", "df_sql_features[\"correct\"] = df[\"correct\"].reset_index(drop=True)\n", "df_sql_features.head(1)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: >"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# for all the wrong queries, sum up each column\n", "df_sql_features_wrong = df_sql_features[df_sql_features[\"correct\"] == 0]\n", "df_sql_features_wrong = df_sql_features_wrong.drop(columns=[\"correct\", \"sql_num_tables\", \"sql_num_columns\", \"sql_joins\", \"sql_table_alias\"])\n", "df_sql_features_wrong_sum = df_sql_features_wrong.sum().sort_values(ascending=False)\n", "df_sql_features_wrong_sum.plot(kind='barh', color='skyblue', figsize=(10, 6))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>correct</th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>sql_num_columns</th>\n", "      <td>3482</td>\n", "      <td>12249</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_num_tables</th>\n", "      <td>1659</td>\n", "      <td>5840</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_table_alias</th>\n", "      <td>357</td>\n", "      <td>931</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_joins</th>\n", "      <td>730</td>\n", "      <td>2107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_join_same</th>\n", "      <td>14</td>\n", "      <td>14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_join_left</th>\n", "      <td>49</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_has_null</th>\n", "      <td>24</td>\n", "      <td>200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_distinct</th>\n", "      <td>227</td>\n", "      <td>776</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_cte</th>\n", "      <td>205</td>\n", "      <td>243</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_union</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_case_condition</th>\n", "      <td>58</td>\n", "      <td>306</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_has_in</th>\n", "      <td>33</td>\n", "      <td>79</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_additive</th>\n", "      <td>244</td>\n", "      <td>456</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_ratio</th>\n", "      <td>123</td>\n", "      <td>521</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_round</th>\n", "      <td>4</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_order_by</th>\n", "      <td>308</td>\n", "      <td>1736</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_limit</th>\n", "      <td>90</td>\n", "      <td>666</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_group_by</th>\n", "      <td>408</td>\n", "      <td>1678</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_having</th>\n", "      <td>13</td>\n", "      <td>29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_agg_count</th>\n", "      <td>384</td>\n", "      <td>1590</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_agg_count_distinct</th>\n", "      <td>145</td>\n", "      <td>508</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_agg_sum</th>\n", "      <td>187</td>\n", "      <td>513</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_agg_avg</th>\n", "      <td>90</td>\n", "      <td>428</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_agg_min</th>\n", "      <td>45</td>\n", "      <td>25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_agg_max</th>\n", "      <td>65</td>\n", "      <td>61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_agg_var</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_agg_percentile</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_window_over</th>\n", "      <td>82</td>\n", "      <td>58</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_lag</th>\n", "      <td>29</td>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_rank</th>\n", "      <td>53</td>\n", "      <td>45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_has_date</th>\n", "      <td>49</td>\n", "      <td>77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_has_date_text</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_has_date_int</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_date_trunc</th>\n", "      <td>106</td>\n", "      <td>202</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_date_part</th>\n", "      <td>54</td>\n", "      <td>142</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_current_date_time</th>\n", "      <td>96</td>\n", "      <td>366</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_interval</th>\n", "      <td>113</td>\n", "      <td>307</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_date_time_type_conversion</th>\n", "      <td>47</td>\n", "      <td>79</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_date_time_format</th>\n", "      <td>0</td>\n", "      <td>14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_generate_timeseries</th>\n", "      <td>20</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_string_concat</th>\n", "      <td>14</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_string_exact_match</th>\n", "      <td>179</td>\n", "      <td>465</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_string_case_insensitive_match</th>\n", "      <td>0</td>\n", "      <td>28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_string_like_match</th>\n", "      <td>38</td>\n", "      <td>158</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_string_ilike_match</th>\n", "      <td>104</td>\n", "      <td>442</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_string_substring</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_string_regex</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sql_sorry</th>\n", "      <td>290</td>\n", "      <td>1180</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["correct                               0      1\n", "sql_num_columns                    3482  12249\n", "sql_num_tables                     1659   5840\n", "sql_table_alias                     357    931\n", "sql_joins                           730   2107\n", "sql_join_same                        14     14\n", "sql_join_left                        49    175\n", "sql_has_null                         24    200\n", "sql_distinct                        227    776\n", "sql_cte                             205    243\n", "sql_union                             0      0\n", "sql_case_condition                   58    306\n", "sql_has_in                           33     79\n", "sql_additive                        244    456\n", "sql_ratio                           123    521\n", "sql_round                             4     10\n", "sql_order_by                        308   1736\n", "sql_limit                            90    666\n", "sql_group_by                        408   1678\n", "sql_having                           13     29\n", "sql_agg_count                       384   1590\n", "sql_agg_count_distinct              145    508\n", "sql_agg_sum                         187    513\n", "sql_agg_avg                          90    428\n", "sql_agg_min                          45     25\n", "sql_agg_max                          65     61\n", "sql_agg_var                           0      0\n", "sql_agg_percentile                    0      0\n", "sql_window_over                      82     58\n", "sql_lag                              29     13\n", "sql_rank                             53     45\n", "sql_has_date                         49     77\n", "sql_has_date_text                     0      0\n", "sql_has_date_int                      0      0\n", "sql_date_trunc                      106    202\n", "sql_date_part                        54    142\n", "sql_current_date_time                96    366\n", "sql_interval                        113    307\n", "sql_date_time_type_conversion        47     79\n", "sql_date_time_format                  0     14\n", "sql_generate_timeseries              20      8\n", "sql_string_concat                    14      0\n", "sql_string_exact_match              179    465\n", "sql_string_case_insensitive_match     0     28\n", "sql_string_like_match                38    158\n", "sql_string_ilike_match              104    442\n", "sql_string_substring                  0      0\n", "sql_string_regex                      0      0\n", "sql_sorry                           290   1180"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# get the correct/incorrect counts for each feature\n", "df_sql_features_correct = df_sql_features.groupby('correct').sum().T\n", "df_sql_features_correct"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The above counts are univariate, which isn't very helpful. The next step is to see how groups of these features perform poorly together.\n", "\n", "For example, sql_date_part + sql_case_condition + sql_joins > 1 fail the most times, then we can know and generate more of such data."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. Analyze invalid SQL with DB exec errors"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["44 questions with recurring exec errors\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>question</th>\n", "      <th>error_db_exec</th>\n", "      <th>error_msg_short</th>\n", "      <th>true_queries</th>\n", "      <th>generated_query</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>How does the ratio of publications to journals change over the years? Return the annual numbers of publications and journals as well.</td>\n", "      <td>1</td>\n", "      <td>(psycopg2.errors.UndefinedColumn) column j.year does not exist\\nLINE 1: ...p GROUP BY p.year ) , yearly_journals AS ( SELECT j.year, CO...\\n                                                             ^</td>\n", "      <td>[SELECT publication.year, COUNT(DISTINCT publication.pid) AS num_publications, COUNT(DISTINCT publication.jid) AS num_journals, CAST(COUNT(DISTINCT publication.pid) AS FLOAT) / NULLIF(COUNT(DISTINCT publication.jid), 0) AS ratio FROM publication GROUP BY publication.year ORDER BY publication.year]</td>\n", "      <td>WITH yearly_publications AS ( SELECT p.year, COUNT ( p.pid ) AS num_publications FROM publication p GROUP BY p.year ) , yearly_journals AS ( SELECT j.year, COUNT ( j.jid ) AS num_journals FROM journal j GROUP BY j.year ) SELECT yp.year, yp.num_publications, yj.num_journals, CAST ( yp.num_publications AS FLOAT ) / NULLIF ( yj.num_journals, 0 ) AS publication_to_journal_ratio FROM yearly_publications yp JOIN yearly_journals yj ON yp.year = yj.year ORDER BY yp.year NULLS LAST;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>How does the ratio of publications to journals change over the years? Return the annual numbers of publications and journals as well.</td>\n", "      <td>1</td>\n", "      <td>(psycopg2.errors.UndefinedColumn) column j.year does not exist\\nLINE 1: ...p GROUP BY p.year ) , yearly_journals AS ( SELECT j.year, CO...\\n                                                             ^</td>\n", "      <td>[SELECT publication.year, COUNT(DISTINCT publication.pid) AS num_publications, COUNT(DISTINCT publication.jid) AS num_journals, CAST(COUNT(DISTINCT publication.pid) AS FLOAT) / NULLIF(COUNT(DISTINCT publication.jid), 0) AS ratio FROM publication GROUP BY publication.year ORDER BY publication.year]</td>\n", "      <td>WITH yearly_publications AS ( SELECT p.year, COUNT ( p.pid ) AS num_publications FROM publication p GROUP BY p.year ) , yearly_journals AS ( SELECT j.year, COUNT ( j.jid ) AS num_journals FROM journal j GROUP BY j.year ) SELECT yp.year, yp.num_publications, yj.num_journals, CAST ( yp.num_publications AS FLOAT ) / NULLIF ( yj.num_journals, 0 ) AS publication_to_journal_ratio FROM yearly_publications yp JOIN yearly_journals yj ON yp.year = yj.year ORDER BY yp.year NULLS LAST;</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                                                                                 question  \\\n", "21  How does the ratio of publications to journals change over the years? Return the annual numbers of publications and journals as well.   \n", "21  How does the ratio of publications to journals change over the years? Return the annual numbers of publications and journals as well.   \n", "\n", "    error_db_exec  \\\n", "21              1   \n", "21              1   \n", "\n", "                                                                                                                                                                                                error_msg_short  \\\n", "21   (psycopg2.errors.UndefinedColumn) column j.year does not exist\\nLINE 1: ...p GROUP BY p.year ) , yearly_journals AS ( SELECT j.year, CO...\\n                                                             ^   \n", "21   (psycopg2.errors.UndefinedColumn) column j.year does not exist\\nLINE 1: ...p GROUP BY p.year ) , yearly_journals AS ( SELECT j.year, CO...\\n                                                             ^   \n", "\n", "                                                                                                                                                                                                                                                                                                  true_queries  \\\n", "21  [SELECT publication.year, COUNT(DISTINCT publication.pid) AS num_publications, COUNT(DISTINCT publication.jid) AS num_journals, CAST(COUNT(DISTINCT publication.pid) AS FLOAT) / NULLIF(COUNT(DISTINCT publication.jid), 0) AS ratio FROM publication GROUP BY publication.year ORDER BY publication.year]   \n", "21  [SELECT publication.year, COUNT(DISTINCT publication.pid) AS num_publications, COUNT(DISTINCT publication.jid) AS num_journals, CAST(COUNT(DISTINCT publication.pid) AS FLOAT) / NULLIF(COUNT(DISTINCT publication.jid), 0) AS ratio FROM publication GROUP BY publication.year ORDER BY publication.year]   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   generated_query  \n", "21  WITH yearly_publications AS ( SELECT p.year, COUNT ( p.pid ) AS num_publications FROM publication p GROUP BY p.year ) , yearly_journals AS ( SELECT j.year, COUNT ( j.jid ) AS num_journals FROM journal j GROUP BY j.year ) SELECT yp.year, yp.num_publications, yj.num_journals, CAST ( yp.num_publications AS FLOAT ) / NULLIF ( yj.num_journals, 0 ) AS publication_to_journal_ratio FROM yearly_publications yp JOIN yearly_journals yj ON yp.year = yj.year ORDER BY yp.year NULLS LAST;  \n", "21  WITH yearly_publications AS ( SELECT p.year, COUNT ( p.pid ) AS num_publications FROM publication p GROUP BY p.year ) , yearly_journals AS ( SELECT j.year, COUNT ( j.jid ) AS num_journals FROM journal j GROUP BY j.year ) SELECT yp.year, yp.num_publications, yj.num_journals, CAST ( yp.num_publications AS FLOAT ) / NULLIF ( yj.num_journals, 0 ) AS publication_to_journal_ratio FROM yearly_publications yp JOIN yearly_journals yj ON yp.year = yj.year ORDER BY yp.year NULLS LAST;  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get db exec error rows across all result files\n", "df_error_exec = df[df['error_db_exec'] == 1][['model', 'db_name', 'question', 'error_db_exec', 'error_msg_short', 'true_queries', 'generated_query']].sort_values(['db_name','question'])\n", "# Get questions with recurring exec errors\n", "df_error_exec_recurr = df_error_exec[df_error_exec.duplicated(subset=['db_name', 'question'], keep=False)][['question', 'error_db_exec', 'error_msg_short', 'true_queries', 'generated_query']]\n", "print(f\"{len(df_error_exec_recurr['question'].unique())} questions with recurring exec errors\")\n", "df_error_exec_recurr.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Convert error_msg_short col to a string of bullet points\n", "error_exec_str = \"\\n\".join([f\"- {x}\" for x in df_error_exec_recurr['error_msg_short']])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Extract patterns from db execution error messages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get error exec patterns\n", "def get_error_exec_patterns(\n", "    model: str, error_exec_str: str\n", ") -> str:\n", "    \"\"\"\n", "    Use LLM to extract recurring patterns in a list of error messages.\n", "    \"\"\"\n", "    messages = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": f\"\"\"Your task is to identify recurring patterns in the error messages below and provide a summary of the patterns.\"\"\"\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": f\"\"\"List of error messages:\n", "{error_exec_str}\n", "\n", "Format your response as a numbered list of recurring patterns in the error messages. \n", "Each point should be a concise yet detailed summary of a trend identified in the error messages along with specific examples.\n", "Do not include any other information before and after the list.\n", "\"\"\",\n", "        },\n", "    ]\n", "\n", "    completion = openai.chat.completions.create(\n", "        model=model,\n", "        messages=messages,\n", "        max_tokens=1000,\n", "        temperature=0,\n", "        # top_p=0.5,\n", "        # response_format = {\"type\": \"json_object\"}\n", "    )\n", "    completion = completion.choices[0].message.content\n", "    return completion"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["error_exec_summary = get_error_exec_patterns(model_4_latest, error_exec_str)\n", "print(error_exec_summary)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4. Analyze valid but wrong examples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get valid but correct examples across all result files\n", "df_valid_wrong = df[(df['correct'] == 0) & (df['error_db_exec'] == 0)][['model', 'db_name', 'query_category', 'question', 'instructions', 'correct', 'error_db_exec', 'true_queries', 'generated_query']].sort_values(['db_name','question']).fillna('')\n", "# Get questions that were repeatedly valid but wrong\n", "df_valid_wrong_recurr = df_valid_wrong[df_valid_wrong.duplicated(subset=['db_name', 'question'], keep=False)][['db_name', 'query_category', 'question', 'instructions', 'true_queries', 'generated_query']]\n", "print(f\"{len(df_valid_wrong_recurr['question'].unique())} unique questions that are recurring valid but wrong\")\n", "df_valid_wrong_recurr.head(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get first row of all duplicates\n", "# To reduce the number of LLM calls, we will assume that all duplicates are wrong in the same way\n", "df_valid_wrong_recurr_first = df_valid_wrong_recurr.drop_duplicates(subset=['db_name', 'question'], keep='first')[['query_category', 'question', 'instructions', 'true_queries', 'generated_query']]\n", "df_valid_wrong_recurr_first.head(4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get reasons for valid but wrong examples\n", "def explain_incorrect(\n", "    model: str, question: str, instructions: str, true_sqls: list, generated_sql: str\n", ") -> str:\n", "    \"\"\"\n", "    Use LLM to explain why a SQL query is incorrect given a question, instructions and the true SQL queries.\n", "    \"\"\"\n", "    if instructions:\n", "        instructions = f\"\\nInstructions: {instructions}\\n\"\n", "    messages = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": f\"\"\"Your task is to explain why the SQL query is incorrect given the question, instructions and the true SQL queries.\"\"\"\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": f\"\"\"Question: {question}{instructions}\n", "Incorrect SQL: {generated_sql}\n", "\n", "True SQL queries:\n", "{true_sqls}\n", "\n", "Format your response as a valid JSON string with reason as a key. \n", "Your response should look like the string below:\n", "{{ \"reason\": \"Your reasoning for why the SQL query is incorrect according to the question and the true SQL queries.\"\n", "}}\n", "\n", "Do not include any other information before and after the JSON string.\n", "\"\"\",\n", "        },\n", "    ]\n", "\n", "    completion = openai.chat.completions.create(\n", "        model=model,\n", "        messages=messages,\n", "        max_tokens=500,\n", "        temperature=0,\n", "        # top_p=0.5,\n", "        response_format = {\"type\": \"json_object\"}\n", "    )\n", "    completion = completion.choices[0].message.content\n", "    try:\n", "        completion_dict = json.loads(completion)\n", "    except:\n", "        print(f\"Error parsing completion {completion}\", flush=True)\n", "        completion_dict = {\"reason\": None}\n", "    reason = completion_dict.get(\"reason\", None)\n", "    return reason"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tqdm.notebook import tqdm\n", "tqdm.pandas()\n", "# Get explanations for valid but wrong examples\n", "df_valid_wrong_recurr_first['reason_incorrect'] = df_valid_wrong_recurr_first.progress_apply(lambda x: explain_incorrect(model_4_latest, x['question'], x['instructions'], x['true_queries'], x['generated_query']), axis=1)\n", "df_valid_wrong_recurr_first"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Convert reason_incorrect col to a string of bullet points\n", "reason_incorrect_str = \"\\n\".join([f\"- {x}\" for x in df_valid_wrong_recurr_first['reason_incorrect']])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Get recurring patterns from reasons of valid but wrong examples."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get reasons for valid but wrong examples\n", "def get_valid_wrong_patterns(\n", "    model: str, reason_incorrect_str: str\n", ") -> str:\n", "    \"\"\"\n", "    Use LLM to extract recurring patterns in a list of error messages that describe why a SQL query is wrong according to the question and the true SQL queries.\n", "    \"\"\"\n", "    messages = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": f\"\"\"Your task is to identify recurring patterns in the error messages below and provide a summary of the patterns.\"\"\"\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": f\"\"\"List of error messages that describe why a SQL query is wrong according to the question and the true SQL queries:\n", "{reason_incorrect_str}\n", "\n", "Format your response as a numbered list of recurring patterns in the error messages. \n", "Each point should be a concise yet detailed summary of a trend identified in the error messages along with specific examples (e.g. inability to follow instructions, common errors in specific SQL categories, etc.).\n", "Do not include any other information before and after the list.\n", "\"\"\",\n", "        },\n", "    ]\n", "\n", "    completion = openai.chat.completions.create(\n", "        model=model,\n", "        messages=messages,\n", "        max_tokens=1000,\n", "        temperature=0,\n", "        # top_p=0.5,\n", "        # response_format = {\"type\": \"json_object\"}\n", "    )\n", "    completion = completion.choices[0].message.content\n", "    return completion"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["valid_wrong_summary = get_valid_wrong_patterns(model_4_latest, reason_incorrect_str)\n", "print(valid_wrong_summary)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# Store summaries in a json file\n", "summary_dict = {\n", "    \"category_corr_dict\": category_corr_dict,\n", "    \"error_exec_summary\": error_exec_summary,\n", "    \"valid_wrong_summary\": valid_wrong_summary\n", "}\n", "output_file = f\"{results_dir_path}error_analysis_ds_{ds_to_analyze}.json\"\n", "with open(output_file, 'w') as f:\n", "    json.dump(summary_dict, f, indent=4)"]}], "metadata": {"kernelspec": {"display_name": "defog", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}