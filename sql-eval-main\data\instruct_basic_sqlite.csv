db_name,db_type,query_category,query,question
broker,sqlite,basic_join_date_group_order_limit,"SELECT c.sbCustCountry, COUNT(t.sbTxId) AS num_transactions, SUM(t.sbTxAmount) AS total_amount FROM sbCustomer AS c JOIN sbTransaction AS t ON c.sbCustId = t.sbTxCustId WHERE t.sbTxDateTime >= DATE('now', '-30 days') GROUP BY c.sbCustCountry ORDER BY total_amount DESC LIMIT 5;","What are the top 5 countries by total transaction amount in the past 30 days, inclusive of 30 days ago? Return the country name, number of transactions and total transaction amount."
broker,sqlite,basic_join_date_group_order_limit,"SELECT t.sbTxType, COUNT(DISTINCT t.sbTxCustId) AS num_customers, AVG(t.sbTxShares) AS avg_shares FROM sbTransaction AS t WHERE t.sbTxDateTime BETWEEN '2023-01-01' AND '2023-03-31 23:59:59' GROUP BY t.sbTxType ORDER BY CASE WHEN num_customers IS NULL THEN 1 ELSE 0 END DESC, num_customers DESC LIMIT 3;","How many distinct customers made each type of transaction between Jan 1, 2023 and Mar 31, 2023 (inclusive of start and end dates)? Return the transaction type, number of distinct customers and average number of shares, for the top 3 transaction types by number of customers."
broker,sqlite,basic_join_group_order_limit,"SELECT tk.sbTickerSymbol, COUNT(tx.sbTxId) AS num_transactions, SUM(tx.sbTxAmount) AS total_amount FROM sbTicker AS tk JOIN sbTransaction AS tx ON tk.sbTickerId = tx.sbTxTickerId GROUP BY tk.sbTickerSymbol ORDER BY CASE WHEN total_amount IS NULL THEN 1 ELSE 0 END DESC, total_amount DESC LIMIT 10;","What are the top 10 ticker symbols by total transaction amount? Return the ticker symbol, number of transactions and total transaction amount."
broker,sqlite,basic_join_group_order_limit,"SELECT c.sbCustState, t.sbTickerType, COUNT(*) AS num_transactions FROM sbTransaction AS tx JOIN sbCustomer AS c ON tx.sbTxCustId = c.sbCustId JOIN sbTicker AS t ON tx.sbTxTickerId = t.sbTickerId GROUP BY c.sbCustState, t.sbTickerType ORDER BY CASE WHEN num_transactions IS NULL THEN 1 ELSE 0 END DESC, num_transactions DESC LIMIT 5;","What are the top 5 combinations of customer state and ticker type by number of transactions? Return the customer state, ticker type and number of transactions."
broker,sqlite,basic_join_distinct,SELECT DISTINCT c.sbCustId FROM sbCustomer AS c JOIN sbTransaction AS t ON c.sbCustId = t.sbTxCustId WHERE t.sbTxType = 'buy';,Return the distinct list of customer IDs who have made a 'buy' transaction.
broker,sqlite,basic_join_distinct,SELECT DISTINCT tk.sbTickerId FROM sbTicker AS tk JOIN sbDailyPrice AS dp ON tk.sbTickerId = dp.sbDpTickerId WHERE dp.sbDpDate >= '2023-04-01';,"Return the distinct list of ticker IDs that have daily price records on or after Apr 1, 2023."
broker,sqlite,basic_group_order_limit,"SELECT sbTxStatus, COUNT(*) AS num_transactions FROM sbTransaction GROUP BY sbTxStatus ORDER BY CASE WHEN num_transactions IS NULL THEN 1 ELSE 0 END DESC, num_transactions DESC LIMIT 3;",What are the top 3 transaction statuses by number of transactions? Return the status and number of transactions.
broker,sqlite,basic_group_order_limit,"SELECT sbCustCountry, COUNT(*) AS num_customers FROM sbCustomer GROUP BY sbCustCountry ORDER BY CASE WHEN num_customers IS NULL THEN 1 ELSE 0 END DESC, num_customers DESC LIMIT 5;",What are the top 5 countries by number of customers? Return the country name and number of customers.
broker,sqlite,basic_left_join,"SELECT c.sbCustId, c.sbCustName FROM sbCustomer AS c LEFT JOIN sbTransaction AS t ON c.sbCustId = t.sbTxCustId WHERE t.sbTxCustId IS NULL;",Return the customer ID and name of customers who have not made any transactions.
broker,sqlite,basic_left_join,"SELECT tk.sbTickerId, tk.sbTickerSymbol FROM sbTicker AS tk LEFT JOIN sbDailyPrice AS dp ON tk.sbTickerId = dp.sbDpTickerId WHERE dp.sbDpTickerId IS NULL;",Return the ticker ID and symbol of tickers that do not have any daily price records.
car_dealership,sqlite,basic_join_date_group_order_limit,"SELECT c.first_name, c.last_name, COUNT(s.id) AS total_sales, SUM(s.sale_price) AS total_revenue FROM sales AS s JOIN salespersons AS c ON s.salesperson_id = c.id WHERE s.sale_date >= DATE('now', '-3 months') GROUP BY c.first_name, c.last_name ORDER BY total_revenue DESC LIMIT 3;","Who were the top 3 sales representatives by total revenue in the past 3 months, inclusive of today's date? Return their first name, last name, total number of sales and total revenue. Note that revenue refers to the sum of sale_price in the sales table."
car_dealership,sqlite,basic_join_date_group_order_limit,"SELECT sp.first_name, sp.last_name, COUNT(s.id) AS total_sales, SUM(s.sale_price) AS total_revenue FROM sales AS s JOIN salespersons AS sp ON s.salesperson_id = sp.id WHERE s.sale_date >= DATE('now', '-30 days') GROUP BY sp.first_name, sp.last_name, sp.id ORDER BY total_sales DESC LIMIT 5;","Return the top 5 salespersons by number of sales in the past 30 days? Return their first and last name, total sales count and total revenue amount."
car_dealership,sqlite,basic_join_group_order_limit,"SELECT c.state, COUNT(DISTINCT s.customer_id) AS unique_customers, SUM(s.sale_price) AS total_revenue FROM sales AS s JOIN customers AS c ON s.customer_id = c.id GROUP BY c.state ORDER BY CASE WHEN total_revenue IS NULL THEN 1 ELSE 0 END DESC, total_revenue DESC LIMIT 5;","Return the top 5 states by total revenue, showing the number of unique customers and total revenue (based on sale price) for each state."
car_dealership,sqlite,basic_join_group_order_limit,"SELECT c.make, c.model, COUNT(s.id) AS total_sales, SUM(s.sale_price) AS total_revenue FROM sales AS s JOIN cars AS c ON s.car_id = c.id GROUP BY c.make, c.model ORDER BY CASE WHEN total_revenue IS NULL THEN 1 ELSE 0 END DESC, total_revenue DESC LIMIT 5;","What are the top 5 best selling car models by total revenue? Return the make, model, total number of sales and total revenue."
car_dealership,sqlite,basic_join_distinct,SELECT DISTINCT c.id AS customer_id FROM customers AS c JOIN sales AS s ON c.id = s.customer_id;,"Return the distinct list of customer IDs that have made a purchase, based on joining the customers and sales tables."
car_dealership,sqlite,basic_join_distinct,SELECT DISTINCT s.id AS salesperson_id FROM salespersons AS s JOIN sales AS sa ON s.id = sa.salesperson_id JOIN payments_received AS p ON sa.id = p.sale_id WHERE p.payment_method = 'cash';,"Return the distinct list of salesperson IDs that have received a cash payment, based on joining the salespersons, sales and payments_received tables."
car_dealership,sqlite,basic_group_order_limit,"SELECT payment_method, COUNT(*) AS total_payments, SUM(payment_amount) AS total_amount FROM payments_received GROUP BY payment_method ORDER BY CASE WHEN total_amount IS NULL THEN 1 ELSE 0 END DESC, total_amount DESC LIMIT 3;","What are the top 3 payment methods by total payment amount received? Return the payment method, total number of payments and total amount."
car_dealership,sqlite,basic_group_order_limit,"SELECT state, COUNT(*) AS total_signups FROM customers GROUP BY state ORDER BY CASE WHEN total_signups IS NULL THEN 1 ELSE 0 END DESC, total_signups DESC LIMIT 2;","What are the total number of customer signups for the top 2 states? Return the state and total signups, starting from the top."
car_dealership,sqlite,basic_left_join,"SELECT c.id AS car_id, c.make, c.model, c.year FROM cars AS c LEFT JOIN sales AS s ON c.id = s.car_id WHERE s.car_id IS NULL;","Return the car ID, make, model and year for cars that have no sales records, by doing a left join from the cars to sales table."
car_dealership,sqlite,basic_left_join,"SELECT s.id AS salesperson_id, s.first_name, s.last_name FROM salespersons AS s LEFT JOIN sales AS sa ON s.id = sa.salesperson_id WHERE sa.salesperson_id IS NULL;","Return the salesperson ID, first name and last name for salespersons that have no sales records, by doing a left join from the salespersons to sales table."
derm_treatment,sqlite,basic_join_date_group_order_limit,"SELECT d.specialty, COUNT(*) AS num_treatments, SUM(t.tot_drug_amt) AS total_drug_amt FROM treatments AS t JOIN doctors AS d ON t.doc_id = d.doc_id WHERE t.start_dt >= DATE('now', '-6 months') GROUP BY d.specialty ORDER BY total_drug_amt DESC LIMIT 3;","What are the top 3 doctor specialties by total drug amount prescribed for treatments started in the past 6 calendar months? Return the specialty, number of treatments, and total drug amount."
derm_treatment,sqlite,basic_join_date_group_order_limit,"SELECT p.ins_type, COUNT(DISTINCT t.patient_id) AS num_patients, AVG(o.day100_pasi_score) AS avg_pasi_score FROM treatments AS t JOIN patients AS p ON t.patient_id = p.patient_id JOIN outcomes AS o ON t.treatment_id = o.treatment_id WHERE t.end_dt BETWEEN '2022-01-01' AND '2022-12-31' GROUP BY p.ins_type ORDER BY CASE WHEN avg_pasi_score IS NULL THEN 1 ELSE 0 END, avg_pasi_score LIMIT 5;","For treatments that ended in the year 2022 (from Jan 1st to Dec 31st inclusive), what is the average PASI score at day 100 and number of distinct patients per insurance type? Return the top 5 insurance types sorted by lowest average PASI score first."
derm_treatment,sqlite,basic_join_group_order_limit,"SELECT d.drug_name, COUNT(*) AS num_treatments, AVG(t.tot_drug_amt) AS avg_drug_amt FROM treatments AS t JOIN drugs AS d ON t.drug_id = d.drug_id GROUP BY d.drug_name ORDER BY CASE WHEN num_treatments IS NULL THEN 1 ELSE 0 END DESC, num_treatments DESC, CASE WHEN avg_drug_amt IS NULL THEN 1 ELSE 0 END DESC, avg_drug_amt DESC LIMIT 5;","What are the top 5 drugs by number of treatments and average drug amount per treatment? Return the drug name, number of treatments, and average drug amount."
derm_treatment,sqlite,basic_join_group_order_limit,"SELECT di.diag_name, COUNT(DISTINCT t.patient_id) AS num_patients, MAX(o.day100_itch_vas) AS max_itch_score FROM treatments AS t JOIN diagnoses AS di ON t.diag_id = di.diag_id JOIN outcomes AS o ON t.treatment_id = o.treatment_id GROUP BY di.diag_name ORDER BY CASE WHEN max_itch_score IS NULL THEN 1 ELSE 0 END DESC, max_itch_score DESC, CASE WHEN num_patients IS NULL THEN 1 ELSE 0 END DESC, num_patients DESC LIMIT 3;","What are the top 3 diagnoses by maximum itch VAS score at day 100 and number of distinct patients? Return the diagnosis name, number of patients, and maximum itch score."
derm_treatment,sqlite,basic_join_distinct,"SELECT DISTINCT d.doc_id, d.first_name, d.last_name FROM treatments AS t JOIN doctors AS d ON t.doc_id = d.doc_id;","Return the distinct list of doctor IDs, first names and last names that have prescribed treatments."
derm_treatment,sqlite,basic_join_distinct,"SELECT DISTINCT p.patient_id, p.first_name, p.last_name FROM outcomes AS o JOIN treatments AS t ON o.treatment_id = t.treatment_id JOIN patients AS p ON t.patient_id = p.patient_id;","Return the distinct list of patient IDs, first names and last names that have outcome assessments."
derm_treatment,sqlite,basic_group_order_limit,"SELECT ins_type, AVG(height_cm) AS avg_height, AVG(weight_kg) AS avg_weight FROM patients GROUP BY ins_type ORDER BY CASE WHEN avg_height IS NULL THEN 1 ELSE 0 END DESC, avg_height DESC LIMIT 3;","What are the top 3 insurance types by average patient height in cm? Return the insurance type, average height and average weight."
derm_treatment,sqlite,basic_group_order_limit,"SELECT specialty, COUNT(*) AS num_doctors FROM doctors GROUP BY specialty ORDER BY CASE WHEN num_doctors IS NULL THEN 1 ELSE 0 END DESC, num_doctors DESC LIMIT 2;",What are the top 2 specialties by number of doctors? Return the specialty and number of doctors.
derm_treatment,sqlite,basic_left_join,"SELECT p.patient_id, p.first_name, p.last_name FROM patients AS p LEFT JOIN treatments AS t ON p.patient_id = t.patient_id WHERE t.patient_id IS NULL;","Return the patient IDs, first names and last names of patients who have not received any treatments."
derm_treatment,sqlite,basic_left_join,"SELECT d.drug_id, d.drug_name FROM drugs AS d LEFT JOIN treatments AS t ON d.drug_id = t.drug_id WHERE t.drug_id IS NULL;",Return the drug IDs and names of drugs that have not been used in any treatments.
ewallet,sqlite,basic_join_date_group_order_limit,"SELECT m.name AS merchant_name, COUNT(t.txid) AS total_transactions, SUM(t.amount) AS total_amount FROM merchants AS m JOIN wallet_transactions_daily AS t ON m.mid = t.receiver_id WHERE t.receiver_type = 1 AND t.created_at >= DATE('now', '-150 days') GROUP BY m.name ORDER BY total_amount DESC LIMIT 2;","Who are the top 2 merchants (receiver type 1) by total transaction amount in the past 150 days (inclusive of 150 days ago)? Return the merchant name, total number of transactions, and total transaction amount."
ewallet,sqlite,basic_join_date_group_order_limit,"SELECT strftime('%Y-%m', t.created_at) AS month, COUNT(DISTINCT t.sender_id) AS active_users FROM wallet_transactions_daily AS t JOIN users AS u ON t.sender_id = u.uid WHERE t.sender_type = 0 AND t.status = 'success' AND u.status = 'active' AND t.created_at >= '2023-01-01' AND t.created_at < '2024-01-01' GROUP BY month ORDER BY month;","How many distinct active users sent money per month in 2023? Return the number of active users per month (as a date), starting from the earliest date. Do not include merchants in the query. Only include successful transactions."
ewallet,sqlite,basic_join_group_order_limit,"SELECT c.code AS coupon_code, COUNT(t.txid) AS redemption_count, SUM(t.amount) AS total_discount FROM coupons AS c JOIN wallet_transactions_daily AS t ON c.cid = t.coupon_id GROUP BY c.code ORDER BY CASE WHEN redemption_count IS NULL THEN 1 ELSE 0 END DESC, redemption_count DESC LIMIT 3;","What are the top 3 most frequently used coupon codes? Return the coupon code, total number of redemptions, and total amount redeemed."
ewallet,sqlite,basic_join_group_order_limit,"SELECT u.country, COUNT(DISTINCT t.sender_id) AS user_count, SUM(t.amount) AS total_amount FROM users AS u JOIN wallet_transactions_daily AS t ON u.uid = t.sender_id WHERE t.sender_type = 0 GROUP BY u.country ORDER BY CASE WHEN total_amount IS NULL THEN 1 ELSE 0 END DESC, total_amount DESC LIMIT 5;","Which are the top 5 countries by total transaction amount sent by users, sender_type = 0? Return the country, number of distinct users who sent, and total transaction amount."
ewallet,sqlite,basic_join_distinct,SELECT DISTINCT m.mid AS merchant_id FROM merchants AS m JOIN wallet_transactions_daily AS t ON m.mid = t.receiver_id WHERE t.receiver_type = 1;,"Return the distinct list of merchant IDs that have received money from a transaction. Consider all transaction types in the results you return, but only include the merchant ids in your final answer."
ewallet,sqlite,basic_join_distinct,SELECT DISTINCT user_id FROM notifications WHERE type = 'transaction';,Return the distinct list of user IDs who have received transaction notifications.
ewallet,sqlite,basic_group_order_limit,"SELECT status, COUNT(*) AS COUNT FROM wallet_transactions_daily GROUP BY status ORDER BY CASE WHEN COUNT IS NULL THEN 1 ELSE 0 END DESC, COUNT DESC LIMIT 3;",What are the top 3 most common transaction statuses and their respective counts?
ewallet,sqlite,basic_group_order_limit,"SELECT device_type, COUNT(*) AS COUNT FROM user_sessions GROUP BY device_type ORDER BY CASE WHEN COUNT IS NULL THEN 1 ELSE 0 END DESC, COUNT DESC LIMIT 2;",What are the top 2 most frequently used device types for user sessions and their respective counts?
ewallet,sqlite,basic_left_join,"SELECT u.uid, u.username FROM users AS u LEFT JOIN notifications AS n ON u.uid = n.user_id WHERE n.id IS NULL;",Return users (user ID and username) who have not received any notifications
ewallet,sqlite,basic_left_join,"SELECT m.mid AS merchant_id, m.name AS merchant_name FROM merchants AS m LEFT JOIN coupons AS c ON m.mid = c.merchant_id WHERE c.cid IS NULL;",Return merchants (merchant ID and name) who have not issued any coupons.
