##########           LIQUIBASE FLOW FILE               ##########
##########  learn more http://docs.liquibase.com/flow  ##########

## NOTE: This is an advanced example flowfile, compared to the other sample at examples/liquibase.flowfile.yaml
#### HOW TO USE THIS FILE:
#### example for CLI: liquibase flow --flow-file=liquibase.advanced.flowfile.yaml
#### example for ENV Var: LIQUIBASE_FLOW_FLOW_FILE=liquibase.advanced.flowfile.yaml

## Advanced options show in this file include:
#### non-default name of 'liquibase.advanced.flowfile.yaml' (use by setting flowfile property to this name)
#### use of 'include' to inject namespaced yaml files of key: val variables
#### use of globalVariables and stageVariables
#### use of globalArgs and cmdArgs
#### use of property substitution
#### use of a nested flowfile (in this case in the endStage, but could be elsewhere)
#### use of if: conditional which allows a -type: shell or -type: liquibase command to run
###### In the example below, we set an environment variable LIQUIBASE_CURRENT_TARGET, such as 'export LIQUIBASE_CURRENT_TARGET=dev'
###### This could be determined dynamically, of course, from the build tools, bu tthis is simpler for this example "if:" conditional
#### use of shell commands in a -type: shell block.
######  command: bash -c "the shell command || and its chained commands && go in the quotes"
########
#### POTENTIAL use of environment variables:
######  DATETIME STAMP
######## In this file, you could replace ${FLOWVARS.THISDATE} with an env var, such as ${LIQUIBASE_THISDATE} set via .bash_profile
######## for example 'export LIQUIBASE_THISDATE=$( date +'%Y-%m-%dT%H-%M-%S' )'


## Bring in and namespace an external file with yaml 'key: val' pairs for use in this file
## The variables will be used as ${namespace.variablename}, seen in this example as ${FLOWVARS.PROJNAME}
include:
  FLOWVARS: liquibase.flowvariables.yaml



## Set up some global variables for property substitution in ANY stage
globalVariables:
  DIRNAME: "./${FLOWVARS.PROJNAME}_${FLOWVARS.THISDATE}"
  STATUSFILE: "status.txt"
  UPDATELOG: "update.log"
  HISTORYFILE: "history.txt"


## Start of the stages.
stages:

  ## A prep stage. There can be more than one stage if desired.
  stage-prep:

    actions:

      - type: shell
        command: bash -c "mkdir -p ${DIRNAME}"


  ## Another stage.
  stage-dowork:

    ## set up vars for property substitution in THIS stage only
    stageVariables:

      VERBOSESTATE: TRUE


    actions:
      #
      # Do a validate command
      #
      - type: liquibase
        command: validate

      #
      # Tell me what is pending a deployment
      #
      - type: shell
        command: bash -c "liquibase --show-banner false --outputfile ./${DIRNAME}/${STATUSFILE} status --verbose ${VERBOSESTATE}"

          # This is the structured way to setup a liquibase command, if you dont want to run it as one 'bash -c' command
          #- type: liquibase
          #  command: status
          #  globalArgs:
          #    outputfile: "${DIRNAME}/${STATUSFILE}"
          #    showbanner: false
          #  cmdArgs: {verbose: "${VERBOSESTATE}"




      #
      # And then save a version in detail, if env var LIQUIBASE_FILE_OUTPUT == 1
      #
      - type: shell
        command: bash -c "echo 'LIQUIBASE_ env vars ' && env | grep 'LIQUIBASE_' "

      - type: liquibase
        ## if this var LIQUIBASE_CURRENT_TARGET is "dev", then the updatesql will run
        if: "${LIQUIBASE_CURRENT_TARGET} == dev"
        command: updatesql
        globalArgs: {outputfile: "${DIRNAME}/${UPDATELOG}"}

      - type: shell
        ## if this var LIQUIBASE_CURRENT_TARGET is not "dev", then the message will be displayed
        if: "${LIQUIBASE_CURRENT_TARGET} != dev"
        command: echo "No output files created. Set env var LIQUIBASE_CURRENT_TARGET to dev to trigger file creation."



      #
      # Quality Checks for changelog
      #
      - type: liquibase
        command: checks run
        cmdArgs: {checks-scope: changelog}

      #
      # Run update
      #
      - type: liquibase
        command: update


      #
      # Quality Checks for database
      #
      - type: liquibase
        command: checks run
        cmdArgs: {checks-scope: database}


      #
      # Create a history file
      #
      - type: liquibase
        command: history
        globalArgs: {outputfile: "${DIRNAME}/${HISTORYFILE}"}



## The endStage ALWAYS RUNS.
## So put actions here which you desire to perform whether previous stages' actions succeed or fail.
## If you do not want any actions to ALWAYS RUN, simply delete the endStage from your flow file.

endStage:
  actions:
    - type: liquibase
      ## Notice this is a flow command in a flow file, and it called a 'nested' flowfile, which in this case lives in the same dir, but could be elsewhere
      command: flow
      cmdArgs: {flowfile: liquibase.endstage.flow}
