import { getAccessToken, withApi<PERSON>uthRequired } from '@auth0/nextjs-auth0'
import { NextApiRequest, NextApiResponse } from 'next'
import { isDevMode, DEV_TOKEN } from '@/middleware/dev-auth'

export default async (req: NextApiRequest, res: NextApiResponse) => {
  // Development mode bypass
  if (isDevMode()) {
    console.log('Development mode: returning dev token')
    res.status(200).json(DEV_TOKEN)
    return
  }

  // Production mode with Auth0
  return withApiAuthRequired(
    async (req: NextApiRequest, res: NextApiResponse) => {
      try {
        const { accessToken } = await getAccessToken(req, res)
        res.status(200).json(accessToken)
      } catch (error) {
        console.error('Error fetching access token:', error)

        // Redirect user to logout
        res.writeHead(302, {
          Location: '/api/auth/logout',
        })
        res.end()
      }
    },
  )(req, res)
}
