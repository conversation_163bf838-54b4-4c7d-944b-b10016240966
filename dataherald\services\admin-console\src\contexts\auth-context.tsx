import { UserProvider, useUser } from '@auth0/nextjs-auth0/client'
import { useRouter } from 'next/navigation'
import React, {
  ComponentType,
  FC,
  ReactNode,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react'
import { isDevMode, DEV_USER, DEV_TOKEN } from '@/middleware/dev-auth'

interface AuthProviderProps {
  children: ReactNode
}

type WithAuthUser = (
  Component: ComponentType<AuthProviderProps>,
) => React.FC<AuthProviderProps>

const withAuthUser: WithAuthUser = (Component) => {
  return function WithAuthUser(props: AuthProviderProps): JSX.Element {
    return (
      <UserProvider>
        <Component {...props} />
      </UserProvider>
    )
  }
}

interface AuthContextType {
  token: string | null
  fetchToken: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const AuthProvider: FC<AuthProviderProps> = withAuthUser(
  ({ children }) => {
    const router = useRouter()
    const { user: sessionUser } = useUser()
    const [token, setToken] = useState<string | null>(null)

    const fetchToken = useCallback(async () => {
      try {
        // In development mode, use dev token directly
        if (isDevMode()) {
          console.log('Development mode: using dev token')
          setToken(DEV_TOKEN)
          return
        }

        const response = await fetch('/api/auth/token')
        const token: string = await response.json()
        setToken(token)
      } catch (error) {
        console.error(`Fetching token failed, redirecting to logout: ${error}`)
        if (!isDevMode()) {
          router.push('/api/auth/logout')
        }
      }
    }, [router])

    useEffect(() => {
      if (isDevMode()) {
        // In development mode, always set the token
        fetchToken()
      } else {
        // In production mode, only fetch token if user is authenticated
        !!sessionUser && fetchToken()
      }
    }, [fetchToken, sessionUser])

    return (
      <AuthContext.Provider value={{ token, fetchToken }}>
        {children}
      </AuthContext.Provider>
    )
  },
)

// Custom hook that works with both Auth0 and development mode
export const useAuthUser = () => {
  const { user: auth0User, isLoading, error } = useUser()

  if (isDevMode()) {
    return {
      user: DEV_USER,
      isLoading: false,
      error: null,
    }
  }

  return {
    user: auth0User,
    isLoading,
    error,
  }
}

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
