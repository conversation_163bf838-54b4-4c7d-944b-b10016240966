#!/usr/bin/env python3
"""
DataHerald Text-to-SQL Evaluation Script
Using sql-eval benchmarks and car dealership database
"""

import requests
import json
import sqlite3
import pandas as pd
import time
from typing import Dict, List, Tuple

class DataHeraldEvaluator:
    def __init__(self, engine_url="http://localhost:8080", db_connection_id="683dee1411e478dbd4494fd4"):
        self.engine_url = engine_url
        self.db_connection_id = db_connection_id
        self.db_path = "dataherald/services/engine/dataherald/car_dealership.db"
        
    def test_database_connection(self) -> bool:
        """Test if the database is accessible"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            conn.close()
            print(f"✅ Database accessible. Tables: {', '.join(tables)}")
            return True
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False
    
    def test_engine_api(self) -> bool:
        """Test if DataHerald engine is accessible"""
        try:
            response = requests.get(f"{self.engine_url}/api/v1/heartbeat")
            if response.status_code == 200:
                print("✅ DataHerald engine is running")
                return True
            else:
                print(f"❌ Engine heartbeat failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Engine connection failed: {e}")
            return False
    
    def execute_sql_directly(self, sql: str) -> List[Tuple]:
        """Execute SQL directly on the database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(sql)
            results = cursor.fetchall()
            conn.close()
            return results
        except Exception as e:
            print(f"❌ SQL execution failed: {e}")
            return []
    
    def create_prompt(self, question: str) -> str:
        """Create a prompt in DataHerald"""
        try:
            payload = {
                "text": question,
                "db_connection_id": self.db_connection_id,
                "schemas": []
            }
            response = requests.post(
                f"{self.engine_url}/api/v1/prompts",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            if response.status_code == 201:
                prompt_data = response.json()
                print(f"✅ Prompt created: {prompt_data['id']}")
                return prompt_data['id']
            else:
                print(f"❌ Prompt creation failed: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"❌ Prompt creation error: {e}")
            return None
    
    def generate_sql_with_manual_schema(self, question: str) -> str:
        """Try to generate SQL by providing manual schema context"""
        schema_context = """
        Database Schema:
        - cars (id, make, model, year, color, price, mileage, condition, vin)
        - customers (id, first_name, last_name, email, phone, address, city, state, zip_code)
        - salespersons (id, first_name, last_name, email, phone, hire_date, commission_rate)
        - sales (id, customer_id, car_id, salesperson_id, sale_date, sale_price, financing_type)
        - payments_received (id, sale_id, payment_date, payment_amount, payment_method)
        
        Question: {question}
        
        Generate SQL query for the above question using the provided schema.
        """
        
        enhanced_question = schema_context.format(question=question)
        
        try:
            payload = {
                "prompt": {
                    "text": enhanced_question,
                    "db_connection_id": self.db_connection_id,
                    "schemas": []
                },
                "llm_config": {
                    "llm_name": "gemini-pro"
                },
                "evaluate": False
            }
            
            response = requests.post(
                f"{self.engine_url}/api/v1/prompts/sql-generations",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=60
            )
            
            if response.status_code == 201:
                result = response.json()
                print(f"✅ SQL generated successfully")
                return result.get('sql', '')
            else:
                print(f"❌ SQL generation failed: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ SQL generation error: {e}")
            return None
    
    def run_evaluation_tests(self):
        """Run comprehensive evaluation tests"""
        print("🎯 Starting DataHerald Evaluation")
        print("=" * 50)
        
        # Test 1: System connectivity
        print("\n📋 Test 1: System Connectivity")
        db_ok = self.test_database_connection()
        engine_ok = self.test_engine_api()
        
        if not (db_ok and engine_ok):
            print("❌ System connectivity failed. Cannot proceed with evaluation.")
            return
        
        # Test 2: SQL-Eval benchmark questions
        print("\n📋 Test 2: SQL-Eval Benchmark Questions")
        
        test_questions = [
            {
                "question": "What are the top 5 best selling car models by total revenue?",
                "expected_sql": """SELECT c.make, c.model, COUNT(s.id) AS total_sales, SUM(s.sale_price) AS total_revenue 
                                  FROM sales AS s 
                                  JOIN cars AS c ON s.car_id = c.id 
                                  GROUP BY c.make, c.model 
                                  ORDER BY total_revenue DESC 
                                  LIMIT 5;"""
            },
            {
                "question": "Who were the top 3 sales representatives by total revenue?",
                "expected_sql": """SELECT sp.first_name, sp.last_name, COUNT(s.id) AS total_sales, SUM(s.sale_price) AS total_revenue 
                                  FROM sales AS s 
                                  JOIN salespersons AS sp ON s.salesperson_id = sp.id 
                                  GROUP BY sp.first_name, sp.last_name 
                                  ORDER BY total_revenue DESC 
                                  LIMIT 3;"""
            },
            {
                "question": "Return the top 5 states by total revenue",
                "expected_sql": """SELECT c.state, COUNT(DISTINCT s.customer_id) AS unique_customers, SUM(s.sale_price) AS total_revenue 
                                  FROM sales AS s 
                                  JOIN customers AS c ON s.customer_id = c.id 
                                  GROUP BY c.state 
                                  ORDER BY total_revenue DESC 
                                  LIMIT 5;"""
            }
        ]
        
        results = []
        
        for i, test in enumerate(test_questions, 1):
            print(f"\n🔍 Test Question {i}: {test['question']}")
            
            # Execute expected SQL
            print("📊 Executing expected SQL...")
            expected_results = self.execute_sql_directly(test['expected_sql'])
            print(f"Expected results: {len(expected_results)} rows")
            if expected_results:
                print(f"Sample: {expected_results[0]}")
            
            # Try to generate SQL with DataHerald
            print("🤖 Generating SQL with DataHerald...")
            generated_sql = self.generate_sql_with_manual_schema(test['question'])
            
            if generated_sql:
                print(f"Generated SQL: {generated_sql}")
                
                # Execute generated SQL
                generated_results = self.execute_sql_directly(generated_sql)
                print(f"Generated results: {len(generated_results)} rows")
                
                # Compare results
                results_match = expected_results == generated_results
                print(f"Results match: {'✅' if results_match else '❌'}")
                
                results.append({
                    'question': test['question'],
                    'expected_sql': test['expected_sql'],
                    'generated_sql': generated_sql,
                    'results_match': results_match,
                    'expected_rows': len(expected_results),
                    'generated_rows': len(generated_results)
                })
            else:
                print("❌ Failed to generate SQL")
                results.append({
                    'question': test['question'],
                    'expected_sql': test['expected_sql'],
                    'generated_sql': None,
                    'results_match': False,
                    'expected_rows': len(expected_results),
                    'generated_rows': 0
                })
        
        # Summary
        print("\n📊 Evaluation Summary")
        print("=" * 50)
        successful_generations = sum(1 for r in results if r['generated_sql'] is not None)
        matching_results = sum(1 for r in results if r['results_match'])
        
        print(f"Total questions: {len(test_questions)}")
        print(f"Successful SQL generations: {successful_generations}/{len(test_questions)}")
        print(f"Matching results: {matching_results}/{len(test_questions)}")
        print(f"Success rate: {(successful_generations/len(test_questions)*100):.1f}%")
        print(f"Accuracy rate: {(matching_results/len(test_questions)*100):.1f}%")
        
        return results

def main():
    evaluator = DataHeraldEvaluator()
    results = evaluator.run_evaluation_tests()
    
    # Save results
    if results:
        with open('dataherald_evaluation_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\n💾 Results saved to dataherald_evaluation_results.json")

if __name__ == "__main__":
    main()
