question,query,db_name,query_category,instructions
"Which authors have written publications in both the domain ""Machine Learning"" and the domain ""Data Science""?","SELECT {author.name,author.aid} FROM author WHERE author.aid IN (SELECT domain_author.aid FROM domain_author WHERE domain_author.did IN (SELECT domain.did FROM DOMAIN WHERE domain.name IN ('Machine Learning', 'Data Science') ) GROUP BY 1 HAVING COUNT(DISTINCT domain_author.did) = 2);",academic,group_by,
What is the total number of citations received by each author?,"SELECT {author.name, author.aid}, sum(publication.citation_num) AS total_citations FROM author J<PERSON><PERSON> writes ON author.aid = writes.aid JOIN publication ON writes.pid = publication.pid GROUP BY {} ORDER BY total_citations DESC NULLS LAST;SELECT {a.aid, a.name}, COUNT(c.cited) AS total_citations FROM author a <PERSON><PERSON><PERSON> writes w ON a.aid = w.aid JOIN publication p ON w.pid = p.pid JOIN cite c ON p.pid = c.cited GROUP BY {} ORDER BY total_citations DESC;SELECT {a.aid, a.name}, COALESCE(SUM(p.citation_num), 0) AS total_citations FROM author a LEFT JOIN writes w ON a.aid = w.aid LEFT JOIN publication p ON w.pid = p.pid GROUP BY {};SELECT {a.aid, a.name}, COALESCE(SUM(p.citation_num), 0) AS total_citations FROM author a LEFT JOIN writes w ON a.aid = w.aid LEFT JOIN publication p ON w.pid = p.pid GROUP BY {};",academic,group_by,
What is the total number of publications published in each year?,"SELECT publication.year, COUNT(DISTINCT publication.pid) AS total_publications FROM publication GROUP BY publication.year ORDER BY publication.year;",academic,group_by,
What is the average number of references cited by publications in each domain name?,"SELECT {domain.name,domain.did}, AVG(publication.reference_num) AS average_references FROM domain_publication JOIN publication ON domain_publication.pid = publication.pid JOIN domain ON domain.did = domain_publication.did GROUP BY {};",academic,group_by,
What is the average number of citations received by publications in each year?,"SELECT publication.year, AVG(publication.citation_num) AS average_citations FROM publication GROUP BY publication.year ORDER BY publication.year NULLS LAST;",academic,group_by,
What is the title of the publication that has received the highest number of citations?,SELECT publication.title FROM publication ORDER BY publication.citation_num DESC NULLS LAST LIMIT 1;,academic,order_by,
What are the top 5 domains with the highest number of authors associated with them?,"SELECT {d.name, d.did}, COUNT(DISTINCT a.aid) AS author_count FROM author a JOIN domain_author da ON a.aid = da.aid JOIN domain d ON da.did = d.did GROUP BY {} ORDER BY author_count DESC LIMIT 5;SELECT {d.name, d.did}, COUNT(da.aid) AS author_count FROM DOMAIN AS d JOIN domain_author AS da ON d.did = da.did GROUP BY {} ORDER BY author_count DESC LIMIT 5;",academic,order_by,
"What are the top 3 titles of the publications that have the highest number of references cited, ordered by the number of references cited in descending order?",SELECT publication.title FROM publication ORDER BY publication.reference_num DESC LIMIT 3;,academic,order_by,
What are the top 3 publications with the highest number of citations?,"SELECT {publication.title, publication.pid}, publication.citation_num FROM publication ORDER BY publication.citation_num DESC LIMIT 3;",academic,order_by,
What are the titles of all publications ordered alphabetically?,SELECT DISTINCT publication.title FROM publication ORDER BY publication.title ASC NULLS LAST;,academic,order_by,
What is the ratio of publications to authors in the database?,"SELECT CAST(COUNT(DISTINCT publication.pid) AS FLOAT) / NULLIF(COUNT(DISTINCT author.aid), 0) AS publication_to_author_ratio FROM publication, author;",academic,ratio,
What is the ratio of publications presented in conferences to publications published in journals?,"SELECT CAST(COUNT(DISTINCT CASE WHEN cid IS NOT NULL THEN pid END) AS FLOAT) / NULLIF(COUNT(DISTINCT CASE WHEN jid IS NOT NULL THEN pid END), 0) AS ratio FROM publication;",academic,ratio,
What is the ratio of the total number of publications to the total number of keywords within each domain ID? Show all domain IDs.,"SELECT domain_publication.did, CAST(COUNT(DISTINCT domain_publication.pid) AS FLOAT) / NULLIF(COUNT(DISTINCT domain_keyword.kid), 0) AS publication_to_keyword_ratio FROM domain_publication LEFT JOIN domain_keyword ON domain_publication.did = domain_keyword.did GROUP BY domain_publication.did ORDER BY publication_to_keyword_ratio DESC NULLS LAST;SELECT domain_publication.did, CAST(COUNT(DISTINCT domain_publication.pid) AS FLOAT) / NULLIF(COUNT(DISTINCT domain_keyword.kid), 0) AS publication_to_keyword_ratio FROM domain_keyword LEFT JOIN domain_publication ON domain_publication.did = domain_keyword.did GROUP BY domain_publication.did ORDER BY publication_to_keyword_ratio DESC NULLS LAST;SELECT d.did, COALESCE(CAST(COUNT(DISTINCT dp.pid) AS FLOAT) / NULLIF(COUNT(DISTINCT dk.kid), 0), 0) AS publication_to_keyword_ratio FROM domain d LEFT JOIN domain_publication dp ON d.did = dp.did LEFT JOIN domain_keyword dk ON d.did = dk.did GROUP BY d.did ORDER BY publication_to_keyword_ratio DESC NULLS LAST;",academic,ratio,
How does the ratio of publications to journals change over the years? Return the annual numbers of publications and journals as well.,"SELECT publication.year, COUNT(DISTINCT publication.pid) AS num_publications, COUNT(DISTINCT publication.jid) AS num_journals, CAST(COUNT(DISTINCT publication.pid) AS FLOAT) / NULLIF(COUNT(DISTINCT publication.jid), 0) AS ratio FROM publication GROUP BY publication.year ORDER BY publication.year;",academic,ratio,
How does the ratio of authors to organizations differ by continent?,"SELECT organization.continent, COUNT(DISTINCT author.aid)::float / NULLIF(COUNT(DISTINCT organization.oid), 0) AS ratio FROM organization LEFT JOIN author ON author.oid = organization.oid GROUP BY organization.continent ORDER BY ratio DESC NULLS LAST;WITH author_counts AS (SELECT o.continent, COUNT(DISTINCT a.aid) AS author_count FROM author AS a JOIN organization AS o ON a.oid = o.oid GROUP BY o.continent), organization_counts AS (SELECT o.continent, COUNT(DISTINCT o.oid) AS organization_count FROM organization AS o GROUP BY o.continent) SELECT ac.continent, CAST(ac.author_count AS FLOAT) / NULLIF(oc.organization_count, 0) AS author_to_organization_ratio FROM author_counts AS ac JOIN organization_counts AS oc ON ac.continent = oc.continent ORDER BY author_to_organization_ratio DESC;",academic,ratio,
Which author had the most publications in the year 2021 and how many publications did he/she have that year?,"SELECT {author.name, author.aid}, COUNT(publication.pid) AS publication_count FROM writes JOIN author ON writes.aid = author.aid JOIN publication ON writes.pid = publication.pid WHERE publication.year = 2021 GROUP BY {} ORDER BY publication_count DESC NULLS LAST LIMIT 1;",academic,table_join,
What is the total number of publications presented in each conference?,"SELECT {conference.name, conference.cid}, COUNT(publication.pid) AS total_publications FROM publication JOIN conference ON publication.cid = conference.cid GROUP BY {} ORDER BY total_publications DESC;",academic,table_join,
"What is the total number of publications in each journal, ordered by the number of publications in descending order?","SELECT {journal.name, journal.jid}, COUNT(publication.pid) AS total_publications FROM publication JOIN journal ON publication.jid=journal.jid GROUP BY {} ORDER BY total_publications DESC NULLS LAST;SELECT {journal.name, journal.jid}, COUNT(publication.pid) AS total_publications FROM journal LEFT JOIN publication ON journal.jid=publication.jid GROUP BY {} ORDER BY total_publications DESC NULLS LAST;",academic,table_join,
"How many publications were presented at each conference, ordered by the number of publications in descending order? Give the names of the conferences and their corresponding number of publications.","SELECT conference.name, COUNT(publication.pid) AS num_publications FROM publication JOIN conference ON publication.cid=conference.cid GROUP BY conference.name, conference.cid ORDER BY num_publications DESC NULLS LAST;",academic,table_join,
"How many publications were published in journals whose names start with the letter ""J""?",SELECT count(DISTINCT publication.pid) FROM publication JOIN journal ON publication.jid = journal.jid WHERE journal.name ilike 'J%';,academic,table_join,
"Which organizations have authors who have written publications in the domain ""Machine Learning""?","SELECT DISTINCT {organization.name, organization.oid} FROM organization JOIN author ON organization.oid = author.oid JOIN writes ON author.aid = writes.aid JOIN domain_publication ON writes.pid = domain_publication.pid JOIN domain ON domain_publication.did = domain.did WHERE domain.name = 'Machine Learning';",academic,instruct,Always filter names using an exact match
Which authors belong to the same domain as Martin?,"SELECT DISTINCT {a2.name, a2.aid} FROM author a1 JOIN domain_author da1 ON a1.aid = da1.aid JOIN domain_author da2 ON da1.did = da2.did JOIN author a2 ON da2.aid = a2.aid WHERE LOWER(a1.name) ILIKE '%martin%';SELECT DISTINCT a2.name, a2.aid FROM author a1 JOIN domain_author da1 ON a1.aid = da1.aid JOIN domain_author da2 ON da1.did = da2.did JOIN author a2 ON da2.aid = a2.aid WHERE LOWER(a1.name) ILIKE '%martin%' AND a2.name NOT ILIKE '%martin%';",academic,instruct,Always filter names using ILIKE with percent sign wildcards
Which authors are not part of any organization?,"SELECT DISTINCT {name, aid} FROM author WHERE oid IS NULL",academic,instruct,Always filter names using ILIKE
What are the publications written by authors from the 'Sociology' domain and presented at conferences in the year 2020?,"SELECT DISTINCT {publication.title, publication.pid} FROM DOMAIN JOIN domain_author ON domain.did = domain_author.did JOIN writes ON domain_author.aid = writes.aid JOIN publication ON writes.pid = publication.pid JOIN domain_conference ON domain.did = domain_conference.did WHERE domain.name ILIKE '%Sociology%' AND publication.year = 2020 AND publication.cid = domain_conference.cid;",academic,instruct,"To get publications written by authors from a given domain, you would need to join domain, domain_author, author to link the domain to the author first, and then join with write to link with the publication id. Finally, to see which ones were presented at conferences, you must join the domain table with the domain_conference table. You must also filter names using ILIKE."
"What are the names of the authors who have written publications in the domain ""Computer Science""?",SELECT DISTINCT author.name FROM author JOIN writes ON author.aid = writes.aid JOIN publication ON writes.pid = publication.pid JOIN domain_publication ON publication.pid = domain_publication.pid JOIN domain ON domain_publication.did = domain.did WHERE domain.name ilike '%computer%science%';,academic,instruct,"To get publications written by authors from a given domain, you would need to join domain, domain_author, author to link the domain to the author first, and then join with write to link with the publication id. You must also filter names using ILIKE."
"What month were most students admitted? Return the no. of students and the month as a date","SELECT date_trunc('month', s.admit_term) AS month, COUNT(*) AS total_students FROM student s GROUP BY MONTH ORDER BY total_students DESC LIMIT 1;SELECT TO_CHAR(DATE_TRUNC('month', s.admit_term), 'YYYY-MM') AS month, COUNT(*) AS total_students FROM student s GROUP BY month ORDER BY total_students DESC LIMIT 1;SELECT date_trunc('month', admit_term)::date AS MONTH, COUNT(*) AS num_students FROM student GROUP BY MONTH ORDER BY COUNT(*) DESC LIMIT 1;",advising,date_functions,
What's the average predicted time to graduation since admission in no. of days?,SELECT avg(predicted_graduation_semester - admit_term) AS average_predicted_time_to_graduation FROM student;,advising,date_functions,
How many students were predicted to graduate in the last 10 years?,"SELECT count(*) AS num_students_graduated FROM student WHERE predicted_graduation_semester >= DATE_TRUNC('year', CURRENT_DATE) - interval '10 year';",advising,date_functions,
How long has it been in days since the last admitted student? Give the answer as an integer.,SELECT CURRENT_DATE - max(admit_term) AS duration_since_last_admitted_student FROM student;,advising,date_functions,
Return the course id's that are offered in either semesters 1 or 2 and ends before 1pm and had an instructor on thursday,"SELECT DISTINCT co.course_id FROM course_offering co JOIN offering_instructor oi ON co.offering_id = oi.offering_id WHERE (co.semester = 1 OR co.semester = 2) AND co.end_time < '13:00:00' AND co.thursday IS NOT NULL;",advising,date_functions,
What is the total number of students who found the instructor to be hilarious per course id?,"SELECT course_tags_count.course_id, SUM(course_tags_count.hilarious) AS total_hilarious FROM course_tags_count GROUP BY course_tags_count.course_id;",advising,group_by,
What is the average clarity score for each instructor who taught a course?,"SELECT {i.name, i.instructor_id}, AVG(c.clarity_score) FROM course c JOIN course_offering co ON c.course_id = co.course_id JOIN offering_instructor oi ON co.offering_id = oi.offering_id JOIN instructor i ON oi.instructor_id = i.instructor_id GROUP BY {};",advising,group_by,
How many course offerings have a final exam and how many do not?,"SELECT course_offering.has_final_exam, COUNT(offering_id) AS num_courses FROM course_offering GROUP BY course_offering.has_final_exam;SELECT COUNT(CASE WHEN co.has_final_exam THEN 1 END) AS num_with_final_exam, COUNT(CASE WHEN NOT co.has_final_exam THEN 1 END) AS num_without_final_exam FROM course_offering co;",advising,group_by,
How many courses does each department offer?,"SELECT course.department, COUNT(DISTINCT course.course_id) AS num_courses FROM course GROUP BY course.department ORDER BY num_courses DESC NULLS LAST;",advising,group_by,
How many courses are offered for each semester id?,"SELECT course_offering.semester, COUNT(DISTINCT course_offering.course_id) AS num_courses FROM course_offering GROUP BY course_offering.semester ORDER BY course_offering.semester;SELECT semester.semester_id, COUNT(DISTINCT course_offering.course_id) AS num_courses FROM semester LEFT JOIN course_offering ON semester.semester_id = course_offering.semester GROUP BY semester.semester_id;",advising,group_by,
"Which course has the highest number of enrolled students, and what is the enrollment number?","SELECT {course.name, course.course_id, course.number}, course.num_enrolled FROM course ORDER BY course.num_enrolled DESC NULLS LAST LIMIT 1;",advising,order_by,
"What is the total number of students who participated actively for each course id, ordered from highest to lowest participants?","SELECT course_tags_count.course_id, course_tags_count.participation FROM course_tags_count ORDER BY course_tags_count.participation DESC NULLS LAST;",advising,order_by,
"What is the total number of students enrolled in each course, ordered from highest to lowest?","SELECT {course.course_id, course.name, course.number}, SUM(course.num_enrolled) AS total_students FROM course GROUP BY {} ORDER BY total_students DESC NULLS LAST;",advising,order_by,
"What is the total number of credits earned by each student, ordered from highest to lowest? Give the student id and the total number of credits.","SELECT student.student_id, student.total_credit FROM student ORDER BY student.total_credit DESC NULLS LAST;",advising,order_by,
"What is the name of the instructor who has taught the most courses, and how many courses have they taught?","SELECT instructor.name, count(offering_instructor.offering_id) AS num_courses FROM offering_instructor JOIN instructor ON offering_instructor.instructor_id = instructor.instructor_id GROUP BY instructor.name ORDER BY num_courses DESC LIMIT 1;",advising,order_by,
What is the ratio of the total number of students enrolled in courses with exams to the total number of students enrolled in courses without exams?,"SELECT SUM(CASE WHEN c.has_exams THEN c.num_enrolled ELSE 0 END)::FLOAT / SUM(CASE WHEN NOT c.has_exams THEN c.num_enrolled ELSE 0 END) AS ratio FROM course c;",advising,ratio,
What is the ratio of the number of students who found the grading criteria clear and easy to understand to the number of students who received good feedback from the instructor for each course id?,"SELECT course_tags_count.course_id, CAST(course_tags_count.clear_grading AS FLOAT) / NULLIF(course_tags_count.good_feedback, 0) AS ratio FROM course_tags_count ORDER BY course_tags_count.course_id NULLS LAST;",advising,ratio,
What is the ratio of the number of courses with projects to the number of courses with exams in each semester id?,"SELECT course_offering.semester, CAST(SUM(CASE WHEN course.has_projects THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN course.has_exams THEN 1 ELSE 0 END), 0) AS ratio FROM course JOIN course_offering ON course.course_id = course_offering.course_id GROUP BY course_offering.semester ORDER BY course_offering.semester NULLS LAST;",advising,ratio,
What is the ratio of helpfulness scores to clarity scores for each course ID?,"SELECT course.course_id, CAST(course.helpfulness_score AS FLOAT) / NULLIF(course.clarity_score, 0) AS ratio FROM course;",advising,ratio,
How does the ratio of enrolled students to the number of reviews vary across different courses?,"SELECT {course.course_id, course.name, course.number}, CAST(course.num_enrolled AS FLOAT) / NULLIF(course.num_reviews, 0) AS student_review_ratio FROM course ORDER BY student_review_ratio NULLS LAST;",advising,ratio,
Which courses have been taken by students in the Computer Science program?,"SELECT DISTINCT {course.name, course.course_id, course.number} AS course_name FROM student JOIN student_record ON student.student_id = student_record.student_id JOIN program ON student.program_id = program.program_id JOIN course ON student_record.course_id = course.course_id WHERE program.name ILIKE '%Computer Science%';",advising,table_join,
Which courses have a final project and a final exam?,"SELECT DISTINCT {course.name, course.course_id, course.number} FROM course_offering JOIN course ON course_offering.course_id = course.course_id WHERE course_offering.has_final_project = true AND course_offering.has_final_exam = true;",advising,table_join,
What is the total number of students who have taken a course with a final project or exam?,SELECT COUNT(DISTINCT student_record.student_id) AS total_students FROM student_record JOIN course_offering ON student_record.course_id = course_offering.course_id WHERE course_offering.has_final_project = true OR course_offering.has_final_exam = true;,advising,table_join,
What is the total number of credits earned by students in each program?,"SELECT {program.name, program.program_id}, SUM(student.total_credit) AS total_credits FROM student JOIN program ON student.program_id = program.program_id GROUP BY {};",advising,table_join,
How many students have declared a major in each program?,"SELECT {program.name, program.program_id}, COUNT(student.student_id) AS number_of_students FROM student JOIN program ON student.program_id = program.program_id WHERE student.declare_major IS NOT NULL GROUP BY {} ORDER BY number_of_students DESC;",advising,table_join,
Which students have declared a minor program? List their firstname and lastname. Order the results by the students' last names.,"SELECT student.firstname, student.lastname FROM student WHERE student.minor IS NOT NULL ORDER BY student.lastname NULLS LAST;",advising,instruct,"student.declare_major is null for students who have not declared their major.
student.minor is null for students who have not declared a minor program."
What is the average GPA of students in the program mathematics?,SELECT AVG(student.total_gpa) FROM student JOIN program ON student.program_id = program.program_id WHERE LOWER(program.name) = 'mathematics';,advising,instruct,Match strings case-insensitively
What are the names of all the courses offered by the department of Computer Science?,SELECT course.name FROM course WHERE course.department ILIKE '%Computer Science%' ORDER BY course.name ASC NULLS LAST;,advising,instruct,"Filter strings using ILIKE.
Use the student_record table for all information relating to students' choices and their course."
"What are the easiness scores for courses in the ""Computer Science"" department? Show both courses and scores.","SELECT {course.name, course.course_id, course.number}, course.easiness_score FROM course WHERE course.department = 'Computer Science';",advising,instruct,Always filter names using exact string matching
Return the student IDs who have taken an in-person course and have gotten a grade of A or C.,"SELECT DISTINCT student_id FROM student_record WHERE student_record.how = 'in-person' AND student_record.grade IN ('A', 'C');",advising,instruct,"Always filter strings with an exact match.
When asked for specific students or courses, do not return duplicates."
Which flight has the shortest duration between departure and arrival times? Convert to integer minutes.,"SELECT {flight.flight_number, flight.flight_id}, (arrival_time - departure_time) / 60 AS duration_minutes FROM flight ORDER BY duration_minutes LIMIT 1;",atis,date_functions,
"What's the average duration between departure and arrival times minus 34 minutes? Convert from UNIX to regular datetime, and return the answer in minutes",SELECT avg(to_timestamp(arrival_time) - to_timestamp(departure_time) - interval '34 minutes') AS average_duration FROM flight;SELECT AVG(arrival_time - departure_time)/60 - 34 AS average_duration FROM flight;,atis,date_functions,
Count the number of flight departures for each month?,"SELECT month.month_name, count(*) AS departure_count FROM flight JOIN month ON extract(MONTH FROM to_timestamp(flight.departure_time)) = month.month_number GROUP BY month.month_name, month.month_number ORDER BY month.month_number;SELECT date_trunc('month', to_timestamp(flight.departure_time)) AS month, COUNT(*) AS num_departures FROM flight GROUP BY MONTH ORDER BY MONTH;SELECT EXTRACT(MONTH FROM to_timestamp(flight.departure_time)) AS month, COUNT(*) AS num_departures FROM flight GROUP BY month ORDER BY month;SELECT TO_CHAR(TO_TIMESTAMP(flight.departure_time), 'YYYY-MM') AS month, COUNT(*) AS num_departures FROM flight GROUP BY month ORDER BY month;",atis,date_functions,
What's the earliest flight departure time in the day in HH:MM?,"SELECT to_char(to_timestamp(departure_time)::TIME, 'HH24:MI') AS earliest_departure_time FROM flight ORDER BY earliest_departure_time LIMIT 1;",atis,date_functions,
What's the absolute difference in time in days between today and the earliest flight departure? Give the answer as an integer.,"SELECT date_part('day', CURRENT_DATE - to_timestamp(departure_time)) AS difference_in_days FROM flight ORDER BY departure_time LIMIT 1;SELECT (CURRENT_DATE - TO_TIMESTAMP(MIN(f.departure_time))) AS days_difference FROM flight f;SELECT CAST(ABS((EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) - EXTRACT(EPOCH FROM TO_TIMESTAMP(MIN(departure_time)))) / 86400) AS INTEGER) AS diff_in_days FROM flight;",atis,date_functions,
What is the total cost of round-trip fares for each airline code?,"SELECT fare.fare_airline, SUM(fare.round_trip_cost) AS total_round_trip_cost FROM fare GROUP BY fare.fare_airline ORDER BY total_round_trip_cost DESC;",atis,group_by,
"What is the average cost of round-trip fares from Los Angeles (LAX) to Chicago (ORD) for each airline, sorted in descending order by average cost?","SELECT fare.fare_airline, AVG(fare.round_trip_cost) AS average_cost FROM fare WHERE fare.from_airport = 'LAX' AND fare.to_airport = 'ORD' GROUP BY fare.fare_airline ORDER BY average_cost DESC NULLS LAST;SELECT airline.airline_name, AVG(fare.round_trip_cost) AS avg_round_trip_cost FROM fare JOIN airline ON fare.fare_airline = airline.airline_code WHERE fare.from_airport = 'LAX' AND fare.to_airport = 'ORD' GROUP BY airline.airline_name ORDER BY avg_round_trip_cost DESC;",atis,group_by,
"What is the average cost of a one-way trip for each airport pair in the fare table?","SELECT f.from_airport, f.to_airport, AVG(f.one_direction_cost) AS average_cost FROM fare f GROUP BY f.from_airport, f.to_airport ORDER BY f.from_airport, f.to_airport NULLS LAST;",atis,group_by,
"How many meals are served in each compartment, sorted by the number of meals in descending order?","SELECT food_service.compartment, COUNT(food_service.meal_number) AS number_of_meals FROM food_service GROUP BY food_service.compartment ORDER BY number_of_meals DESC NULLS LAST;",atis,group_by,
"How many flights depart from each airport code, excluding departures with connections?","SELECT airport.airport_code, COUNT(flight.from_airport) AS num_departures FROM airport LEFT JOIN flight ON airport.airport_code = flight.from_airport AND flight.connections=0 GROUP BY airport.airport_code;SELECT airport.airport_code, COUNT(flight.from_airport) AS num_departures FROM airport JOIN flight ON airport.airport_code = flight.from_airport WHERE flight.connections=0 GROUP BY airport.airport_code;",atis,group_by,
"Which flight ids to Chicago (ORD) have the longest duration from departure to arrival, sorted in ascending order?","SELECT flight.flight_id, (flight.arrival_time - flight.departure_time) AS duration FROM flight WHERE to_airport = 'ORD' ORDER BY duration ASC NULLS LAST;",atis,order_by,
"Which airports have the shortest minimum connect time, sorted in ascending order? Show the minimum connect time.","SELECT {airport.airport_name, airport.airport_code}, airport.minimum_connect_time FROM airport ORDER BY airport.minimum_connect_time ASC NULLS LAST;",atis,order_by,
Which aircraft code can carry the highest weight of cargo that any aircraft can carry?,SELECT aircraft.aircraft_code FROM aircraft ORDER BY pay_load DESC NULLS LAST LIMIT 1;,atis,order_by,
What are the top 2 airlines with the most flights?,"SELECT {airline.airline_name, airline.airline_code}, COUNT(flight.flight_id) AS number_of_flights FROM flight JOIN airline ON flight.airline_code = airline.airline_code GROUP BY {} ORDER BY number_of_flights DESC NULLS LAST LIMIT 2;",atis,order_by,
What are the aircraft codes for all aircraft with a cruising speed of over 200 mph? sort the aircraft codes in ascending order.,SELECT aircraft.aircraft_code FROM aircraft WHERE aircraft.cruising_speed > 200 ORDER BY aircraft.aircraft_code ASC NULLS LAST;,atis,order_by,
Calculate the ratio of the maximum range to the maximum payload for each aircraft,"SELECT aircraft.range_miles::float / NULLIF(aircraft.pay_load, 0) AS range_to_payload_ratio FROM aircraft;",atis,ratio,
What is the ratio of one-way trip costs to round-trip costs for each fare?,"SELECT fare.fare_id, fare.one_direction_cost::float / NULLIF(fare.round_trip_cost, 0) AS cost_ratio FROM fare ORDER BY cost_ratio;",atis,ratio,
What is the ratio of aircraft capacity to its range in miles for each aircraft code?,"SELECT aircraft.aircraft_code, CAST(aircraft.capacity AS FLOAT) / NULLIF(aircraft.range_miles, 0) AS capacity_range_ratio FROM aircraft;",atis,ratio,
What is the proportion of flights with stops out of all flights for each airline code?,"SELECT flight.airline_code, CAST(SUM(CASE WHEN flight.stops > 0 THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(COUNT(*), 0) AS ratio FROM flight GROUP BY flight.airline_code;",atis,ratio,
How does the average ratio of the cruising speed to the payload of an aircraft vary across different aircraft manufacturers?,"SELECT aircraft.manufacturer, AVG(CAST(aircraft.cruising_speed AS FLOAT) / NULLIF(aircraft.pay_load, 0)) AS speed_payload_ratio FROM aircraft GROUP BY aircraft.manufacturer ORDER BY speed_payload_ratio DESC NULLS LAST;",atis,ratio,
Which flights serve meals in first class? Give me the flight id and meal description.,"SELECT flight.flight_id, food_service.meal_description FROM flight JOIN food_service ON flight.meal_code = food_service.meal_code WHERE LOWER(food_service.compartment) LIKE '%first class%';",atis,table_join,
Which airlines offer flights with a stopover in Dallas?,"SELECT DISTINCT {airline.airline_name, airline.airline_code} FROM flight_stop JOIN airport ON flight_stop.stop_airport = airport.airport_code JOIN flight ON flight_stop.flight_id = flight.flight_id JOIN airline ON flight.airline_code = airline.airline_code WHERE airport.airport_location ILIKE '%Dallas%';",atis,table_join,
Which airlines offer flights from LAX to ORD?,"SELECT DISTINCT {airline.airline_name, airline.airline_code} FROM flight JOIN airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'LAX' AND flight.to_airport = 'ORD';",atis,table_join,
"Which airlines offer flights from Chicago (ORD) to New York (JFK), and how many stops do they have, sorted by number of stops in ascending order?","SELECT {airline.airline_name, airline.airline_code}, flight.stops FROM flight JOIN airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'ORD' AND flight.to_airport = 'JFK' ORDER BY flight.stops NULLS LAST;",atis,table_join,
"Which airlines do not have any flights that either depart from/arrive at JFK, or have one or more stops?","SELECT DISTINCT {airline.airline_name, airline.airline_code} FROM airline WHERE airline.airline_code NOT IN (SELECT flight.airline_code FROM flight WHERE flight.from_airport = 'JFK' OR flight.to_airport = 'JFK' OR flight.stops > 0);",atis,table_join,
Which state code is Orlando International Airport in?,SELECT state_code FROM airport WHERE airport_name ILIKE '%Orlando International Airport%';,atis,instruct,"Filter airport, city, country names using ILIKE.
Filter state code (eg NY), airport codes (eg JFK) using case-insensitive matches.
If multiple flight days are requested, use ILIKE and wildcards for each of the days separately, since they are not necessarily ordered."
Which flights operate on Mondays and Wednesdays? Give me the relevant flight numbers,"SELECT {flight.flight_number, flight.flight_id} FROM flight WHERE LOWER(flight.flight_days) LIKE '%mon%' AND LOWER(flight.flight_days) LIKE '%wed%';",atis,instruct,"Filter airport, city, country names using ILIKE.
Filter state code (eg NY), airport codes (eg JFK) using case-insensitive matches.
If multiple flight days are requested, use ILIKE for each of the days separately, since they are not necessarily ordered."
What is the total cost of all round-trip fares from New York (JFK) to Los Angeles?,SELECT SUM(fare.round_trip_cost) AS total_round_trip_cost FROM fare WHERE fare.from_airport = 'JFK' AND fare.to_airport = 'LAX';,atis,instruct,"Filter airport, city, country names using ILIKE.
Filter state code (eg NY), airport codes (eg JFK) using case-insensitive matches.
fare.round_trip_required is not needed when getting the round trip cost."
What is the minimum amount of time required for a connecting flight at JFK Airport?,SELECT minimum_connect_time FROM airport WHERE airport_code = 'JFK';,atis,instruct,"Filter airport, city, country names using ILIKE.
Filter state code (eg NY) and airport codes (eg JFK) using upper-case matches."
How many flights require a round-trip to purchase the fare?,SELECT COUNT(DISTINCT flight_fare.flight_id) FROM flight_fare JOIN fare ON flight_fare.fare_id = fare.fare_id WHERE fare.round_trip_required = 'Yes';,atis,instruct,"Filter airport, city, country names using ILIKE.
Filter state code (eg NY) and airport codes (eg JFK) using upper-case matches."
What is the total population in cities by country?,"SELECT city.country_name, SUM(city.population) AS total_population FROM city GROUP BY city.country_name ORDER BY total_population DESC NULLS LAST;",geography,group_by,
What is the average length of rivers in each country?,"SELECT river.country_name, AVG(river.length) AS average_length FROM river GROUP BY river.country_name ORDER BY average_length DESC NULLS LAST;",geography,group_by,
How many rivers flow through each country?,"SELECT river.country_name, COUNT(DISTINCT river.river_name) AS number_of_rivers FROM river GROUP BY river.country_name ORDER BY number_of_rivers DESC;",geography,group_by,
How many mountains are there in each country?,"SELECT mountain.country_name, COUNT(mountain.mountain_name) AS number_of_mountains FROM mountain GROUP BY mountain.country_name ORDER BY number_of_mountains DESC;",geography,group_by,
How many lakes are there in each state?,"SELECT lake.state_name, COUNT(lake.lake_name) AS lake_count FROM lake GROUP BY lake.state_name ORDER BY lake_count DESC;SELECT s.state_name, COUNT(l.lake_name) AS lake_count FROM state s LEFT JOIN lake l ON s.state_name = l.state_name GROUP BY s.state_name ORDER BY s.state_name;",geography,group_by,
"Which states have the highest population density in people per square kilometer, ordered from highest to lowest?","SELECT state.state_name, state.density FROM state ORDER BY state.density DESC NULLS LAST;",geography,order_by,
"Which lakes have the largest areas in square kilometers, ordered from largest to smallest?","SELECT lake.lake_name, lake.area FROM lake ORDER BY lake.area DESC NULLS LAST;",geography,order_by,
What are the top 5 cities with the highest population? Give both city names and the population.,"SELECT city.city_name, city.population FROM city ORDER BY city.population DESC NULLS LAST LIMIT 5;",geography,order_by,
"What are the longest rivers in meters, ordered from longest to shortest?","SELECT river.river_name, river.length FROM river ORDER BY river.length DESC NULLS LAST;",geography,order_by,
"What are the highest mountains in meters, ordered from highest to lowest altitude?","SELECT mountain.mountain_name, mountain.mountain_altitude FROM mountain ORDER BY mountain.mountain_altitude DESC NULLS LAST;",geography,order_by,
What is the ratio of the population of the United States to the population of California?,"SELECT CAST(SUM(NULLIF(state.population, 0)) FILTER (WHERE LOWER(state.country_name) LIKE '%united states%') AS FLOAT) / CAST(SUM(NULLIF(state.population, 0)) FILTER (WHERE LOWER(state.state_name) LIKE '%california%') AS FLOAT) AS population_ratio FROM state;",geography,ratio,
What is the ratio of the length of the Mississippi River to the length of the Rhine River?,"SELECT CAST((SELECT length FROM river WHERE LOWER(river_name) LIKE '%mississippi%') AS FLOAT) / NULLIF((SELECT length FROM river WHERE LOWER(river_name) LIKE '%rhine%'), 0) AS ratio ;",geography,ratio,
"What is the ratio of the altitude of 'Mount Everest' to the altitude of 'Dhaulagiri'? Match strings exactly","SELECT (CAST(everest.mountain_altitude AS FLOAT) / NULLIF(dhaulagiri.mountain_altitude, 0)) AS altitude_ratio FROM (SELECT mountain_altitude FROM mountain WHERE mountain_name = 'Mount Everest') AS everest, (SELECT mountain_altitude FROM mountain WHERE mountain_name = 'Dhaulagiri') AS dhaulagiri;",geography,ratio,
"How does the population of each city vary in relation to the population of its corresponding state? Return the city name, and the proportion of each city's population relative to the state.","SELECT city.city_name, CAST(city.population AS float) / NULLIF(state.population, 0) AS population_ratio FROM city JOIN state ON city.state_name = state.state_name ORDER BY population_ratio DESC NULLS LAST;",geography,ratio,
Get the ratio of population per area for each state,"SELECT state_name, population / NULLIF(area, 0) AS population_density FROM state;",geography,ratio,
Which countries have both lakes and rivers?,SELECT DISTINCT lake.country_name FROM lake JOIN river ON lake.country_name = river.country_name;,geography,table_join,
Which states border the state where lake ontario is?,SELECT border_info.border FROM border_info JOIN lake ON border_info.state_name = lake.state_name WHERE lake.lake_name ilike '%Ontario%';,geography,table_join,
"Which lakes have a name that starts with ""Lake""? They should be located in states with an area greater than 1000 square kilometers.",SELECT lake.lake_name FROM lake JOIN state ON lake.state_name = state.state_name WHERE state.area > 1000 AND lake.lake_name ilike 'Lake%' ORDER BY lake.lake_name NULLS LAST;,geography,table_join,
What is the highest point in each state and what is the population density of that state?,"SELECT highlow.state_name, highlow.highest_point, state.density FROM highlow JOIN state ON highlow.state_name = state.state_name;",geography,table_join,
What is the average length of rivers per country in countries with a lake?,"SELECT l.country_name, AVG(r.length) AS average_length FROM river r JOIN lake l ON r.country_name = l.country_name GROUP BY 1;",geography,table_join,
Which states have fewer than a hundred thousand people?,SELECT state_name FROM state WHERE population < 100000;,geography,instruct,Always filter names using ILIKE
Which rivers traverse at least 3 cities/landmarks?,"SELECT river_name FROM river WHERE traverse LIKE '%,%,%';",geography,instruct,Always filter names using ILIKE
What are the names and areas of the lakes in Michigan?,"SELECT lake_name, area FROM lake WHERE state_name ILIKE '%Michigan%';",geography,instruct,Always filter names using ILIKE
What are the names and altitudes of the mountains in Nepal?,"SELECT mountain_name, mountain_altitude FROM mountain WHERE country_name ILIKE '%Nepal%';",geography,instruct,Always filter names using ILIKE
Get the cities in the United States and their population,"SELECT city_name, population FROM city WHERE country_name ILIKE '%United States%';",geography,instruct,Always filter names using ILIKE
What is the total number of restaurants serving each type of food?,"SELECT restaurant.food_type, COUNT(DISTINCT restaurant.id) AS total_number_of_restaurants FROM restaurant GROUP BY restaurant.food_type;",restaurants,group_by,
What is the total count of restaurants in each city?,"SELECT location.city_name, COUNT(DISTINCT location.restaurant_id) AS total_count FROM LOCATION GROUP BY location.city_name;",restaurants,group_by,
What is the average rating of restaurants serving each type of food?,"SELECT restaurant.food_type, AVG(restaurant.rating) AS average_rating FROM restaurant GROUP BY restaurant.food_type ORDER BY average_rating DESC NULLS LAST;",restaurants,group_by,
How many restaurants serve Italian food in each city?,"SELECT restaurant.city_name, COUNT(*) AS number_of_restaurants FROM restaurant WHERE restaurant.food_type ILIKE '%Italian%' GROUP BY restaurant.city_name ORDER BY number_of_restaurants DESC NULLS LAST;",restaurants,group_by,
How many restaurants are there in each city? Order the results by the number of restaurants in descending order.,"SELECT location.city_name, COUNT(DISTINCT location.restaurant_id) AS number_of_restaurants FROM LOCATION GROUP BY location.city_name ORDER BY number_of_restaurants DESC NULLS LAST;",restaurants,group_by,
Which street has the most number of restaurants?,SELECT street_name FROM location GROUP BY street_name ORDER BY COUNT(restaurant_id) DESC LIMIT 1;,restaurants,order_by,
Which restaurants serve Italian cuisine or are located in New York? Order the results by the restaurant name.,SELECT name FROM restaurant WHERE food_type ILIKE '%Italian%' OR city_name ILIKE '%New York%' ORDER BY name NULLS LAST;,restaurants,order_by,
What is the average rating of restaurants in each region? Order the results by the region name.,"SELECT geographic.region, AVG(restaurant.rating) AS average_rating FROM restaurant JOIN geographic ON restaurant.city_name = geographic.city_name GROUP BY geographic.region ORDER BY geographic.region NULLS LAST;",restaurants,order_by,
What are the names of the top 3 restaurants with the highest ratings?,SELECT restaurant.name FROM restaurant ORDER BY restaurant.rating DESC NULLS LAST LIMIT 3;,restaurants,order_by,
List the restaurants starting from the best ratings to the lowest,"SELECT {name, id}, rating FROM restaurant ORDER BY rating DESC;",restaurants,order_by,
What is the ratio of restaurants with rating > 4.5 to the total number of restaurants in the database.,"SELECT COUNT(*)::float / NULLIF((SELECT COUNT(*) FROM restaurant), 0) AS rating_ratio FROM restaurant WHERE rating > 4.5;",restaurants,ratio,
What is the ratio of restaurants with a rating above 4.0 to restaurants with a rating below 4.0 overall?,"SELECT CAST(SUM(CASE WHEN restaurant.rating > 4.0 THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN restaurant.rating < 4.0 THEN 1 ELSE 0 END), 0) AS ratio FROM restaurant;",restaurants,ratio,
What is the ratio of restaurants with a rating above 4 to restaurants with a rating below 4 in New York?,"SELECT CAST(COUNT(CASE WHEN rating > 4 THEN 1 END) AS FLOAT) / NULLIF(COUNT(CASE WHEN rating < 4 THEN 1 END), 0) AS ratio FROM restaurant WHERE city_name ILIKE 'New York';",restaurants,ratio,
"What is the ratio of restaurants serving vegan food to restaurants serving non-vegan food in San Francisco? Match food_type case insensitively","SELECT CAST(SUM(CASE WHEN LOWER(restaurant.food_type) LIKE '%vegan%' THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN LOWER(restaurant.food_type) NOT LIKE '%vegan%' THEN 1 ELSE 0 END), 0) AS ratio FROM restaurant WHERE LOWER(restaurant.city_name) ILIKE '%san francisco%';",restaurants,ratio,
What is the ratio of Italian restaurants out of all restaurants in Los Angeles?,"SELECT CAST(COUNT(CASE WHEN food_type ILIKE '%Italian%' THEN 1 END) AS FLOAT) / NULLIF(COUNT(food_type), 0) AS ratio FROM restaurant WHERE city_name ILIKE '%Los Angeles%';",restaurants,ratio,
"What cities have more than one restaurants with the same name, and how many of them are there? Return the city name, restaurant name, and restaurant count","SELECT r.city_name, r.name, COUNT(r.id) AS restaurant_count FROM restaurant r GROUP BY r.city_name, r.name HAVING COUNT(r.id) > 1;",restaurants,table_join,
What is the average rating of restaurants that serve Mexican food in each city?,"SELECT location.city_name, AVG(restaurant.rating) AS average_rating FROM restaurant JOIN LOCATION ON restaurant.id = location.restaurant_id WHERE LOWER(restaurant.food_type) LIKE '%mexican%' GROUP BY location.city_name;",restaurants,table_join,
What is the average rating of restaurants in each region?,"SELECT geographic.region, AVG(restaurant.rating) AS average_rating FROM geographic JOIN restaurant ON geographic.city_name=restaurant.city_name GROUP BY 1;",restaurants,table_join,
How many restaurants serve Italian food in each region?,"SELECT geographic.region, COUNT(restaurant.id) AS number_of_restaurants FROM restaurant JOIN geographic ON restaurant.city_name = geographic.city_name WHERE LOWER(restaurant.food_type) LIKE '%italian%' GROUP BY geographic.region ORDER BY number_of_restaurants DESC NULLS LAST;",restaurants,table_join,
How many restaurants are there in each region?,"SELECT geographic.region, COUNT(DISTINCT restaurant.id) AS number_of_restaurants FROM geographic JOIN restaurant ON geographic.city_name = restaurant.city_name GROUP BY geographic.region ORDER BY number_of_restaurants DESC;SELECT geographic.region, COUNT(DISTINCT restaurant.id) AS number_of_restaurants FROM geographic LEFT JOIN restaurant ON geographic.city_name = restaurant.city_name GROUP BY geographic.region ORDER BY number_of_restaurants DESC;",restaurants,table_join,
Which city has the highest-rated restaurant?,SELECT DISTINCT restaurant.city_name FROM restaurant WHERE rating=(SELECT MAX(rating) FROM restaurant);,restaurants,instruct,Match all strings case-insensitively using wildcard operators
What's the name and rating of all the restaurants that have a rating greater than 4 and are located in the city of New York?,"SELECT restaurant.name, restaurant.rating FROM restaurant WHERE restaurant.rating > 4 AND restaurant.city_name ILIKE '%New York%';",restaurants,instruct,Match all strings case-insensitively using wildcard operators
What's the name and food type of all the restaurants located on Market St in San Francisco?,"SELECT restaurant.name, restaurant.food_type FROM restaurant JOIN LOCATION ON restaurant.id = location.restaurant_id WHERE location.street_name ILIKE '%Market St%' AND location.city_name ILIKE '%San Francisco%';",restaurants,instruct,Match all strings case-insensitively using wildcard operators
What are the names of the restaurants that serve Italian food?,SELECT restaurant.name FROM restaurant WHERE LOWER(restaurant.food_type) ILIKE '%italian%';,restaurants,instruct,Match all strings case-insensitively using wildcard operators
What are the names of the restaurants in Los Angeles that have a rating higher than 4?,SELECT DISTINCT restaurant.name FROM restaurant WHERE restaurant.city_name ILIKE '%Los Angeles%' AND restaurant.rating > 4 ORDER BY restaurant.name NULLS LAST;,restaurants,instruct,Match all strings case-insensitively using wildcard operators
What is the total number of papers published per year?,"SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM paper GROUP BY paper.year ORDER BY paper.year NULLS LAST;",scholar,group_by,
What is the total number of papers published in each year?,"SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM paper GROUP BY paper.year ORDER BY paper.year;",scholar,group_by,
What is the total number of papers associated with each dataset?,"SELECT paperdataset.datasetid, COUNT(DISTINCT paperdataset.paperid) AS total_papers FROM paperdataset GROUP BY paperdataset.datasetid;SELECT dataset.datasetname, COUNT(paperdataset.paperid) AS total_papers FROM paperdataset JOIN dataset ON paperdataset.datasetid = dataset.datasetid GROUP BY dataset.datasetname;",scholar,group_by,
How many keyphrases are associated with each paper?,"SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS keyphrase_count FROM paperkeyphrase GROUP BY paperkeyphrase.paperid ORDER BY keyphrase_count DESC NULLS LAST;SELECT p.title, COUNT(pk.keyphraseid) AS num_keyphrases FROM paper p JOIN paperkeyphrase pk ON p.paperid = pk.paperid GROUP BY p.title ORDER BY num_keyphrases DESC NULLS LAST;",scholar,group_by,
How many authors have published more than 2 papers?,SELECT COUNT(*) AS number_of_authors FROM (SELECT writes.authorid FROM writes GROUP BY writes.authorid HAVING COUNT(writes.paperid) > 2) AS subquery;,scholar,group_by,
"Which papers have the highest number of authors, ordered by the number of authors in descending order?","SELECT {paper.paperid, paper.title}, COUNT(DISTINCT writes.authorid) AS num_authors FROM paper JOIN writes ON paper.paperid = writes.paperid GROUP BY {} ORDER BY num_authors DESC;",scholar,order_by,
"What is the total number of keyphrases associated with each paper, ordered by the paper ID in ascending order?","SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS total_keyphrases FROM paperkeyphrase GROUP BY paperkeyphrase.paperid ORDER BY paperkeyphrase.paperid ASC NULLS LAST;",scholar,order_by,
"What are the titles of the papers published in the year 2020, ordered alphabetically?",SELECT paper.title FROM paper WHERE paper.year = 2020 ORDER BY paper.title ASC NULLS LAST;,scholar,order_by,
"What are the names of the journals in the database, ordered by the length of the journal name from shortest to longest?",SELECT journal.journalname FROM journal ORDER BY LENGTH(journal.journalname) ASC NULLS LAST;,scholar,order_by,
"For each paper that cites other papers, how many other papers does it cite? Sort by the number of papers cited in descending order","SELECT cite.citingpaperid, COUNT(*) AS citation_count FROM cite GROUP BY cite.citingpaperid ORDER BY citation_count DESC NULLS LAST;SELECT p.paperid, p.numciting FROM paper p WHERE p.numciting > 0 ORDER BY p.numciting DESC;SELECT p.title, COUNT(c.citedpaperid) AS num_cited_papers FROM paper p JOIN cite c ON p.paperid = c.citingpaperid GROUP BY p.title ORDER BY num_cited_papers DESC;",scholar,order_by,
What is the ratio of papers that have more than 1 keyphrases to papers that have 1 keyphrase?,"SELECT CAST(COUNT(DISTINCT CASE WHEN keyphrase_count > 1 THEN subquery.paperid END) AS FLOAT) / NULLIF(COUNT(DISTINCT CASE WHEN keyphrase_count =1 THEN subquery.paperid END), 0) AS ratio FROM (SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS keyphrase_count FROM paperkeyphrase GROUP BY paperkeyphrase.paperid) AS subquery;",scholar,ratio,
What is the ratio of papers that have been cited by 2 or more papers to papers that have been cited by less than 2 papers?,"SELECT CAST(COUNT(CASE WHEN paper.numcitedby > 1 THEN 1 END) AS FLOAT) / NULLIF(COUNT(CASE WHEN paper.numcitedby < 2 THEN 1 END), 0) AS ratio FROM paper;",scholar,ratio,
What is the ratio of papers published in the year 2020 to the total number of papers in the database?,"SELECT CAST(COUNT(CASE WHEN paper.year = 2020 THEN 1 END) AS FLOAT) / NULLIF(COUNT(paper.paperid), 0) AS ratio FROM paper;",scholar,ratio,
What is the ratio of authors who have written 3 or more papers to authors who have written less than 3 papers?,"SELECT CAST(COUNT(DISTINCT CASE WHEN paper_count >= 3 THEN subquery.authorid END) AS FLOAT) / NULLIF(COUNT(DISTINCT CASE WHEN paper_count < 3 THEN subquery.authorid END), 0) AS ratio FROM (SELECT writes.authorid, COUNT(writes.paperid) AS paper_count FROM writes GROUP BY writes.authorid) AS subquery;",scholar,ratio,
What is the proportion of papers that belong to more than 1 dataset to papers that belong to 1 dataset?,"SELECT CAST(COUNT(CASE WHEN dataset_count > 1 THEN 1 END) AS FLOAT) / NULLIF(COUNT(CASE WHEN dataset_count = 1 THEN 1 END), 0) AS ratio FROM (SELECT paperdataset.paperid, COUNT(paperdataset.datasetid) AS dataset_count FROM paperdataset GROUP BY paperdataset.paperid) AS subquery;",scholar,ratio,
"Which papers are associated with the keyphrase ""Machine Learning""?","SELECT {paper.title,paper.paperid} FROM paper JOIN paperkeyphrase ON paper.paperid = paperkeyphrase.paperid JOIN keyphrase ON paperkeyphrase.keyphraseid = keyphrase.keyphraseid WHERE keyphrase.keyphrasename ILIKE '%Machine Learning%';",scholar,table_join,
"Which authors have published the most papers, ordered by the number of papers they have published in descending order?","SELECT {author.authorname, author.authorid}, COUNT(DISTINCT writes.paperid) AS number_of_papers FROM author JOIN writes ON author.authorid = writes.authorid GROUP BY {} ORDER BY number_of_papers DESC NULLS LAST;",scholar,table_join,
"What is the total number of unique keyphrases associated with papers published in the journal with ""IEEE Transactions"" in its name?",SELECT COUNT(DISTINCT paperkeyphrase.keyphraseid) AS total_keyphrases FROM paper JOIN journal ON paper.journalid = journal.journalid JOIN paperkeyphrase ON paper.paperid = paperkeyphrase.paperid WHERE journal.journalname ILIKE '%IEEE Transactions%';,scholar,table_join,
"What is the total number of papers published in each journal, ordered by the journal name?","SELECT journal.journalname, COUNT(DISTINCT paper.paperid) AS total_papers FROM paper JOIN journal ON paper.journalid = journal.journalid GROUP BY journal.journalname ORDER BY journal.journalname NULLS LAST;",scholar,table_join,
"How many papers cite each paper in the dataset named ""COVID-19 Research""?","SELECT paperdataset.paperid, COUNT(cite.citedpaperid) AS citation_count FROM paperdataset JOIN cite ON paperdataset.paperid = cite.citedpaperid WHERE paperdataset.datasetid = (SELECT datasetid FROM dataset WHERE datasetname ILIKE '%COVID-19 Research%') GROUP BY paperdataset.paperid ORDER BY citation_count DESC;SELECT p.title, COUNT(c.citingpaperid) AS num_citing_papers FROM paper p JOIN paperdataset pd ON p.paperid = pd.paperid JOIN cite c ON p.paperid = c.citedpaperid JOIN dataset d ON pd.datasetid = d.datasetid WHERE d.datasetname = 'COVID-19 Research' GROUP BY p.title ORDER BY num_citing_papers DESC NULLS LAST;",scholar,table_join,
"What is the name of the venue where the paper with paper ID 2 was published, and how many papers were published in total in that venue?","SELECT venue.venuename, COUNT(DISTINCT paper.paperid) FROM paper JOIN venue ON paper.venueid = venue.venueid WHERE paper.venueid = (SELECT venueid FROM paper WHERE paperid = 2 ) GROUP BY venue.venuename;",scholar,instruct,Always filter strings using ILIKE
"What are the names of the authors who wrote the paper with the title ""The Effects of Climate Change on Agriculture""?",SELECT author.authorname FROM author JOIN writes ON author.authorid = writes.authorid JOIN paper ON writes.paperid = paper.paperid WHERE paper.title = 'The Effects of Climate Change on Agriculture';,scholar,instruct,Always filter strings with an exact match
"How many papers were published in the journal ""nature"" in the year 2020?",SELECT COUNT(paper.paperid) FROM paper JOIN journal ON paper.journalid = journal.journalid WHERE paper.year = 2020 AND journal.journalname ILIKE '%nature%';,scholar,instruct,Filter strings with case-insensitive matching
"How many papers are associated with the keyphrase ""machine learning"" and were published in the journal named ""IEEE Transactions on Pattern Analysis and Machine Intelligence""?",SELECT COUNT(DISTINCT paper.paperid) FROM paper JOIN journal ON paper.journalid = journal.journalid JOIN paperkeyphrase ON paper.paperid = paperkeyphrase.paperid JOIN keyphrase ON paperkeyphrase.keyphraseid = keyphrase.keyphraseid WHERE keyphrase.keyphrasename ILIKE '%machine learning%' AND journal.journalname = 'IEEE Transactions on Pattern Analysis and Machine Intelligence';,scholar,instruct,"Filter paper names, journal names, using exact matches. Filter keyphrases with case-insensitive matching."
"How many authors wrote papers that were published in the journal ""Science"" in the year 2020?",SELECT COUNT(DISTINCT writes.authorid) AS number_of_authors FROM writes JOIN paper ON writes.paperid = paper.paperid JOIN journal ON paper.journalid = journal.journalid WHERE journal.journalname ILIKE '%Science%' AND paper.year = 2020;,scholar,instruct,Filter paper names using exact matches. Filter keyphrases and journal names with case-insensitive matching.
How many reviews were written for businesses located in California in the last 10 calendar months (not including the current month)?,"SELECT count(*) AS review_count FROM review r JOIN business b ON r.business_id = b.business_id WHERE b.state = 'CA' AND (r.year * 12 + extract(MONTH FROM to_date(r.month, 'Month'))) >= (extract(YEAR FROM CURRENT_DATE) * 12 + extract(MONTH FROM CURRENT_DATE) - 10);",yelp,date_functions,
What is the total number of check-ins on the 2 days before Saturday?,"SELECT sum(COUNT) AS total_checkins FROM checkin WHERE DAY IN ('Thursday', 'Friday') ;",yelp,date_functions,
How many reviews were there 2 months before the review with id 3?,"SELECT COUNT(*) AS review_count FROM review WHERE TO_DATE(CAST(review.year AS TEXT) || '-' || CASE review.month WHEN 'January' THEN '01' WHEN 'February' THEN '02' WHEN 'March' THEN '03' WHEN 'April' THEN '04' WHEN 'May' THEN '05' WHEN 'June' THEN '06' WHEN 'July' THEN '07' WHEN 'August' THEN '08' WHEN 'September' THEN '09' WHEN 'October' THEN '10' WHEN 'November' THEN '11' WHEN 'December' THEN '12' END || '-01', 'YYYY-MM-DD') = (SELECT TO_DATE(CAST(r.year AS TEXT) || '-' || CASE r.month WHEN 'January' THEN '01' WHEN 'February' THEN '02' WHEN 'March' THEN '03' WHEN 'April' THEN '04' WHEN 'May' THEN '05' WHEN 'June' THEN '06' WHEN 'July' THEN '07' WHEN 'August' THEN '08' WHEN 'September' THEN '09' WHEN 'October' THEN '10' WHEN 'November' THEN '11' WHEN 'December' THEN '12' END || '-01', 'YYYY-MM-DD') - INTERVAL '2 months' FROM review r WHERE r.rid = 3);",yelp,date_functions,
What was the message that came with the tip made exactly 2 months after March 2021?,SELECT text AS message FROM tip WHERE MONTH ILIKE '%May%' AND YEAR = 2021 LIMIT 1;,yelp,date_functions,
How many months between June 2021 and December 2021 had reviews?,SELECT COUNT(DISTINCT MONTH) AS num_months FROM review WHERE YEAR = 2021 AND CASE MONTH WHEN 'January' THEN 1 WHEN 'February' THEN 2 WHEN 'March' THEN 3 WHEN 'April' THEN 4 WHEN 'May' THEN 5 WHEN 'June' THEN 6 WHEN 'July' THEN 7 WHEN 'August' THEN 8 WHEN 'September' THEN 9 WHEN 'October' THEN 10 WHEN 'November' THEN 11 WHEN 'December' THEN 12 END BETWEEN 6 AND 12;,yelp,date_functions,
"Which neighbourhoods have the highest number of businesses, and how many businesses are located in each neighbourhood?","SELECT {neighbourhood.neighbourhood_name, neighbourhood.id}, COUNT(DISTINCT neighbourhood.business_id) AS business_count FROM neighbourhood GROUP BY {} ORDER BY business_count DESC NULLS LAST;",yelp,group_by,
"What is the total number of check-ins for each day of the week for the business with ID ""abc123""?","SELECT checkin.day, SUM(checkin.count) AS total_checkins FROM checkin WHERE checkin.business_id = 'abc123' GROUP BY checkin.day ORDER BY total_checkins DESC NULLS LAST;",yelp,group_by,
What is the total count of check-ins for each business id?,"SELECT checkin.business_id, SUM(checkin.count) AS total_checkins FROM checkin GROUP BY checkin.business_id ORDER BY total_checkins DESC NULLS LAST;",yelp,group_by,
Return the name and average rating for each business in new york,"SELECT business.name, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id WHERE business.city ILIKE '%NEW YORK%' GROUP BY business.name;",yelp,group_by,
How many check-ins occurred on each day of the week?,"SELECT checkin.day, SUM(checkin.count) AS total_checkins FROM checkin GROUP BY checkin.day ORDER BY total_checkins DESC NULLS LAST;",yelp,group_by,
Please provide a list of business names in New York and their average ratings ordered by the highest average rating first.,"SELECT business.name, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id WHERE business.city ILIKE '%New York%' GROUP BY business.name, business.business_id ORDER BY average_rating DESC NULLS LAST;",yelp,order_by,
What is the latitude and longitude of the business with the highest rating?,"SELECT business.latitude, business.longitude FROM business JOIN review ON business.business_id = review.business_id GROUP BY business.business_id, business.latitude, business.longitude ORDER BY AVG(review.rating) DESC LIMIT 1;",yelp,order_by,
What are the top 3 businesses in terms of review count?,"SELECT {business.name, business.business_id, business.bid}, business.review_count FROM business ORDER BY business.review_count DESC NULLS LAST LIMIT 3;",yelp,order_by,
"What are the names of the businesses in the database, ordered alphabetically?",SELECT business.name FROM business ORDER BY business.name ASC NULLS LAST;,yelp,order_by,
"How many reviews were posted in each month of the year 2021, ordered by the month?","SELECT review.month, COUNT(review.rid) AS review_count FROM review WHERE review.year = 2021 GROUP BY review.month ORDER BY TO_DATE(review.month, 'Month') NULLS LAST;",yelp,order_by,
What is the ratio of the number of businesses in each state to the total number of businesses in the database?,"SELECT business.state, COUNT(business.business_id) / NULLIF(CAST((SELECT COUNT(*) FROM business) AS FLOAT), 0) AS ratio FROM business GROUP BY business.state;",yelp,ratio,
What is the ratio of open businesses to closed businesses in the city of San Francisco?,"SELECT CAST(SUM(CASE WHEN business.is_open = 1 THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN business.is_open = 0 THEN 1 ELSE 0 END), 0) AS ratio FROM business WHERE LOWER(business.city) ILIKE '%san francisco%';",yelp,ratio,
"What is the ratio of check-ins on weekends to check-ins on weekdays for the business named ""Mark’s Bistro""?","SELECT CAST(SUM(CASE WHEN checkin.day IN ('Saturday', 'Sunday') THEN checkin.count ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN checkin.day NOT IN ('Saturday', 'Sunday') THEN checkin.count ELSE 0 END), 0) AS ratio FROM checkin JOIN business ON checkin.business_id = business.business_id WHERE business.name ILIKE '%Mark’s Bistro%';",yelp,ratio,
What is the ratio of businesses in the state of California to businesses in the state of New York?,"SELECT CAST(COUNT(CASE WHEN business.state = 'CA' THEN 1 END) AS FLOAT) / NULLIF(COUNT(CASE WHEN business.state = 'NY' THEN 1 END), 0) AS ratio FROM business;",yelp,ratio,
"How does the ratio of positive reviews (rating > 3) to negative reviews (rating < 3) vary across different categories of businesses, ordered by descending ratio?","SELECT {category.category_name, category.id}, CAST(COUNT(CASE WHEN review.rating > 3 THEN 1 END) AS FLOAT) / NULLIF(COUNT(CASE WHEN review.rating < 3 THEN 1 END), 0) AS ratio FROM review JOIN category ON review.business_id = category.business_id GROUP BY {} ORDER BY ratio DESC NULLS LAST;",yelp,ratio,
"Which users have posted reviews for businesses located in the neighbourhood of ""Downtown"" and how many reviews have they posted?","SELECT {users.name, users.user_id}, COUNT(review.rid) AS review_count FROM review JOIN neighbourhood ON review.business_id = neighbourhood.business_id JOIN users ON review.user_id = users.user_id WHERE neighbourhood.neighbourhood_name ILIKE '%Downtown%' GROUP BY {} ORDER BY review_count DESC NULLS LAST;",yelp,table_join,
"What is the total number of reviews for each category in the state of ""California""?","SELECT {category.category_name, category.id}, SUM(business.review_count) AS total_reviews FROM business JOIN category ON business.business_id = category.business_id WHERE business.state = 'CA' GROUP BY {} ORDER BY total_reviews DESC NULLS LAST;",yelp,table_join,
What is the total number of reviews for each business category?,"SELECT {category.category_name, category.id}, SUM(business.review_count) AS total_reviews FROM business JOIN category ON business.business_id = category.business_id GROUP BY {} ORDER BY total_reviews DESC NULLS LAST;",yelp,table_join,
What is the total number of check-ins for each business in the state of California?,"SELECT {business.business_id, business.name, business.bid}, SUM(checkin.count) AS total_checkins FROM business JOIN checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' GROUP BY {} ORDER BY total_checkins DESC NULLS LAST;",yelp,table_join,
What are the top 2 categories of businesses with the highest average rating?,"SELECT {category.category_name, category.id} FROM (SELECT business.business_id, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id GROUP BY business.business_id) AS business_rating JOIN category ON business_rating.business_id = category.business_id GROUP BY {} ORDER BY AVG(business_rating.average_rating) DESC NULLS LAST LIMIT 2;",yelp,table_join,
"What is the total number of reviews posted in the year 2021 for businesses in the category ""Cafe""?",SELECT COUNT(review.rid) AS total_reviews FROM review JOIN category ON review.business_id = category.business_id WHERE review.year = 2021 AND category.category_name ILIKE '%Cafe%';,yelp,instruct,"Filter strings of users, city, address, business.name using ILIKE with wildcards.
Filter strings of state using exact upper case matches.
Assume the rating of a business to be its average rating, and compute it before computing other aggregates on it.
"
What is the average rating of businesses in the city of San Francisco?,"SELECT AVG(sf.average_rating) AS sf_average_rating FROM (SELECT business.business_id, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id WHERE LOWER(business.city) ILIKE '%san francisco%' GROUP BY business.business_id) AS sf;",yelp,instruct,"Filter strings of users, city, address, business.name using ILIKE with wildcards.
Filter strings of state using exact upper case matches.
The rating of businesses in a city refers to the average rating of the businesses in that city. I.e., you must compute the average rating of each business before computing the average rating of businesses in the city.
"
How many reviews were posted for each business id in the year 2021?,"SELECT review.business_id, COUNT(*) AS review_count FROM review WHERE review.year = 2021 GROUP BY review.business_id ORDER BY review_count DESC NULLS LAST;",yelp,instruct,"Filter strings of users, city, address, business.name using ILIKE with wildcards.
Filter strings of state using exact upper case matches.
Assume the rating of a business to be its average rating, and compute it before computing other aggregates on it.
"
"How many reviews were posted by users with the name ""Sarah Williams"" in the month of April 2021?",SELECT COUNT(*) FROM review JOIN users ON review.user_id = users.user_id WHERE users.name ILIKE '%Sarah Williams%' AND review.month = 'April' AND review.year = 2021;,yelp,instruct,"Filter strings of users, city, address, business.name using ILIKE with wildcards.
Filter strings of state using exact upper case matches.
Assume the rating of a business to be its average rating, and compute it before computing other aggregates on it.
"
How many check-ins occurred on Mondays at businesses in the state of California?,SELECT SUM(checkin.count) AS total_checkins FROM business JOIN checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' AND checkin.day ILIKE '%Monday%';,yelp,instruct,"Filter strings of users, city, address, business.name using ILIKE with wildcards.
Filter strings of state using exact upper case matches.
Assume the rating of a business to be its average rating, and compute it before computing other aggregates on it.
"
"Return the customer who made the most sell transactions on 2023-04-01. Return the id, name and number of transactions.","WITH SellTransactions AS (SELECT sbTxCustId, COUNT(*) AS num_tx FROM sbTransaction WHERE sbTxDateTime::date = '2023-04-01' AND sbTxType = 'sell' GROUP BY sbTxCustId) SELECT c.sbCustId, c.sbCustName, st.num_tx FROM sbCustomer c JOIN SellTransactions st ON c.sbCustId = st.sbTxCustId ORDER BY st.num_tx DESC LIMIT 1;",broker,date_functions,
"What is the monthly average transaction price for successful transactions in the 1st quarter of 2023?","SELECT DATE_TRUNC('month', sbTxDateTime) AS MONTH, AVG(sbTxPrice) AS avg_price FROM sbTransaction WHERE sbTxStatus = 'success' AND sbTxDateTime BETWEEN '2023-01-01' AND '2023-03-31' GROUP BY MONTH ORDER BY MONTH;SELECT TO_CHAR(sbTxDateTime, 'YYYY-MM') AS MONTH, AVG(sbTxPrice) AS average_transaction_price FROM sbTransaction WHERE sbTxStatus = 'success' AND sbTxDateTime >= '2023-01-01' AND sbTxDateTime < '2023-04-01' GROUP BY TO_CHAR(sbTxDateTime, 'YYYY-MM') ORDER BY MONTH;",broker,date_functions,
"Lowest daily closest price for symbol `VTI` in the past 7 days","SELECT MIN(sdp.sbDpClose) AS lowest_price FROM sbDailyPrice AS sdp JOIN sbTicker AS st ON sdp.sbDpTickerId = st.sbTickerId WHERE st.sbTickerSymbol = 'VTI' AND sdp.sbDpDate >= CURRENT_DATE - INTERVAL '7 days';",broker,date_functions,
"Return the customer id and the difference between their time from joining to their first transaction. Ignore customers who haven't made any transactions.","SELECT c.sbCustId, MIN(t.sbTxDateTime) - c.sbCustJoinDate AS DaysFromJoinToFirstTransaction FROM sbCustomer c JOIN sbTransaction t ON c.sbCustId = t.sbTxCustId GROUP BY c.sbCustId;SELECT c.sbCustId, age(MIN(t.sbTxDateTime), c.sbCustJoinDate) AS time_difference FROM sbCustomer c JOIN sbTransaction t ON c.sbCustId = t.sbTxCustId GROUP BY c.sbCustId, c.sbCustJoinDate;SELECT c.sbCustId, MIN(DATE(t.sbTxDateTime)) - c.sbCustJoinDate AS days_difference FROM sbCustomer c JOIN sbTransaction t ON c.sbCustId = t.sbTxCustId GROUP BY c.sbCustId, c.sbCustJoinDate;",broker,date_functions,
"number of transactions by users who joined in the past 70 days","SELECT COUNT(t.sbTxCustId) AS transaction_count FROM sbTransaction t JOIN sbCustomer c ON t.sbTxCustId = c.sbCustId WHERE c.sbCustJoinDate >= CURRENT_DATE - INTERVAL '70' DAY;",broker,date_functions,
"Return the treatment id, treatment start date, adverse event date and description of all adverse events that occured within 10 days after starting treatment","SELECT t.treatment_id, t.start_dt, ae.reported_dt, ae.description FROM adverse_events ae JOIN treatments t ON ae.treatment_id = t.treatment_id WHERE ae.reported_dt BETWEEN t.start_dt AND t.start_dt + INTERVAL '10 days';",derm_treatment,date_functions,
"List the last name, year of registration, and first treatment (date and id) by doctors who were registered 2 years ago.","WITH doc_first_treatment AS (SELECT d.doc_id, d.last_name, d.year_reg, t.treatment_id, t.start_dt, ROW_NUMBER() OVER (PARTITION BY d.doc_id ORDER BY t.start_dt ASC) AS rn FROM doctors d JOIN treatments t ON d.doc_id = t.doc_id WHERE d.year_reg = EXTRACT(YEAR FROM CURRENT_DATE) - 2 ) SELECT last_name, year_reg, start_dt AS first_treatment_date, treatment_id AS first_treatment_id FROM doc_first_treatment WHERE rn = 1;",derm_treatment,date_functions,
"what is average age of all registered male patients with private insurance currently?","SELECT AVG(EXTRACT(YEAR FROM AGE(CURRENT_DATE, date_of_birth))) AS avg_age FROM patients WHERE gender = 'Male' AND ins_type = 'private';",derm_treatment,date_functions,
"show all placebo treatment id, start and end date, where there concomitant_meds were started within 2 weeks of starting the treatment. also return the start and end dates of all concomitant drug usage.","SELECT t.treatment_id, t.start_dt AS treatment_start_date, t.end_dt AS treatment_end_date, cm.start_dt AS concomitant_med_start_date, cm.end_dt AS concomitant_med_end_date FROM treatments t JOIN concomitant_meds cm ON t.treatment_id = cm.treatment_id WHERE t.is_placebo = TRUE AND TO_DATE(cm.start_dt, 'YYYY-MM-DD') BETWEEN t.start_dt AND t.start_dt + INTERVAL '2 WEEK' ORDER BY t.treatment_id;",derm_treatment,date_functions,
"How many treatments for diagnoses containing 'psoriasis' (match with wildcards case-insensitively) involve drugs that have been FDA-approved and the treatments have ended within the last 6 months from today?","SELECT COUNT(*) FROM treatments t JOIN diagnoses d ON t.diag_id = d.diag_id JOIN drugs dr ON t.drug_id = dr.drug_id WHERE d.diag_name ILIKE '%psoriasis%' AND dr.fda_appr_dt IS NOT NULL AND t.end_dt >= CURRENT_DATE - INTERVAL '6 months';",derm_treatment,date_functions,
"What was the average transaction daily and monthly limit for the earliest setting snapshot in 2023?","SELECT AVG(tx_limit_daily) AS avg_daily_limit, AVG(tx_limit_monthly) AS avg_monthly_limit FROM consumer_div.user_setting_snapshot WHERE snapshot_date = (SELECT MIN(snapshot_date) FROM consumer_div.user_setting_snapshot WHERE snapshot_date >= '2023-01-01' AND snapshot_date < '2024-01-01' );",ewallet,date_functions,
"Which users did not get a notification within the first year of signing up? Return their usernames, emails and signup dates.","SELECT u.username, u.email, u.created_at FROM consumer_div.users u LEFT JOIN consumer_div.notifications n ON u.uid = n.user_id AND n.created_at BETWEEN u.created_at AND u.created_at + INTERVAL '1 year' WHERE n.user_id IS NULL;",ewallet,date_functions,
"what was the average user session duration in seconds split by device_type?","SELECT device_type, AVG(EXTRACT(EPOCH FROM (session_end_ts - session_start_ts))) AS avg_session_duration_seconds FROM consumer_div.user_sessions WHERE session_end_ts IS NOT NULL GROUP BY device_type;",ewallet,date_functions,
"Give me today's median merchant wallet balance for all active merchants whose category contains 'retail'","WITH retail_merchants AS (SELECT mid FROM consumer_div.merchants WHERE category ILIKE '%retail%' AND status = 'active' ), merchant_balances AS (SELECT balance FROM consumer_div.wallet_merchant_balance_daily wmbd JOIN retail_merchants rm ON wmbd.merchant_id = rm.mid WHERE DATE(wmbd.updated_at) = CURRENT_DATE ) SELECT PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY balance) AS median_balance FROM merchant_balances;",ewallet,date_functions,
"Which merchants earliest coupon start date was within a year of the merchant's registration? Return the merchant id, registration date, and earliest coupon id and start date","WITH earliest_coupons AS (SELECT c.merchant_id, MIN(c.start_date) AS earliest_coupon_start_date FROM consumer_div.coupons c GROUP BY c.merchant_id) SELECT m.mid AS merchant_id, m.created_at AS merchant_registration_date, ec.earliest_coupon_start_date, c.cid AS earliest_coupon_id FROM consumer_div.merchants m JOIN earliest_coupons ec ON m.mid = ec.merchant_id JOIN consumer_div.coupons c ON ec.merchant_id = c.merchant_id AND ec.earliest_coupon_start_date = c.start_date WHERE ec.earliest_coupon_start_date <= m.created_at + INTERVAL '1 year';",ewallet,date_functions,
"Return the name and phone number of the salesperson with the shortest time from being hired to getting fired. Return the number of days he/she was employed for.","SELECT s.first_name, s.last_name, s.phone, s.termination_date - s.hire_date AS days_employed FROM salespersons s ORDER BY days_employed ASC LIMIT 1;SELECT first_name || ' ' || last_name AS name, phone, (termination_date - hire_date) AS days_employed FROM salespersons WHERE termination_date IS NOT NULL ORDER BY (termination_date - hire_date) LIMIT 1;",car_dealership,date_functions,
"Return the number of payments made on weekends to the vendor named 'Utility Company'","SELECT COUNT(*) AS weekend_payments FROM payments_made WHERE vendor_name = 'Utility Company' AND EXTRACT(DOW FROM payment_date) IN (0, 6);",car_dealership,date_functions,
"show me the daily total amount of payments received in the whole of the previous ISO week not including the current week, split by the payment_method","SELECT payment_date, payment_method, SUM(payment_amount) AS total_amount FROM payments_received WHERE payment_date >= DATE_TRUNC('WEEK', CURRENT_DATE) - INTERVAL '1 week' AND payment_date < DATE_TRUNC('WEEK', CURRENT_DATE) GROUP BY payment_date, payment_method ORDER BY payment_date DESC, payment_method ASC;",car_dealership,date_functions,
"Which cars were in inventory in the latest snapshot for march 2023? Return the car id, make, model, and year. Cars are considered to be "in inventory" if is_in_inventory is True.","WITH latest_snapshot AS (SELECT MAX(snapshot_date) AS snapshot_date FROM inventory_snapshots WHERE snapshot_date BETWEEN '2023-03-01' AND '2023-03-31' ), latest_snapshot_data AS (SELECT inv.car_id FROM inventory_snapshots inv JOIN latest_snapshot ls ON inv.snapshot_date = ls.snapshot_date WHERE inv.is_in_inventory = TRUE ) SELECT c.id, c.make, c.model, c.year FROM cars c JOIN latest_snapshot_data lsd ON c.id = lsd.car_id;",car_dealership,date_functions,
"What were the total quarterly sales in 2023 grouped by customer's state? Represent each quarter as the first date in the quarter.","SELECT DATE_TRUNC('QUARTER', s.sale_date) AS QUARTER, c.state, SUM(s.sale_price) AS total_sales FROM sales s JOIN customers c ON s.customer_id = c.id WHERE EXTRACT(YEAR FROM s.sale_date) = 2023 GROUP BY c.state, QUARTER HAVING SUM(s.sale_price) > 0 ORDER BY QUARTER, c.state;SELECT c.state, date_trunc('quarter', s.sale_date)::date AS quarter_start, SUM(s.sale_price) AS total_sales FROM sales s JOIN customers c ON s.customer_id = c.id WHERE s.sale_date BETWEEN '2023-01-01' AND '2023-12-31' GROUP BY c.state, date_trunc('quarter', s.sale_date) ORDER BY c.state, quarter_start;",car_dealership,date_functions,