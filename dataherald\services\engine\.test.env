SQL_GENERATOR='dataherald.tests.sql_generator.test_generator.TestGenerator'
EVALUATOR='dataherald.tests.evaluator.test_eval.TestEvaluator'
DB='dataherald.tests.db.test_db.TestDB'
OPENAI_API_KEY='foo'
PINECONE_API_KEY='foo2'
PINECONE_ENVIRONMENT='bar'
GOLDEN_RECORD_COLLECTION='bar2'
VECTOR_STORE='dataherald.tests.vector_store.test_vector_store.TestVectorStore'
encrypt_key='4Mbe2GYx0Hk94o_f-irVHk1fKkCGAt1R7LLw5wHVghI='
