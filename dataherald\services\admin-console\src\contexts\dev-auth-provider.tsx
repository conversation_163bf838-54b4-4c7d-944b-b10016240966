// Development Authentication Provider - Bypasses Auth0 in development mode
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

interface DevUser {
  sub: string
  email: string
  name: string
  picture: string
  email_verified: boolean
}

interface DevAuthContextType {
  user: DevUser | null
  isLoading: boolean
  error: null
  token: string | null
}

const DevAuthContext = createContext<DevAuthContextType | undefined>(undefined)

const DEV_USER: DevUser = {
  sub: 'dev-user-123',
  email: '<EMAIL>',
  name: 'Development User',
  picture: 'https://via.placeholder.com/150',
  email_verified: true,
}

const DEV_TOKEN = 'dev-token-for-local-development-only'

interface DevAuthProviderProps {
  children: ReactNode
}

export function DevAuthProvider({ children }: DevAuthProviderProps) {
  const [user] = useState<DevUser>(DEV_USER)
  const [isLoading] = useState(false)
  const [error] = useState(null)
  const [token] = useState<string>(DEV_TOKEN)

  const contextValue: DevAuthContextType = {
    user,
    isLoading,
    error,
    token,
  }

  return (
    <DevAuthContext.Provider value={contextValue}>
      {children}
    </DevAuthContext.Provider>
  )
}

export function useDevAuth(): DevAuthContextType {
  const context = useContext(DevAuthContext)
  if (!context) {
    throw new Error('useDevAuth must be used within a DevAuthProvider')
  }
  return context
}

// Hook that mimics useUser from Auth0
export function useDevUser() {
  const { user, isLoading, error } = useDevAuth()
  return { user, isLoading, error }
}

// Hook that provides token
export function useDevToken() {
  const { token } = useDevAuth()
  return token
}
