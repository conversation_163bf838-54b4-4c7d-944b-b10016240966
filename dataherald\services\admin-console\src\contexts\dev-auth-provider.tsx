// Development Authentication Provider - Complete replacement for Auth0 in development mode
import React, { createContext, useContext, useState, ReactNode } from 'react'

interface DevUser {
  sub: string
  email: string
  name: string
  picture: string
  email_verified: boolean
}

// Auth context interface (matches the original AuthContext)
interface DevAuthContextType {
  token: string | null
  fetchToken: () => Promise<void>
}

// User context interface (matches Auth0 useUser)
interface DevUserContextType {
  user: DevUser | null
  isLoading: boolean
  error: null
}

const DevAuthContext = createContext<DevAuthContextType | undefined>(undefined)
const DevUserContext = createContext<DevUserContextType | undefined>(undefined)

const DEV_USER: DevUser = {
  sub: 'dev-user-123',
  email: '<EMAIL>',
  name: 'Development User',
  picture: 'https://via.placeholder.com/150',
  email_verified: true,
}

const DEV_TOKEN = 'dev-token-for-local-development-only'

interface DevAuthProviderProps {
  children: ReactNode
}

export function DevAuthProvider({ children }: DevAuthProviderProps) {
  const [user] = useState<DevUser>(DEV_USER)
  const [isLoading] = useState(false)
  const [error] = useState(null)
  const [token] = useState<string>(DEV_TOKEN)

  const fetchToken = async () => {
    // In development mode, token is always available
    console.log('Development mode: token already available')
  }

  const authContextValue: DevAuthContextType = {
    token,
    fetchToken,
  }

  const userContextValue: DevUserContextType = {
    user,
    isLoading,
    error,
  }

  return (
    <DevUserContext.Provider value={userContextValue}>
      <DevAuthContext.Provider value={authContextValue}>
        {children}
      </DevAuthContext.Provider>
    </DevUserContext.Provider>
  )
}

// Hook that matches the original useAuth interface
export function useAuth(): DevAuthContextType {
  const context = useContext(DevAuthContext)
  if (!context) {
    throw new Error('useAuth must be used within a DevAuthProvider')
  }
  return context
}

// Hook that matches Auth0's useUser interface
export function useUser(): DevUserContextType {
  const context = useContext(DevUserContext)
  if (!context) {
    throw new Error('useUser must be used within a DevAuthProvider')
  }
  return context
}

// Additional hooks for compatibility
export function useDevAuth(): DevAuthContextType {
  return useAuth()
}

export function useDevUser(): DevUserContextType {
  return useUser()
}

export function useDevToken(): string | null {
  const { token } = useAuth()
  return token
}
