NODE_ENV="development" # development | production

# API URL
NEXT_PUBLIC_API_URL='http://localhost:3001' # enterprise API service url

# AUTH 0 CONFIG
AUTH0_BASE_URL='http://localhost:3000' # admin-console url
AUTH0_SECRET=your_auth0_secret_here_use_openssl_rand_hex_32
AUTH0_ISSUER_BASE_URL= # your auth0 issuer url, i.e.: https://auth.dataherald.com/
AUTH0_API_AUDIENCE= # your auth0 API audience, i.e.: https://dataherald.us.auth0.com/api/v2/
AUTH0_CLIENT_ID= # your auth0 application client ID
AUTH0_CLIENT_SECRET= # your auth0 application client secret

# (OPTIONAL) Posthog Analytics 
NEXT_PUBLIC_POSTHOG_DISABLED='true'
NEXT_PUBLIC_POSTHOG_KEY=
NEXT_PUBLIC_POSTHOG_HOST="https://app.posthog.com"


# (OPTIONAL) STRIPE
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
