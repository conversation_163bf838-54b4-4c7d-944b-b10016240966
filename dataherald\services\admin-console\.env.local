NODE_ENV="development" # development | production
# Development mode - skip authentication
SKIP_AUTH=true
NEXT_PUBLIC_SKIP_AUTH=true

# API URL
NEXT_PUBLIC_API_URL='http://localhost:3001' # enterprise API service url

# AUTH 0 CONFIG - DEVELOPMENT MODE
AUTH0_BASE_URL=http://localhost:3000
AUTH0_SECRET=****************************************************************************************************************************************************************
AUTH0_ISSUER_BASE_URL=https://dev-uwr0tgykwzbsc1hz.us.auth0.com
AUTH0_API_AUDIENCE=https://dev-uwr0tgykwzbsc1hz.us.auth0.com/api/v2/
AUTH0_CLIENT_ID=5exQVnEFRWSQItimak76iFF8skkaP61W
AUTH0_CLIENT_SECRET=****************************************************************
AUTH0_SCOPE=openid profile email
# Development bypass flag
AUTH0_DISABLED=true

# (OPTIONAL) Posthog Analytics 
NEXT_PUBLIC_POSTHOG_DISABLED='true'
NEXT_PUBLIC_POSTHOG_KEY=
NEXT_PUBLIC_POSTHOG_HOST="https://app.posthog.com"


# (OPTIONAL) STRIPE
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
