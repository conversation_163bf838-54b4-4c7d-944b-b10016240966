NODE_ENV="development" # development | production

# API URL
NEXT_PUBLIC_API_URL='http://localhost:3001' # enterprise API service url

# AUTH 0 CONFIG
AUTH0_BASE_URL='http://localhost:3000' # admin-console url
AUTH0_SECRET=a_very_long_random_string_for_development_only_32_chars_minimum_length_required
AUTH0_ISSUER_BASE_URL=https://dev-dataherald.us.auth0.com
AUTH0_API_AUDIENCE=https://dataherald-api.dev
AUTH0_CLIENT_ID=dev_client_id_placeholder
AUTH0_CLIENT_SECRET=dev_client_secret_placeholder

# (OPTIONAL) Posthog Analytics 
NEXT_PUBLIC_POSTHOG_DISABLED='true'
NEXT_PUBLIC_POSTHOG_KEY=
NEXT_PUBLIC_POSTHOG_HOST="https://app.posthog.com"


# (OPTIONAL) STRIPE
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
