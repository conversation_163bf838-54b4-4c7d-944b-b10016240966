NODE_ENV="development" # development | production
# Development mode - skip authentication
SKIP_AUTH=true
NEXT_PUBLIC_SKIP_AUTH=true

# API URL
NEXT_PUBLIC_API_URL='http://localhost:3001' # enterprise API service url

# AUTH 0 CONFIG - DEVELOPMENT MODE
AUTH0_BASE_URL=http://localhost:3000
AUTH0_SECRET=****************************************************************************************************************************************************************
AUTH0_ISSUER_BASE_URL=https://dataherald-dev.us.auth0.com
AUTH0_API_AUDIENCE=https://api.dataherald.dev
AUTH0_CLIENT_ID=dataherald_dev_client_id_12345
AUTH0_CLIENT_SECRET=dataherald_dev_client_secret_67890_development_only
AUTH0_SCOPE=openid profile email
# Development bypass flag
AUTH0_DISABLED=true

# (OPTIONAL) Posthog Analytics 
NEXT_PUBLIC_POSTHOG_DISABLED='true'
NEXT_PUBLIC_POSTHOG_KEY=
NEXT_PUBLIC_POSTHOG_HOST="https://app.posthog.com"


# (OPTIONAL) STRIPE
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
