NODE_ENV="development" # development | production

# API URL
NEXT_PUBLIC_API_URL='http://localhost:3001' # enterprise API service url

# AUTH 0 CONFIG
AUTH0_BASE_URL='http://localhost:3000' # admin-console url
AUTH0_SECRET=your_auth0_secret_here_use_openssl_rand_hex_32
AUTH0_ISSUER_BASE_URL=https://dev.auth0.com # Development placeholder
AUTH0_API_AUDIENCE=dataherald-api # Development placeholder
AUTH0_CLIENT_ID=dev_client_id # Development placeholder
AUTH0_CLIENT_SECRET=dev_client_secret # Development placeholder

# (OPTIONAL) Posthog Analytics 
NEXT_PUBLIC_POSTHOG_DISABLED='true'
NEXT_PUBLIC_POSTHOG_KEY=
NEXT_PUBLIC_POSTHOG_HOST="https://app.posthog.com"


# (OPTIONAL) STRIPE
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
