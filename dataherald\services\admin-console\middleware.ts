import { NextRequest, NextResponse } from 'next/server'

export function middleware(request: NextRequest) {
  // In development mode with SKIP_AUTH, bypass all authentication
  if (process.env.NODE_ENV === 'development' && process.env.SKIP_AUTH === 'true') {
    // Allow all requests to pass through without authentication
    return NextResponse.next()
  }

  // For production or when SKIP_AUTH is not enabled, use normal Auth0 middleware
  // This would be where you'd import and use the Auth0 middleware
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
