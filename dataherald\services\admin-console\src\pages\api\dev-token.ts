// Development Token API - Provides tokens without Auth0 in development mode
import { NextApiRequest, NextApiResponse } from 'next'

const DEV_TOKEN = 'dev-token-for-local-development-only'

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only work in development mode
  if (process.env.NODE_ENV !== 'development' || process.env.SKIP_AUTH !== 'true') {
    res.status(404).json({ error: 'Not found' })
    return
  }

  // Return development token
  res.status(200).json(DEV_TOKEN)
}
