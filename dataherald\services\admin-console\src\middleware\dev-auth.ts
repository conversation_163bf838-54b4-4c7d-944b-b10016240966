// Development Authentication Bypass
// This file provides a development-only authentication bypass when AUTH0_DISABLED=true

export const DEV_AUTH_ENABLED = process.env.AUTH0_DISABLED === 'true';

export const DEV_USER = {
  sub: 'dev-user-123',
  email: '<EMAIL>',
  name: 'Development User',
  picture: 'https://via.placeholder.com/150',
  email_verified: true,
};

export const DEV_TOKEN = 'dev-token-for-local-development-only';

export function createDevSession() {
  return {
    user: DEV_USER,
    accessToken: DEV_TOKEN,
    idToken: DEV_TOKEN,
    refreshToken: DEV_TOKEN,
  };
}

export function isDevMode(): boolean {
  return process.env.NODE_ENV === 'development' && DEV_AUTH_ENABLED;
}
