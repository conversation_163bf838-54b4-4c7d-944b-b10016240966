ENGINE_URL= # http://{DOCKER_CONTAINER_NAME:PORT}/api/v1 -- example: http://engine/api/v1 (port 80 by default)
MONGODB_DB_NAME=dataherald
MONGODB_URI=***********************************

#The encryption key should be the same as the one used in engine
ENCRYPT_KEY=

AUTH0_DOMAIN= # your auth0 domain, i.e.: auth.dataherald.com
AUTH0_ISSUER_BASE_URL= # your auth0 issuer url, i.e.: https://auth.dataherald.com/
AUTH0_API_AUDIENCE= # your auth0 API audience, i.e.: https://dataherald.us.auth0.com/api/v2/
# AUTH0_DISABLED creates a mock authentication token type for testing purposes
# you can provide an email as the bearer token, note that the admin console will not be able to authenticate
AUTH0_DISABLED=False

# The salt is used to hash the API key, use the string from ENCRYPT_KEY or create a different one
API_KEY_SALT=

DEFAULT_ENGINE_TIMEOUT=120

# Used to store credential files for database connections
# should be the same as the ones in engine
S3_AWS_ACCESS_KEY_ID=
S3_AWS_SECRET_ACCESS_KEY=

POSTHOG_DISABLED=True
# Optional posthog analytics if disabled
POSTHOG_API_KEY=
POSTHOG_HOST=



# Optional ssh credentials if connecting to a remote database using ssh tunnel
SSH_PRIVATE_KEY_PASSWORD=
SSH_PATH_TO_CREDENTIAL_FILE=

STRIPE_DISABLED=True
# Optional stripe env vars if STRIPE_DISABLED is set to False
# Otherwise you would need to create a new stripe account and fillout the env vars below
STRIPE_API_KEY=
STRIPE_WEBHOOK_SECRET=
SQL_GENERATION_PRICE_ID=
FINETUNING_GPT_35_PRICE_ID=
FINETUNING_GPT_4_PRICE_ID=
DEFAULT_SPENDING_LIMIT=
SINGUP_CREDITS=
SQL_GENERATION_COST=
FINETUNING_GPT_35_COST=
FINETUNING_GPT_4_COST=
