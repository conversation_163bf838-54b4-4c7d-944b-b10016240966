Introduction
==============

Dataherald is a natural language-to-SQL engine built for enteprise-level question answering over structured data. It allows you to set up an API from your database that can answer questions in plain English.

You can use Dataherald to:

* Allow business users to get insights from the data warehouse without going through a data analyst.
* Enable Q+A from your production DBs inside your SaaS application.
* Create a ChatGPT plug-in from your proprietary data.


Dataherald is built to:

* 🔌 Be modular, allowing different implementations of core modules to be plugged-in
* 🔋 Come batteries included: Have best-in-class implementations for modules like text to SQL, evaluation   
* 📀 Be easy to set-up and use with major data warehouses 
* 👨‍🏫 Allow for Active Learning, allowing you to improve the performance with usage 
* 🏎️ Be fast 