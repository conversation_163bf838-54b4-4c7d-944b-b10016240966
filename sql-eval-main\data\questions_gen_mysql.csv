db_name,db_type,query_category,query,question,instructions
academic,mysql,group_by,"SELECT author.name FROM author WHERE author.aid IN (SELECT domain_author.aid FROM domain_author WHERE domain_author.did IN (SELECT domain.did FROM domain WHERE domain.name IN ('Machine Learning', 'Data Science')) GROUP BY 1 HAVING COUNT(DISTINCT domain_author.did) = 2);SELECT author.aid FROM author WHERE author.aid IN (SELECT domain_author.aid FROM domain_author WHERE domain_author.did IN (SELECT domain.did FROM domain WHERE domain.name IN ('Machine Learning', 'Data Science')) GROUP BY 1 HAVING COUNT(DISTINCT domain_author.did) = 2);SELECT author.name, author.aid FROM author WHERE author.aid IN (SELECT domain_author.aid FROM domain_author WHERE domain_author.did IN (SELECT domain.did FROM domain WHERE domain.name IN ('Machine Learning', 'Data Science')) GROUP BY 1 HAVING COUNT(DISTINCT domain_author.did) = 2);","Which authors have written publications in both the domain ""Machine Learning"" and the domain ""Data Science""?",
academic,mysql,group_by,"SELECT author.name, SUM(publication.citation_num) AS total_citations FROM author JOIN writes ON author.aid = writes.aid JOIN publication ON writes.pid = publication.pid GROUP BY author.name ORDER BY total_citations DESC;SELECT author.aid, SUM(publication.citation_num) AS total_citations FROM author JOIN writes ON author.aid = writes.aid JOIN publication ON writes.pid = publication.pid GROUP BY author.aid ORDER BY total_citations DESC;SELECT author.name, author.aid, SUM(publication.citation_num) AS total_citations FROM author JOIN writes ON author.aid = writes.aid JOIN publication ON writes.pid = publication.pid GROUP BY author.name, author.aid ORDER BY total_citations DESC;SELECT a.aid, COUNT(c.cited) AS total_citations FROM author AS a JOIN writes AS w ON a.aid = w.aid JOIN publication AS p ON w.pid = p.pid JOIN cite AS c ON p.pid = c.cited GROUP BY a.aid ORDER BY CASE WHEN total_citations IS NULL THEN 1 ELSE 0 END DESC, total_citations DESC;SELECT a.name, COUNT(c.cited) AS total_citations FROM author AS a JOIN writes AS w ON a.aid = w.aid JOIN publication AS p ON w.pid = p.pid JOIN cite AS c ON p.pid = c.cited GROUP BY a.name ORDER BY CASE WHEN total_citations IS NULL THEN 1 ELSE 0 END DESC, total_citations DESC;SELECT a.aid, a.name, COUNT(c.cited) AS total_citations FROM author AS a JOIN writes AS w ON a.aid = w.aid JOIN publication AS p ON w.pid = p.pid JOIN cite AS c ON p.pid = c.cited GROUP BY a.aid, a.name ORDER BY CASE WHEN total_citations IS NULL THEN 1 ELSE 0 END DESC, total_citations DESC;",What is the total number of citations received by each author?,
academic,mysql,group_by,"SELECT publication.year, COUNT(DISTINCT publication.pid) AS total_publications FROM publication GROUP BY publication.year ORDER BY CASE WHEN publication.year IS NULL THEN 1 ELSE 0 END, publication.year;",What is the total number of publications published in each year?,
academic,mysql,group_by,"SELECT domain.name, AVG(publication.reference_num) AS average_references FROM domain_publication JOIN publication ON domain_publication.pid = publication.pid JOIN domain ON domain.did = domain_publication.did GROUP BY domain.name;SELECT domain.did, AVG(publication.reference_num) AS average_references FROM domain_publication JOIN publication ON domain_publication.pid = publication.pid JOIN domain ON domain.did = domain_publication.did GROUP BY domain.did;SELECT domain.name, domain.did, AVG(publication.reference_num) AS average_references FROM domain_publication JOIN publication ON domain_publication.pid = publication.pid JOIN domain ON domain.did = domain_publication.did GROUP BY domain.name, domain.did;",What is the average number of references cited by publications in each domain name?,
academic,mysql,group_by,"SELECT publication.year, AVG(publication.citation_num) AS average_citations FROM publication GROUP BY publication.year ORDER BY CASE WHEN publication.year IS NULL THEN 1 ELSE 0 END, publication.year;",What is the average number of citations received by publications in each year?,
academic,mysql,order_by,SELECT publication.title FROM publication ORDER BY publication.citation_num DESC LIMIT 1;,What is the title of the publication that has received the highest number of citations?,
academic,mysql,order_by,"SELECT d.name, COUNT(DISTINCT a.aid) AS author_count FROM author AS a JOIN domain_author AS da ON a.aid = da.aid JOIN domain AS d ON da.did = d.did GROUP BY d.name ORDER BY CASE WHEN author_count IS NULL THEN 1 ELSE 0 END DESC, author_count DESC LIMIT 5;SELECT d.did, COUNT(DISTINCT a.aid) AS author_count FROM author AS a JOIN domain_author AS da ON a.aid = da.aid JOIN domain AS d ON da.did = d.did GROUP BY d.did ORDER BY CASE WHEN author_count IS NULL THEN 1 ELSE 0 END DESC, author_count DESC LIMIT 5;SELECT d.name, d.did, COUNT(DISTINCT a.aid) AS author_count FROM author AS a JOIN domain_author AS da ON a.aid = da.aid JOIN domain AS d ON da.did = d.did GROUP BY d.name, d.did ORDER BY CASE WHEN author_count IS NULL THEN 1 ELSE 0 END DESC, author_count DESC LIMIT 5;",What are the top 5 domains with the highest number of authors associated with them?,
academic,mysql,order_by,"SELECT publication.title FROM publication ORDER BY CASE WHEN publication.reference_num IS NULL THEN 1 ELSE 0 END DESC, publication.reference_num DESC LIMIT 3;","What are the top 3 titles of the publications that have the highest number of references cited, ordered by the number of references cited in descending order?",
academic,mysql,order_by,"SELECT publication.title, publication.citation_num FROM publication ORDER BY CASE WHEN publication.citation_num IS NULL THEN 1 ELSE 0 END DESC, publication.citation_num DESC LIMIT 3;SELECT publication.pid, publication.citation_num FROM publication ORDER BY CASE WHEN publication.citation_num IS NULL THEN 1 ELSE 0 END DESC, publication.citation_num DESC LIMIT 3;SELECT publication.title, publication.pid, publication.citation_num FROM publication ORDER BY CASE WHEN publication.citation_num IS NULL THEN 1 ELSE 0 END DESC, publication.citation_num DESC LIMIT 3;",What are the top 3 publications with the highest number of citations?,
academic,mysql,order_by,"SELECT DISTINCT publication.title FROM publication ORDER BY CASE WHEN publication.title IS NULL THEN 1 ELSE 0 END, publication.title ASC;",What are the titles of all publications ordered alphabetically?,
academic,mysql,ratio,"SELECT CAST(COUNT(DISTINCT publication.pid) AS DOUBLE) / NULLIF(COUNT(DISTINCT author.aid), 0) AS publication_to_author_ratio FROM publication, author;",What is the ratio of publications to authors in the database?,
academic,mysql,ratio,"SELECT CAST(COUNT(DISTINCT CASE WHEN NOT cid IS NULL THEN pid END) AS DOUBLE) / NULLIF(COUNT(DISTINCT CASE WHEN NOT jid IS NULL THEN pid END), 0) AS ratio FROM publication;",What is the ratio of publications presented in conferences to publications published in journals?,
academic,mysql,ratio,"SELECT domain_publication.did, CAST(COUNT(DISTINCT domain_publication.pid) AS DOUBLE) / NULLIF(COUNT(DISTINCT domain_keyword.kid), 0) AS publication_to_keyword_ratio FROM domain_publication LEFT JOIN domain_keyword ON domain_publication.did = domain_keyword.did GROUP BY domain_publication.did ORDER BY publication_to_keyword_ratio DESC;SELECT domain_publication.did, CAST(COUNT(DISTINCT domain_publication.pid) AS DOUBLE) / NULLIF(COUNT(DISTINCT domain_keyword.kid), 0) AS publication_to_keyword_ratio FROM domain_keyword LEFT JOIN domain_publication ON domain_publication.did = domain_keyword.did GROUP BY domain_publication.did ORDER BY publication_to_keyword_ratio DESC;SELECT d.did, COALESCE(CAST(COUNT(DISTINCT dp.pid) AS DOUBLE) / NULLIF(COUNT(DISTINCT dk.kid), 0), 0) AS publication_to_keyword_ratio FROM domain AS d LEFT JOIN domain_publication AS dp ON d.did = dp.did LEFT JOIN domain_keyword AS dk ON d.did = dk.did GROUP BY d.did ORDER BY publication_to_keyword_ratio DESC;",What is the ratio of the total number of publications to the total number of keywords within each domain ID? Show all domain IDs.,
academic,mysql,ratio,"SELECT publication.year, COUNT(DISTINCT publication.pid) AS num_publications, COUNT(DISTINCT publication.jid) AS num_journals, CAST(COUNT(DISTINCT publication.pid) AS DOUBLE) / NULLIF(COUNT(DISTINCT publication.jid), 0) AS ratio FROM publication GROUP BY publication.year ORDER BY CASE WHEN publication.year IS NULL THEN 1 ELSE 0 END, publication.year;",How does the ratio of publications to journals change over the years? Return the annual numbers of publications and journals as well.,
academic,mysql,ratio,"SELECT organization.continent, CAST(COUNT(DISTINCT author.aid) AS DOUBLE) / NULLIF(COUNT(DISTINCT organization.oid), 0) AS ratio FROM organization LEFT JOIN author ON author.oid = organization.oid GROUP BY organization.continent ORDER BY ratio DESC;WITH author_counts AS (SELECT o.continent, COUNT(DISTINCT a.aid) AS author_count FROM author AS a JOIN organization AS o ON a.oid = o.oid GROUP BY o.continent), organization_counts AS (SELECT o.continent, COUNT(DISTINCT o.oid) AS organization_count FROM organization AS o GROUP BY o.continent) SELECT ac.continent, CAST(ac.author_count AS FLOAT) / NULLIF(oc.organization_count, 0) AS author_to_organization_ratio FROM author_counts AS ac JOIN organization_counts AS oc ON ac.continent = oc.continent ORDER BY author_to_organization_ratio DESC;",How does the ratio of authors to organizations differ by continent?,
academic,mysql,table_join,"SELECT author.name, COUNT(publication.pid) AS publication_count FROM writes JOIN author ON writes.aid = author.aid JOIN publication ON writes.pid = publication.pid WHERE publication.year = 2021 GROUP BY author.name ORDER BY publication_count DESC LIMIT 1;SELECT author.aid, COUNT(publication.pid) AS publication_count FROM writes JOIN author ON writes.aid = author.aid JOIN publication ON writes.pid = publication.pid WHERE publication.year = 2021 GROUP BY author.aid ORDER BY publication_count DESC LIMIT 1;SELECT author.name, author.aid, COUNT(publication.pid) AS publication_count FROM writes JOIN author ON writes.aid = author.aid JOIN publication ON writes.pid = publication.pid WHERE publication.year = 2021 GROUP BY author.name, author.aid ORDER BY publication_count DESC LIMIT 1;",Which author had the most publications in the year 2021 and how many publications did he/she have that year?,
academic,mysql,table_join,"SELECT conference.name, COUNT(publication.pid) AS total_publications FROM publication JOIN conference ON publication.cid = conference.cid GROUP BY conference.name ORDER BY CASE WHEN total_publications IS NULL THEN 1 ELSE 0 END DESC, total_publications DESC;SELECT conference.cid, COUNT(publication.pid) AS total_publications FROM publication JOIN conference ON publication.cid = conference.cid GROUP BY conference.cid ORDER BY CASE WHEN total_publications IS NULL THEN 1 ELSE 0 END DESC, total_publications DESC;SELECT conference.name, conference.cid, COUNT(publication.pid) AS total_publications FROM publication JOIN conference ON publication.cid = conference.cid GROUP BY conference.name, conference.cid ORDER BY CASE WHEN total_publications IS NULL THEN 1 ELSE 0 END DESC, total_publications DESC;",What is the total number of publications presented in each conference?,
academic,mysql,table_join,"SELECT journal.name, COUNT(publication.pid) AS total_publications FROM publication JOIN journal ON publication.jid = journal.jid GROUP BY journal.name ORDER BY total_publications DESC;SELECT journal.jid, COUNT(publication.pid) AS total_publications FROM publication JOIN journal ON publication.jid = journal.jid GROUP BY journal.jid ORDER BY total_publications DESC;SELECT journal.name, journal.jid, COUNT(publication.pid) AS total_publications FROM publication JOIN journal ON publication.jid = journal.jid GROUP BY journal.name, journal.jid ORDER BY total_publications DESC;SELECT journal.name, COUNT(publication.pid) AS total_publications FROM journal LEFT JOIN publication ON journal.jid = publication.jid GROUP BY journal.name ORDER BY total_publications DESC;SELECT journal.jid, COUNT(publication.pid) AS total_publications FROM journal LEFT JOIN publication ON journal.jid = publication.jid GROUP BY journal.jid ORDER BY total_publications DESC;SELECT journal.name, journal.jid, COUNT(publication.pid) AS total_publications FROM journal LEFT JOIN publication ON journal.jid = publication.jid GROUP BY journal.name, journal.jid ORDER BY total_publications DESC;","What is the total number of publications in each journal, ordered by the number of publications in descending order?",
academic,mysql,table_join,"SELECT conference.name, COUNT(publication.pid) AS num_publications FROM publication JOIN conference ON publication.cid = conference.cid GROUP BY conference.name, conference.cid ORDER BY num_publications DESC;","How many publications were presented at each conference, ordered by the number of publications in descending order? Give the names of the conferences and their corresponding number of publications.",
academic,mysql,table_join,SELECT COUNT(DISTINCT publication.pid) FROM publication JOIN journal ON publication.jid = journal.jid WHERE LOWER(journal.name) LIKE LOWER('J%');,"How many publications were published in journals whose names start with the letter ""J""?",
academic,mysql,instruct,"SELECT DISTINCT organization.name FROM organization JOIN author ON organization.oid = author.oid JOIN writes ON author.aid = writes.aid JOIN domain_publication ON writes.pid = domain_publication.pid JOIN domain ON domain_publication.did = domain.did WHERE domain.name = 'Machine Learning';SELECT DISTINCT organization.oid FROM organization JOIN author ON organization.oid = author.oid JOIN writes ON author.aid = writes.aid JOIN domain_publication ON writes.pid = domain_publication.pid JOIN domain ON domain_publication.did = domain.did WHERE domain.name = 'Machine Learning';SELECT DISTINCT organization.name, organization.oid FROM organization JOIN author ON organization.oid = author.oid JOIN writes ON author.aid = writes.aid JOIN domain_publication ON writes.pid = domain_publication.pid JOIN domain ON domain_publication.did = domain.did WHERE domain.name = 'Machine Learning';","Which organizations have authors who have written publications in the domain ""Machine Learning""?",Always filter names using an exact match
academic,mysql,instruct,"SELECT DISTINCT a2.name FROM author AS a1 JOIN domain_author AS da1 ON a1.aid = da1.aid JOIN domain_author AS da2 ON da1.did = da2.did JOIN author AS a2 ON da2.aid = a2.aid WHERE LOWER(LOWER(a1.name)) LIKE LOWER('%martin%');SELECT DISTINCT a2.aid FROM author AS a1 JOIN domain_author AS da1 ON a1.aid = da1.aid JOIN domain_author AS da2 ON da1.did = da2.did JOIN author AS a2 ON da2.aid = a2.aid WHERE LOWER(LOWER(a1.name)) LIKE LOWER('%martin%');SELECT DISTINCT a2.name, a2.aid FROM author AS a1 JOIN domain_author AS da1 ON a1.aid = da1.aid JOIN domain_author AS da2 ON da1.did = da2.did JOIN author AS a2 ON da2.aid = a2.aid WHERE LOWER(LOWER(a1.name)) LIKE LOWER('%martin%');",Which authors belong to the same domain as Martin?,Always filter names using LIKE with percent sign wildcards
academic,mysql,instruct,"SELECT DISTINCT name FROM author WHERE oid IS NULL;SELECT DISTINCT aid FROM author WHERE oid IS NULL;SELECT DISTINCT name, aid FROM author WHERE oid IS NULL;",Which authors are not part of any organization?,Always filter names using LIKE
academic,mysql,instruct,"SELECT DISTINCT publication.title FROM domain JOIN domain_author ON domain.did = domain_author.did JOIN writes ON domain_author.aid = writes.aid JOIN publication ON writes.pid = publication.pid JOIN domain_conference ON domain.did = domain_conference.did WHERE LOWER(domain.name) LIKE LOWER('%Sociology%') AND publication.year = 2020 AND publication.cid = domain_conference.cid;SELECT DISTINCT publication.pid FROM domain JOIN domain_author ON domain.did = domain_author.did JOIN writes ON domain_author.aid = writes.aid JOIN publication ON writes.pid = publication.pid JOIN domain_conference ON domain.did = domain_conference.did WHERE LOWER(domain.name) LIKE LOWER('%Sociology%') AND publication.year = 2020 AND publication.cid = domain_conference.cid;SELECT DISTINCT publication.title, publication.pid FROM domain JOIN domain_author ON domain.did = domain_author.did JOIN writes ON domain_author.aid = writes.aid JOIN publication ON writes.pid = publication.pid JOIN domain_conference ON domain.did = domain_conference.did WHERE LOWER(domain.name) LIKE LOWER('%Sociology%') AND publication.year = 2020 AND publication.cid = domain_conference.cid;",What are the publications written by authors from the 'Sociology' domain and presented at conferences in the year 2020?,"To get publications written by authors from a given domain, you would need to join domain, domain_author, author to link the domain to the author first, and then join with write to link with the publication id. Finally, to see which ones were presented at conferences, you must join the domain table with the domain_conference table. You must also filter names using LIKE."
academic,mysql,instruct,SELECT DISTINCT author.name FROM author JOIN writes ON author.aid = writes.aid JOIN publication ON writes.pid = publication.pid JOIN domain_publication ON publication.pid = domain_publication.pid JOIN domain ON domain_publication.did = domain.did WHERE LOWER(domain.name) LIKE LOWER('%computer%science%');,"What are the names of the authors who have written publications in the domain ""Computer Science""?","To get publications written by authors from a given domain, you would need to join domain, domain_author, author to link the domain to the author first, and then join with write to link with the publication id. You must also filter names using LIKE."
advising,mysql,date_functions,"SELECT DATE_ADD('0000-01-01 00:00:00', INTERVAL (TIMESTAMPDIFF(MONTH, '0000-01-01 00:00:00', s.admit_term)) MONTH) AS month, COUNT(*) AS total_students FROM student AS s GROUP BY MONTH ORDER BY CASE WHEN total_students IS NULL THEN 1 ELSE 0 END DESC, total_students DESC LIMIT 1;SELECT DATE_FORMAT(DATE_ADD('0000-01-01 00:00:00', INTERVAL (TIMESTAMPDIFF(MONTH, '0000-01-01 00:00:00', s.admit_term)) MONTH), '%Y-%m') AS month, COUNT(*) AS total_students FROM student AS s GROUP BY month ORDER BY CASE WHEN total_students IS NULL THEN 1 ELSE 0 END DESC, total_students DESC LIMIT 1;",What month were most students admitted? Return the no. of students and the month as a date,
advising,mysql,date_functions,SELECT AVG(predicted_graduation_semester - admit_term) AS average_predicted_time_to_graduation FROM student;,What's the average predicted time to graduation since admission in no. of days?,
advising,mysql,date_functions,"SELECT COUNT(*) AS num_students_graduated FROM student WHERE predicted_graduation_semester >= DATE_ADD('0000-01-01 00:00:00', INTERVAL (TIMESTAMPDIFF(YEAR, '0000-01-01 00:00:00', CURRENT_DATE)) YEAR) - INTERVAL '10' YEAR;",How many students were predicted to graduate in the last 10 years?,
advising,mysql,date_functions,"SELECT CAST(DATEDIFF(CURRENT_DATE, MAX(admit_term)) AS SIGNED) AS duration_since_last_admitted_student FROM student;",How long has it been in days since the last admitted student? Give the answer as an integer.,
advising,mysql,date_functions,SELECT DISTINCT co.course_id FROM advising.course_offering AS co JOIN advising.offering_instructor AS oi ON co.offering_id = oi.offering_id WHERE (co.semester = 1 OR co.semester = 2) AND co.end_time < '13:00:00' AND co.thursday IS NOT NULL;,Return the course id's that are offered in either semesters 1 or 2 and ends before 1pm and had an instructor on thursday,
advising,mysql,group_by,"SELECT course_tags_count.course_id, SUM(course_tags_count.hilarious) AS total_hilarious FROM course_tags_count GROUP BY course_tags_count.course_id;",What is the total number of students who found the instructor to be hilarious per course id?,
advising,mysql,group_by,"SELECT i.name, AVG(c.clarity_score) FROM course AS c JOIN course_offering AS co ON c.course_id = co.course_id JOIN offering_instructor AS oi ON co.offering_id = oi.offering_id JOIN instructor AS i ON oi.instructor_id = i.instructor_id GROUP BY i.name;SELECT i.instructor_id, AVG(c.clarity_score) FROM course AS c JOIN course_offering AS co ON c.course_id = co.course_id JOIN offering_instructor AS oi ON co.offering_id = oi.offering_id JOIN instructor AS i ON oi.instructor_id = i.instructor_id GROUP BY i.instructor_id;SELECT i.name, i.instructor_id, AVG(c.clarity_score) FROM course AS c JOIN course_offering AS co ON c.course_id = co.course_id JOIN offering_instructor AS oi ON co.offering_id = oi.offering_id JOIN instructor AS i ON oi.instructor_id = i.instructor_id GROUP BY i.name, i.instructor_id;",What is the average clarity score for each instructor who taught a course?,
advising,mysql,group_by,"SELECT course_offering.has_final_exam, COUNT(offering_id) AS num_courses FROM course_offering GROUP BY course_offering.has_final_exam;SELECT COUNT(CASE WHEN co.has_final_exam THEN 1 END) AS num_with_final_exam, COUNT(CASE WHEN NOT co.has_final_exam THEN 1 END) AS num_without_final_exam FROM course_offering AS co;",How many course offerings have a final exam and how many do not?,
advising,mysql,group_by,"SELECT course.department, COUNT(DISTINCT course.course_id) AS num_courses FROM course GROUP BY course.department ORDER BY num_courses DESC;",How many courses does each department offer?,
advising,mysql,group_by,"SELECT course_offering.semester, COUNT(DISTINCT course_offering.course_id) AS num_courses FROM course_offering GROUP BY course_offering.semester ORDER BY CASE WHEN course_offering.semester IS NULL THEN 1 ELSE 0 END, course_offering.semester;SELECT semester.semester_id, COUNT(DISTINCT course_offering.course_id) AS num_courses FROM semester LEFT JOIN course_offering ON semester.semester_id = course_offering.semester GROUP BY semester.semester_id;",How many courses are offered for each semester id?,
advising,mysql,order_by,"SELECT course.name, course.num_enrolled FROM course ORDER BY course.num_enrolled DESC LIMIT 1;SELECT course.course_id, course.num_enrolled FROM course ORDER BY course.num_enrolled DESC LIMIT 1;SELECT course.number, course.num_enrolled FROM course ORDER BY course.num_enrolled DESC LIMIT 1;SELECT course.name, course.course_id, course.num_enrolled FROM course ORDER BY course.num_enrolled DESC LIMIT 1;SELECT course.name, course.number, course.num_enrolled FROM course ORDER BY course.num_enrolled DESC LIMIT 1;SELECT course.course_id, course.number, course.num_enrolled FROM course ORDER BY course.num_enrolled DESC LIMIT 1;SELECT course.name, course.course_id, course.number, course.num_enrolled FROM course ORDER BY course.num_enrolled DESC LIMIT 1;","Which course has the highest number of enrolled students, and what is the enrollment number?",
advising,mysql,order_by,"SELECT course_tags_count.course_id, course_tags_count.participation FROM course_tags_count ORDER BY course_tags_count.participation DESC;","What is the total number of students who participated actively for each course id, ordered from highest to lowest participants?",
advising,mysql,order_by,"SELECT course.course_id, SUM(course.num_enrolled) AS total_students FROM course GROUP BY course.course_id ORDER BY total_students DESC;SELECT course.name, SUM(course.num_enrolled) AS total_students FROM course GROUP BY course.name ORDER BY total_students DESC;SELECT course.number, SUM(course.num_enrolled) AS total_students FROM course GROUP BY course.number ORDER BY total_students DESC;SELECT course.course_id, course.name, SUM(course.num_enrolled) AS total_students FROM course GROUP BY course.course_id, course.name ORDER BY total_students DESC;SELECT course.course_id, course.number, SUM(course.num_enrolled) AS total_students FROM course GROUP BY course.course_id, course.number ORDER BY total_students DESC;SELECT course.name, course.number, SUM(course.num_enrolled) AS total_students FROM course GROUP BY course.name, course.number ORDER BY total_students DESC;SELECT course.course_id, course.name, course.number, SUM(course.num_enrolled) AS total_students FROM course GROUP BY course.course_id, course.name, course.number ORDER BY total_students DESC;","What is the total number of students enrolled in each course, ordered from highest to lowest?",
advising,mysql,order_by,"SELECT student.student_id, student.total_credit FROM student ORDER BY student.total_credit DESC;","What is the total number of credits earned by each student, ordered from highest to lowest? Give the student id and the total number of credits.",
advising,mysql,order_by,"SELECT instructor.name, COUNT(offering_instructor.offering_id) AS num_courses FROM offering_instructor JOIN instructor ON offering_instructor.instructor_id = instructor.instructor_id GROUP BY instructor.name ORDER BY CASE WHEN num_courses IS NULL THEN 1 ELSE 0 END DESC, num_courses DESC LIMIT 1;","What is the name of the instructor who has taught the most courses, and how many courses have they taught?",
advising,mysql,ratio,SELECT CAST(SUM(CASE WHEN c.has_exams THEN c.num_enrolled ELSE 0 END) AS DOUBLE) / SUM(CASE WHEN NOT c.has_exams THEN c.num_enrolled ELSE 0 END) AS ratio FROM course AS c;,What is the ratio of the total number of students enrolled in courses with exams to the total number of students enrolled in courses without exams?,
advising,mysql,ratio,"SELECT course_tags_count.course_id, CAST(course_tags_count.clear_grading AS DOUBLE) / NULLIF(course_tags_count.good_feedback, 0) AS ratio FROM course_tags_count ORDER BY CASE WHEN course_tags_count.course_id IS NULL THEN 1 ELSE 0 END, course_tags_count.course_id;",What is the ratio of the number of students who found the grading criteria clear and easy to understand to the number of students who received good feedback from the instructor for each course id?,
advising,mysql,ratio,"SELECT course_offering.semester, CAST(SUM(CASE WHEN course.has_projects THEN 1 ELSE 0 END) AS DOUBLE) / NULLIF(SUM(CASE WHEN course.has_exams THEN 1 ELSE 0 END), 0) AS ratio FROM course JOIN course_offering ON course.course_id = course_offering.course_id GROUP BY course_offering.semester ORDER BY CASE WHEN course_offering.semester IS NULL THEN 1 ELSE 0 END, course_offering.semester;",What is the ratio of the number of courses with projects to the number of courses with exams in each semester id?,
advising,mysql,ratio,"SELECT course.course_id, CAST(course.helpfulness_score AS DOUBLE) / NULLIF(course.clarity_score, 0) AS ratio FROM course;",What is the ratio of helpfulness scores to clarity scores for each course ID?,
advising,mysql,ratio,"SELECT course.course_id, CAST(course.num_enrolled AS DOUBLE) / NULLIF(course.num_reviews, 0) AS student_review_ratio FROM course ORDER BY CASE WHEN student_review_ratio IS NULL THEN 1 ELSE 0 END, student_review_ratio;SELECT course.name, CAST(course.num_enrolled AS DOUBLE) / NULLIF(course.num_reviews, 0) AS student_review_ratio FROM course ORDER BY CASE WHEN student_review_ratio IS NULL THEN 1 ELSE 0 END, student_review_ratio;SELECT course.number, CAST(course.num_enrolled AS DOUBLE) / NULLIF(course.num_reviews, 0) AS student_review_ratio FROM course ORDER BY CASE WHEN student_review_ratio IS NULL THEN 1 ELSE 0 END, student_review_ratio;SELECT course.course_id, course.name, CAST(course.num_enrolled AS DOUBLE) / NULLIF(course.num_reviews, 0) AS student_review_ratio FROM course ORDER BY CASE WHEN student_review_ratio IS NULL THEN 1 ELSE 0 END, student_review_ratio;SELECT course.course_id, course.number, CAST(course.num_enrolled AS DOUBLE) / NULLIF(course.num_reviews, 0) AS student_review_ratio FROM course ORDER BY CASE WHEN student_review_ratio IS NULL THEN 1 ELSE 0 END, student_review_ratio;SELECT course.name, course.number, CAST(course.num_enrolled AS DOUBLE) / NULLIF(course.num_reviews, 0) AS student_review_ratio FROM course ORDER BY CASE WHEN student_review_ratio IS NULL THEN 1 ELSE 0 END, student_review_ratio;SELECT course.course_id, course.name, course.number, CAST(course.num_enrolled AS DOUBLE) / NULLIF(course.num_reviews, 0) AS student_review_ratio FROM course ORDER BY CASE WHEN student_review_ratio IS NULL THEN 1 ELSE 0 END, student_review_ratio;",How does the ratio of enrolled students to the number of reviews vary across different courses?,
advising,mysql,table_join,"SELECT DISTINCT course.name AS course_name FROM student JOIN student_record ON student.student_id = student_record.student_id JOIN program ON student.program_id = program.program_id JOIN course ON student_record.course_id = course.course_id WHERE LOWER(program.name) LIKE LOWER('%Computer Science%');SELECT DISTINCT course.course_id AS course_name FROM student JOIN student_record ON student.student_id = student_record.student_id JOIN program ON student.program_id = program.program_id JOIN course ON student_record.course_id = course.course_id WHERE LOWER(program.name) LIKE LOWER('%Computer Science%');SELECT DISTINCT course.number AS course_name FROM student JOIN student_record ON student.student_id = student_record.student_id JOIN program ON student.program_id = program.program_id JOIN course ON student_record.course_id = course.course_id WHERE LOWER(program.name) LIKE LOWER('%Computer Science%');SELECT DISTINCT course.name, course.course_id AS course_name FROM student JOIN student_record ON student.student_id = student_record.student_id JOIN program ON student.program_id = program.program_id JOIN course ON student_record.course_id = course.course_id WHERE LOWER(program.name) LIKE LOWER('%Computer Science%');SELECT DISTINCT course.name, course.number AS course_name FROM student JOIN student_record ON student.student_id = student_record.student_id JOIN program ON student.program_id = program.program_id JOIN course ON student_record.course_id = course.course_id WHERE LOWER(program.name) LIKE LOWER('%Computer Science%');SELECT DISTINCT course.course_id, course.number AS course_name FROM student JOIN student_record ON student.student_id = student_record.student_id JOIN program ON student.program_id = program.program_id JOIN course ON student_record.course_id = course.course_id WHERE LOWER(program.name) LIKE LOWER('%Computer Science%');SELECT DISTINCT course.name, course.course_id, course.number AS course_name FROM student JOIN student_record ON student.student_id = student_record.student_id JOIN program ON student.program_id = program.program_id JOIN course ON student_record.course_id = course.course_id WHERE LOWER(program.name) LIKE LOWER('%Computer Science%');",Which courses have been taken by students in the Computer Science program?,
advising,mysql,table_join,"SELECT DISTINCT course.name FROM course_offering JOIN course ON course_offering.course_id = course.course_id WHERE course_offering.has_final_project = TRUE AND course_offering.has_final_exam = TRUE;SELECT DISTINCT course.course_id FROM course_offering JOIN course ON course_offering.course_id = course.course_id WHERE course_offering.has_final_project = TRUE AND course_offering.has_final_exam = TRUE;SELECT DISTINCT course.number FROM course_offering JOIN course ON course_offering.course_id = course.course_id WHERE course_offering.has_final_project = TRUE AND course_offering.has_final_exam = TRUE;SELECT DISTINCT course.name, course.course_id FROM course_offering JOIN course ON course_offering.course_id = course.course_id WHERE course_offering.has_final_project = TRUE AND course_offering.has_final_exam = TRUE;SELECT DISTINCT course.name, course.number FROM course_offering JOIN course ON course_offering.course_id = course.course_id WHERE course_offering.has_final_project = TRUE AND course_offering.has_final_exam = TRUE;SELECT DISTINCT course.course_id, course.number FROM course_offering JOIN course ON course_offering.course_id = course.course_id WHERE course_offering.has_final_project = TRUE AND course_offering.has_final_exam = TRUE;SELECT DISTINCT course.name, course.course_id, course.number FROM course_offering JOIN course ON course_offering.course_id = course.course_id WHERE course_offering.has_final_project = TRUE AND course_offering.has_final_exam = TRUE;",Which courses have a final project and a final exam?,
advising,mysql,table_join,SELECT COUNT(DISTINCT student_record.student_id) AS total_students FROM student_record JOIN course_offering ON student_record.course_id = course_offering.course_id WHERE course_offering.has_final_project = TRUE OR course_offering.has_final_exam = TRUE;,What is the total number of students who have taken a course with a final project or exam?,
advising,mysql,table_join,"SELECT program.name, SUM(student.total_credit) AS total_credits FROM student JOIN program ON student.program_id = program.program_id GROUP BY program.name;SELECT program.program_id, SUM(student.total_credit) AS total_credits FROM student JOIN program ON student.program_id = program.program_id GROUP BY program.program_id;SELECT program.name, program.program_id, SUM(student.total_credit) AS total_credits FROM student JOIN program ON student.program_id = program.program_id GROUP BY program.name, program.program_id;",What is the total number of credits earned by students in each program?,
advising,mysql,table_join,"SELECT program.name, COUNT(student.student_id) AS number_of_students FROM student JOIN program ON student.program_id = program.program_id WHERE NOT student.declare_major IS NULL GROUP BY program.name ORDER BY CASE WHEN number_of_students IS NULL THEN 1 ELSE 0 END DESC, number_of_students DESC;SELECT program.program_id, COUNT(student.student_id) AS number_of_students FROM student JOIN program ON student.program_id = program.program_id WHERE NOT student.declare_major IS NULL GROUP BY program.program_id ORDER BY CASE WHEN number_of_students IS NULL THEN 1 ELSE 0 END DESC, number_of_students DESC;SELECT program.name, program.program_id, COUNT(student.student_id) AS number_of_students FROM student JOIN program ON student.program_id = program.program_id WHERE NOT student.declare_major IS NULL GROUP BY program.name, program.program_id ORDER BY CASE WHEN number_of_students IS NULL THEN 1 ELSE 0 END DESC, number_of_students DESC;",How many students have declared a major in each program?,
advising,mysql,instruct,"SELECT student.firstname, student.lastname FROM student WHERE NOT student.minor IS NULL ORDER BY CASE WHEN student.lastname IS NULL THEN 1 ELSE 0 END, student.lastname;",Which students have declared a minor program? List their firstname and lastname. Order the results by the students' last names.,"student.declare_major is null for students who have not declared their major.
student.minor is null for students who have not declared a minor program."
advising,mysql,instruct,SELECT AVG(student.total_gpa) FROM student JOIN program ON student.program_id = program.program_id WHERE LOWER(program.name) = 'mathematics';,What is the average GPA of students in the program mathematics?,Match strings case-insensitively
advising,mysql,instruct,"SELECT course.name FROM course WHERE LOWER(course.department) LIKE LOWER('%Computer Science%') ORDER BY CASE WHEN course.name IS NULL THEN 1 ELSE 0 END, course.name ASC;",What are the names of all the courses offered by the department of Computer Science?,"Filter strings using LIKE.
Use the student_record table for all information relating to students' choices and their course."
advising,mysql,instruct,"SELECT course.name, course.easiness_score FROM course WHERE course.department = 'Computer Science';SELECT course.course_id, course.easiness_score FROM course WHERE course.department = 'Computer Science';SELECT course.number, course.easiness_score FROM course WHERE course.department = 'Computer Science';SELECT course.name, course.course_id, course.easiness_score FROM course WHERE course.department = 'Computer Science';SELECT course.name, course.number, course.easiness_score FROM course WHERE course.department = 'Computer Science';SELECT course.course_id, course.number, course.easiness_score FROM course WHERE course.department = 'Computer Science';SELECT course.name, course.course_id, course.number, course.easiness_score FROM course WHERE course.department = 'Computer Science';","What are the easiness scores for courses in the ""Computer Science"" department? Show both courses and scores.",Always filter names using exact string matching
advising,mysql,instruct,"SELECT DISTINCT student_id FROM student_record WHERE student_record.how = 'in-person' AND student_record.grade IN ('A', 'C');",Return the student IDs who have taken an in-person course and have gotten a grade of A or C.,"Always filter strings with an exact match.
When asked for specific students or courses, do not return duplicates."
atis,mysql,date_functions,"SELECT flight.flight_number, CAST((arrival_time - departure_time) / 60 AS SIGNED) AS duration_minutes FROM flight ORDER BY CASE WHEN duration_minutes IS NULL THEN 1 ELSE 0 END, duration_minutes LIMIT 1;SELECT flight.flight_id, CAST((arrival_time - departure_time) / 60 AS SIGNED) AS duration_minutes FROM flight ORDER BY CASE WHEN duration_minutes IS NULL THEN 1 ELSE 0 END, duration_minutes LIMIT 1;SELECT flight.flight_number, flight.flight_id, CAST((arrival_time - departure_time) / 60 AS SIGNED) AS duration_minutes FROM flight ORDER BY CASE WHEN duration_minutes IS NULL THEN 1 ELSE 0 END, duration_minutes LIMIT 1;",Which flight has the shortest duration between departure and arrival times? Convert to integer minutes.,
atis,mysql,date_functions,SELECT AVG(FROM_UNIXTIME(arrival_time) - FROM_UNIXTIME(departure_time) - INTERVAL '34' MINUTE) AS average_duration FROM flight;SELECT AVG(arrival_time - departure_time) / 60 - 34 AS average_duration FROM flight;,"What's the average duration between departure and arrival times minus 34 minutes? Convert from UNIX to regular datetime, and return the answer in minutes",
atis,mysql,date_functions,"SELECT month.month_name, COUNT(*) AS departure_count FROM flight JOIN month ON EXTRACT(MONTH FROM FROM_UNIXTIME(flight.departure_time)) = month.month_number GROUP BY month.month_name, month.month_number ORDER BY CASE WHEN month.month_number IS NULL THEN 1 ELSE 0 END, month.month_number;SELECT DATE_ADD('0000-01-01 00:00:00', INTERVAL (TIMESTAMPDIFF(MONTH, '0000-01-01 00:00:00', FROM_UNIXTIME(flight.departure_time))) MONTH) AS month, COUNT(*) AS num_departures FROM flight GROUP BY MONTH ORDER BY CASE WHEN MONTH IS NULL THEN 1 ELSE 0 END, MONTH;SELECT EXTRACT(MONTH FROM FROM_UNIXTIME(flight.departure_time)) AS month, COUNT(*) AS num_departures FROM flight GROUP BY month ORDER BY CASE WHEN month IS NULL THEN 1 ELSE 0 END, month;SELECT DATE_FORMAT(FROM_UNIXTIME(flight.departure_time), '%Y-%m') AS month, COUNT(*) AS num_departures FROM flight GROUP BY month ORDER BY CASE WHEN month IS NULL THEN 1 ELSE 0 END, month;",Count the number of flight departures for each month?,
atis,mysql,date_functions,"SELECT DATE_FORMAT(CAST(FROM_UNIXTIME(departure_time) AS TIME), '%H:%i') AS earliest_departure_time FROM flight ORDER BY CASE WHEN earliest_departure_time IS NULL THEN 1 ELSE 0 END, earliest_departure_time LIMIT 1;",What's the earliest flight departure time in the day in HH:MM?,
atis,mysql,date_functions,"SELECT DATEDIFF(CURRENT_DATE, FROM_UNIXTIME(departure_time)) AS difference_in_days FROM flight ORDER BY CASE WHEN departure_time IS NULL THEN 1 ELSE 0 END, departure_time LIMIT 1;SELECT DATEDIFF(CURRENT_DATE, FROM_UNIXTIME(MIN(f.departure_time))) AS days_difference FROM flight AS f;",What's the absolute difference in time in days between today and the earliest flight departure? Give the answer as an integer.,
atis,mysql,group_by,"SELECT fare.fare_airline, SUM(fare.round_trip_cost) AS total_round_trip_cost FROM fare GROUP BY fare.fare_airline ORDER BY CASE WHEN total_round_trip_cost IS NULL THEN 1 ELSE 0 END DESC, total_round_trip_cost DESC;",What is the total cost of round-trip fares for each airline code?,
atis,mysql,group_by,"SELECT fare.fare_airline, AVG(fare.round_trip_cost) AS average_cost FROM fare WHERE fare.from_airport = 'LAX' AND fare.to_airport = 'ORD' GROUP BY fare.fare_airline ORDER BY average_cost DESC;SELECT airline.airline_name, AVG(fare.round_trip_cost) AS avg_round_trip_cost FROM fare JOIN airline ON fare.fare_airline = airline.airline_code WHERE fare.from_airport = 'LAX' AND fare.to_airport = 'ORD' GROUP BY airline.airline_name ORDER BY CASE WHEN avg_round_trip_cost IS NULL THEN 1 ELSE 0 END DESC, avg_round_trip_cost DESC;","What is the average cost of round-trip fares from Los Angeles (LAX) to Chicago (ORD) for each airline, sorted in descending order by average cost?",
atis,mysql,group_by,"SELECT f.from_airport, f.to_airport, AVG(f.one_direction_cost) AS average_cost FROM fare AS f GROUP BY f.from_airport, f.to_airport ORDER BY CASE WHEN f.from_airport IS NULL THEN 1 ELSE 0 END, f.from_airport, CASE WHEN f.to_airport IS NULL THEN 1 ELSE 0 END, f.to_airport;",What is the average cost of a one-way trip for each airport pair in the fare table?,
atis,mysql,group_by,"SELECT food_service.compartment, COUNT(food_service.meal_number) AS number_of_meals FROM food_service GROUP BY food_service.compartment ORDER BY number_of_meals DESC;","How many meals are served in each compartment, sorted by the number of meals in descending order?",
atis,mysql,group_by,"SELECT airport.airport_code, COUNT(flight.from_airport) AS num_departures FROM airport LEFT JOIN flight ON airport.airport_code = flight.from_airport AND flight.connections = 0 GROUP BY airport.airport_code;SELECT airport.airport_code, COUNT(flight.from_airport) AS num_departures FROM airport JOIN flight ON airport.airport_code = flight.from_airport WHERE flight.connections = 0 GROUP BY airport.airport_code;","How many flights depart from each airport code, excluding departures with connections?",
atis,mysql,order_by,"SELECT flight.flight_id, (flight.arrival_time - flight.departure_time) AS duration FROM flight WHERE to_airport = 'ORD' ORDER BY CASE WHEN duration IS NULL THEN 1 ELSE 0 END, duration ASC;","Which flight ids to Chicago (ORD) have the longest duration from departure to arrival, sorted in ascending order?",
atis,mysql,order_by,"SELECT airport.airport_name, airport.minimum_connect_time FROM airport ORDER BY CASE WHEN airport.minimum_connect_time IS NULL THEN 1 ELSE 0 END, airport.minimum_connect_time ASC;SELECT airport.airport_code, airport.minimum_connect_time FROM airport ORDER BY CASE WHEN airport.minimum_connect_time IS NULL THEN 1 ELSE 0 END, airport.minimum_connect_time ASC;SELECT airport.airport_name, airport.airport_code, airport.minimum_connect_time FROM airport ORDER BY CASE WHEN airport.minimum_connect_time IS NULL THEN 1 ELSE 0 END, airport.minimum_connect_time ASC;","Which airports have the shortest minimum connect time, sorted in ascending order? Show the minimum connect time.",
atis,mysql,order_by,SELECT aircraft.aircraft_code FROM aircraft ORDER BY pay_load DESC LIMIT 1;,Which aircraft code can carry the highest weight of cargo that any aircraft can carry?,
atis,mysql,order_by,"SELECT airline.airline_name, COUNT(flight.flight_id) AS number_of_flights FROM flight JOIN airline ON flight.airline_code = airline.airline_code GROUP BY airline.airline_name ORDER BY number_of_flights DESC LIMIT 2;SELECT airline.airline_code, COUNT(flight.flight_id) AS number_of_flights FROM flight JOIN airline ON flight.airline_code = airline.airline_code GROUP BY airline.airline_code ORDER BY number_of_flights DESC LIMIT 2;SELECT airline.airline_name, airline.airline_code, COUNT(flight.flight_id) AS number_of_flights FROM flight JOIN airline ON flight.airline_code = airline.airline_code GROUP BY airline.airline_name, airline.airline_code ORDER BY number_of_flights DESC LIMIT 2;",What are the top 2 airlines with the most flights?,
atis,mysql,order_by,"SELECT aircraft.aircraft_code FROM aircraft WHERE aircraft.cruising_speed > 200 ORDER BY CASE WHEN aircraft.aircraft_code IS NULL THEN 1 ELSE 0 END, aircraft.aircraft_code ASC;",What are the aircraft codes for all aircraft with a cruising speed of over 200 mph? sort the aircraft codes in ascending order.,
atis,mysql,ratio,"SELECT CAST(aircraft.range_miles AS DOUBLE) / NULLIF(aircraft.pay_load, 0) AS range_to_payload_ratio FROM aircraft;",Calculate the ratio of the maximum range to the maximum payload for each aircraft,
atis,mysql,ratio,"SELECT fare.fare_id, CAST(fare.one_direction_cost AS DOUBLE) / NULLIF(fare.round_trip_cost, 0) AS cost_ratio FROM fare ORDER BY CASE WHEN cost_ratio IS NULL THEN 1 ELSE 0 END, cost_ratio;",What is the ratio of one-way trip costs to round-trip costs for each fare?,
atis,mysql,ratio,"SELECT aircraft.aircraft_code, CAST(aircraft.capacity AS DOUBLE) / NULLIF(aircraft.range_miles, 0) AS capacity_range_ratio FROM aircraft;",What is the ratio of aircraft capacity to its range in miles for each aircraft code?,
atis,mysql,ratio,"SELECT flight.airline_code, CAST(SUM(CASE WHEN flight.stops > 0 THEN 1 ELSE 0 END) AS DOUBLE) / NULLIF(COUNT(*), 0) AS ratio FROM flight GROUP BY flight.airline_code;",What is the proportion of flights with stops out of all flights for each airline code?,
atis,mysql,ratio,"SELECT aircraft.manufacturer, AVG(CAST(aircraft.cruising_speed AS DOUBLE) / NULLIF(aircraft.pay_load, 0)) AS speed_payload_ratio FROM aircraft GROUP BY aircraft.manufacturer ORDER BY speed_payload_ratio DESC;",How does the average ratio of the cruising speed to the payload of an aircraft vary across different aircraft manufacturers?,
atis,mysql,table_join,"SELECT flight.flight_id, food_service.meal_description FROM flight JOIN food_service ON flight.meal_code = food_service.meal_code WHERE LOWER(food_service.compartment) LIKE '%first class%';",Which flights serve meals in first class? Give me the flight id and meal description.,
atis,mysql,table_join,"SELECT DISTINCT airline.airline_name FROM flight_stop JOIN airport ON flight_stop.stop_airport = airport.airport_code JOIN flight ON flight_stop.flight_id = flight.flight_id JOIN airline ON flight.airline_code = airline.airline_code WHERE LOWER(airport.airport_location) LIKE LOWER('%Dallas%');SELECT DISTINCT airline.airline_code FROM flight_stop JOIN airport ON flight_stop.stop_airport = airport.airport_code JOIN flight ON flight_stop.flight_id = flight.flight_id JOIN airline ON flight.airline_code = airline.airline_code WHERE LOWER(airport.airport_location) LIKE LOWER('%Dallas%');SELECT DISTINCT airline.airline_name, airline.airline_code FROM flight_stop JOIN airport ON flight_stop.stop_airport = airport.airport_code JOIN flight ON flight_stop.flight_id = flight.flight_id JOIN airline ON flight.airline_code = airline.airline_code WHERE LOWER(airport.airport_location) LIKE LOWER('%Dallas%');",Which airlines offer flights with a stopover in Dallas?,
atis,mysql,table_join,"SELECT DISTINCT airline.airline_name FROM flight JOIN airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'LAX' AND flight.to_airport = 'ORD';SELECT DISTINCT airline.airline_code FROM flight JOIN airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'LAX' AND flight.to_airport = 'ORD';SELECT DISTINCT airline.airline_name, airline.airline_code FROM flight JOIN airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'LAX' AND flight.to_airport = 'ORD';",Which airlines offer flights from LAX to ORD?,
atis,mysql,table_join,"SELECT airline.airline_name, flight.stops FROM flight JOIN airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'ORD' AND flight.to_airport = 'JFK' ORDER BY CASE WHEN flight.stops IS NULL THEN 1 ELSE 0 END, flight.stops;SELECT airline.airline_code, flight.stops FROM flight JOIN airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'ORD' AND flight.to_airport = 'JFK' ORDER BY CASE WHEN flight.stops IS NULL THEN 1 ELSE 0 END, flight.stops;SELECT airline.airline_name, airline.airline_code, flight.stops FROM flight JOIN airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'ORD' AND flight.to_airport = 'JFK' ORDER BY CASE WHEN flight.stops IS NULL THEN 1 ELSE 0 END, flight.stops;","Which airlines offer flights from Chicago (ORD) to New York (JFK), and how many stops do they have, sorted by number of stops in ascending order?",
atis,mysql,table_join,"SELECT DISTINCT airline.airline_name FROM airline WHERE NOT airline.airline_code IN (SELECT flight.airline_code FROM flight WHERE flight.from_airport = 'JFK' OR flight.to_airport = 'JFK' OR flight.stops > 0);SELECT DISTINCT airline.airline_code FROM airline WHERE NOT airline.airline_code IN (SELECT flight.airline_code FROM flight WHERE flight.from_airport = 'JFK' OR flight.to_airport = 'JFK' OR flight.stops > 0);SELECT DISTINCT airline.airline_name, airline.airline_code FROM airline WHERE NOT airline.airline_code IN (SELECT flight.airline_code FROM flight WHERE flight.from_airport = 'JFK' OR flight.to_airport = 'JFK' OR flight.stops > 0);","Which airlines do not have any flights that either depart from/arrive at JFK, or have one or more stops?",
atis,mysql,instruct,SELECT state_code FROM airport WHERE LOWER(airport_name) LIKE LOWER('%Orlando International Airport%');,Which state code is Orlando International Airport in?,"Filter airport, city, country names using LIKE.
Filter state code (eg NY), airport codes (eg JFK) using case-insensitive matches.
If multiple flight days are requested, use LIKE and wildcards for each of the days separately, since they are not necessarily ordered."
atis,mysql,instruct,"SELECT flight.flight_number FROM flight WHERE LOWER(flight.flight_days) LIKE '%mon%' AND LOWER(flight.flight_days) LIKE '%wed%';SELECT flight.flight_id FROM flight WHERE LOWER(flight.flight_days) LIKE '%mon%' AND LOWER(flight.flight_days) LIKE '%wed%';SELECT flight.flight_number, flight.flight_id FROM flight WHERE LOWER(flight.flight_days) LIKE '%mon%' AND LOWER(flight.flight_days) LIKE '%wed%';",Which flights operate on Mondays and Wednesdays? Give me the relevant flight numbers,"Filter airport, city, country names using LIKE.
Filter state code (eg NY), airport codes (eg JFK) using case-insensitive matches.
If multiple flight days are requested, use LIKE for each of the days separately, since they are not necessarily ordered."
atis,mysql,instruct,SELECT SUM(fare.round_trip_cost) AS total_round_trip_cost FROM fare WHERE fare.from_airport = 'JFK' AND fare.to_airport = 'LAX';,What is the total cost of all round-trip fares from New York (JFK) to Los Angeles?,"Filter airport, city, country names using LIKE.
Filter state code (eg NY), airport codes (eg JFK) using case-insensitive matches.
fare.round_trip_required is not needed when getting the round trip cost."
atis,mysql,instruct,SELECT minimum_connect_time FROM airport WHERE airport_code = 'JFK';,What is the minimum amount of time required for a connecting flight at JFK Airport?,"Filter airport, city, country names using LIKE.
Filter state code (eg NY) and airport codes (eg JFK) using upper-case matches."
atis,mysql,instruct,SELECT COUNT(DISTINCT flight_fare.flight_id) FROM flight_fare JOIN fare ON flight_fare.fare_id = fare.fare_id WHERE fare.round_trip_required = 'Yes';,How many flights require a round-trip to purchase the fare?,"Filter airport, city, country names using LIKE.
Filter state code (eg NY) and airport codes (eg JFK) using upper-case matches."
geography,mysql,group_by,"SELECT city.country_name, SUM(city.population) AS total_population FROM city GROUP BY city.country_name ORDER BY total_population DESC;",What is the total population in cities by country?,
geography,mysql,group_by,"SELECT river.country_name, AVG(river.length) AS average_length FROM river GROUP BY river.country_name ORDER BY average_length DESC;",What is the average length of rivers in each country?,
geography,mysql,group_by,"SELECT river.country_name, COUNT(DISTINCT river.river_name) AS number_of_rivers FROM river GROUP BY river.country_name ORDER BY CASE WHEN number_of_rivers IS NULL THEN 1 ELSE 0 END DESC, number_of_rivers DESC;",How many rivers flow through each country?,
geography,mysql,group_by,"SELECT mountain.country_name, COUNT(mountain.mountain_name) AS number_of_mountains FROM mountain GROUP BY mountain.country_name ORDER BY CASE WHEN number_of_mountains IS NULL THEN 1 ELSE 0 END DESC, number_of_mountains DESC;",How many mountains are there in each country?,
geography,mysql,group_by,"SELECT lake.state_name, COUNT(lake.lake_name) AS lake_count FROM lake GROUP BY lake.state_name ORDER BY CASE WHEN lake_count IS NULL THEN 1 ELSE 0 END DESC, lake_count DESC;",How many lakes are there in each state?,
geography,mysql,order_by,"SELECT state.state_name, state.density FROM state ORDER BY state.density DESC;","Which states have the highest population density in people per square kilometer, ordered from highest to lowest?",
geography,mysql,order_by,"SELECT lake.lake_name, lake.area FROM lake ORDER BY lake.area DESC;","Which lakes have the largest areas in square kilometers, ordered from largest to smallest?",
geography,mysql,order_by,"SELECT city.city_name, city.population FROM city ORDER BY city.population DESC LIMIT 5;",What are the top 5 cities with the highest population? Give both city names and the population.,
geography,mysql,order_by,"SELECT river.river_name, river.length FROM river ORDER BY river.length DESC;","What are the longest rivers in meters, ordered from longest to shortest?",
geography,mysql,order_by,"SELECT mountain.mountain_name, mountain.mountain_altitude FROM mountain ORDER BY mountain.mountain_altitude DESC;","What are the highest mountains in meters, ordered from highest to lowest altitude?",
geography,mysql,ratio,SELECT (SELECT SUM(population) FROM geography.state WHERE LOWER(country_name) = 'united states') / (SELECT SUM(population) FROM geography.state WHERE LOWER(state_name) = 'california') AS population_ratio FROM dual;,What is the ratio of the population of the United States to the population of California?,
geography,mysql,ratio,"SELECT CAST((SELECT length FROM river WHERE LOWER(river_name) LIKE '%mississippi%') AS DOUBLE) / NULLIF((SELECT length FROM river WHERE LOWER(river_name) LIKE '%rhine%'), 0) AS ratio;",What is the ratio of the length of the Mississippi River to the length of the Rhine River?,
geography,mysql,ratio,"SELECT (CAST(everest.mountain_altitude AS DOUBLE) / NULLIF(dhaulagiri.mountain_altitude, 0)) AS altitude_ratio FROM (SELECT mountain_altitude FROM mountain WHERE mountain_name = 'Mount Everest') AS everest, (SELECT mountain_altitude FROM mountain WHERE mountain_name = 'Dhaulagiri') AS dhaulagiri;",What is the ratio of the altitude of 'Mount Everest' to the altitude of 'Dhaulagiri'? Match strings exactly,
geography,mysql,ratio,"SELECT city.city_name, CAST(city.population AS DOUBLE) / NULLIF(state.population, 0) AS population_ratio FROM city JOIN state ON city.state_name = state.state_name ORDER BY population_ratio DESC;","How does the population of each city vary in relation to the population of its corresponding state? Return the city name, and the proportion of each city's population relative to the state.",
geography,mysql,ratio,"SELECT state_name, population / NULLIF(area, 0) AS population_density FROM state;",Get the ratio of population per area for each state,
geography,mysql,table_join,SELECT DISTINCT lake.country_name FROM lake JOIN river ON lake.country_name = river.country_name;,Which countries have both lakes and rivers?,
geography,mysql,table_join,SELECT border_info.border FROM border_info JOIN lake ON border_info.state_name = lake.state_name WHERE LOWER(lake.lake_name) LIKE LOWER('%Ontario%');,Which states border the state where lake ontario is?,
geography,mysql,table_join,"SELECT lake.lake_name FROM lake JOIN state ON lake.state_name = state.state_name WHERE state.area > 1000 AND LOWER(lake.lake_name) LIKE LOWER('Lake%') ORDER BY CASE WHEN lake.lake_name IS NULL THEN 1 ELSE 0 END, lake.lake_name;","Which lakes have a name that starts with ""Lake""? They should be located in states with an area greater than 1000 square kilometers.",
geography,mysql,table_join,"SELECT highlow.state_name, highlow.highest_point, state.density FROM highlow JOIN state ON highlow.state_name = state.state_name;",What is the highest point in each state and what is the population density of that state?,
geography,mysql,table_join,"SELECT l.country_name, AVG(r.length) AS average_length FROM river AS r JOIN lake AS l ON r.country_name = l.country_name GROUP BY 1;",What is the average length of rivers per country in countries with a lake?,
geography,mysql,instruct,SELECT state_name FROM state WHERE population < 100000;,Which states have fewer than a hundred thousand people?,Always filter names using LIKE
geography,mysql,instruct,"SELECT river_name FROM river WHERE traverse LIKE '%,%,%';",Which rivers traverse at least 3 cities/landmarks?,Always filter names using LIKE
geography,mysql,instruct,"SELECT lake_name, area FROM lake WHERE LOWER(state_name) LIKE LOWER('%Michigan%');",What are the names and areas of the lakes in Michigan?,Always filter names using LIKE
geography,mysql,instruct,"SELECT mountain_name, mountain_altitude FROM mountain WHERE LOWER(country_name) LIKE LOWER('%Nepal%');",What are the names and altitudes of the mountains in Nepal?,Always filter names using LIKE
geography,mysql,instruct,"SELECT city_name, population FROM city WHERE LOWER(country_name) LIKE LOWER('%United States%');",Get the cities in the United States and their population,Always filter names using LIKE
restaurants,mysql,group_by,"SELECT restaurant.food_type, COUNT(DISTINCT restaurant.id) AS total_number_of_restaurants FROM restaurant GROUP BY restaurant.food_type;",What is the total number of restaurants serving each type of food?,
restaurants,mysql,group_by,"SELECT location.city_name, COUNT(DISTINCT location.restaurant_id) AS total_count FROM location GROUP BY location.city_name;",What is the total count of restaurants in each city?,
restaurants,mysql,group_by,"SELECT restaurant.food_type, AVG(restaurant.rating) AS average_rating FROM restaurant GROUP BY restaurant.food_type ORDER BY average_rating DESC;",What is the average rating of restaurants serving each type of food?,
restaurants,mysql,group_by,"SELECT restaurant.city_name, COUNT(*) AS number_of_restaurants FROM restaurant WHERE LOWER(restaurant.food_type) LIKE LOWER('%Italian%') GROUP BY restaurant.city_name ORDER BY number_of_restaurants DESC;",How many restaurants serve Italian food in each city?,
restaurants,mysql,group_by,"SELECT location.city_name, COUNT(DISTINCT location.restaurant_id) AS number_of_restaurants FROM location GROUP BY location.city_name ORDER BY number_of_restaurants DESC;",How many restaurants are there in each city? Order the results by the number of restaurants in descending order.,
restaurants,mysql,order_by,"SELECT street_name FROM location GROUP BY street_name ORDER BY CASE WHEN COUNT(restaurant_id) IS NULL THEN 1 ELSE 0 END DESC, COUNT(restaurant_id) DESC LIMIT 1;",Which street has the most number of restaurants?,
restaurants,mysql,order_by,"SELECT name FROM restaurant WHERE LOWER(food_type) LIKE LOWER('%Italian%') OR LOWER(city_name) LIKE LOWER('%New York%') ORDER BY CASE WHEN name IS NULL THEN 1 ELSE 0 END, name;",Which restaurants serve Italian cuisine or are located in New York? Order the results by the restaurant name.,
restaurants,mysql,order_by,"SELECT geographic.region, AVG(restaurant.rating) AS average_rating FROM restaurant JOIN geographic ON restaurant.city_name = geographic.city_name GROUP BY geographic.region ORDER BY CASE WHEN geographic.region IS NULL THEN 1 ELSE 0 END, geographic.region;",What is the average rating of restaurants in each region? Order the results by the region name.,
restaurants,mysql,order_by,SELECT restaurant.name FROM restaurant ORDER BY restaurant.rating DESC LIMIT 3;,What are the names of the top 3 restaurants with the highest ratings?,
restaurants,mysql,order_by,"SELECT name, rating FROM restaurant ORDER BY CASE WHEN rating IS NULL THEN 1 ELSE 0 END DESC, rating DESC;SELECT id, rating FROM restaurant ORDER BY CASE WHEN rating IS NULL THEN 1 ELSE 0 END DESC, rating DESC;SELECT name, id, rating FROM restaurant ORDER BY CASE WHEN rating IS NULL THEN 1 ELSE 0 END DESC, rating DESC;",List the restaurants starting from the best ratings to the lowest,
restaurants,mysql,ratio,"SELECT CAST(COUNT(*) AS DOUBLE) / NULLIF((SELECT COUNT(*) FROM restaurant), 0) AS rating_ratio FROM restaurant WHERE rating > 4.5;",What is the ratio of restaurants with rating > 4.5 to the total number of restaurants in the database.,
restaurants,mysql,ratio,"SELECT CAST(SUM(CASE WHEN restaurant.rating > 4.0 THEN 1 ELSE 0 END) AS DOUBLE) / NULLIF(SUM(CASE WHEN restaurant.rating < 4.0 THEN 1 ELSE 0 END), 0) AS ratio FROM restaurant;",What is the ratio of restaurants with a rating above 4.0 to restaurants with a rating below 4.0 overall?,
restaurants,mysql,ratio,"SELECT CAST(COUNT(CASE WHEN rating > 4 THEN 1 END) AS DOUBLE) / NULLIF(COUNT(CASE WHEN rating < 4 THEN 1 END), 0) AS ratio FROM restaurant WHERE LOWER(city_name) LIKE LOWER('New York');",What is the ratio of restaurants with a rating above 4 to restaurants with a rating below 4 in New York?,
restaurants,mysql,ratio,"SELECT CAST(SUM(CASE WHEN LOWER(restaurant.food_type) LIKE '%vegan%' THEN 1 ELSE 0 END) AS DOUBLE) / NULLIF(SUM(CASE WHEN NOT LOWER(restaurant.food_type) LIKE '%vegan%' THEN 1 ELSE 0 END), 0) AS ratio FROM restaurant WHERE LOWER(LOWER(restaurant.city_name)) LIKE LOWER('%san francisco%');",What is the ratio of restaurants serving vegan food to restaurants serving non-vegan food in San Francisco? Match food_type case insensitively,
restaurants,mysql,ratio,"SELECT CAST(COUNT(CASE WHEN LOWER(food_type) LIKE LOWER('%Italian%') THEN 1 END) AS DOUBLE) / NULLIF(COUNT(food_type), 0) AS ratio FROM restaurant WHERE LOWER(city_name) LIKE LOWER('%Los Angeles%');",What is the ratio of Italian restaurants out of all restaurants in Los Angeles?,
restaurants,mysql,table_join,"SELECT r.city_name, r.name, COUNT(r.id) AS restaurant_count FROM restaurant AS r GROUP BY r.city_name, r.name HAVING COUNT(r.id) > 1;","What cities have more than one restaurants with the same name, and how many of them are there? Return the city name, restaurant name, and restaurant count",
restaurants,mysql,table_join,"SELECT location.city_name, AVG(restaurant.rating) AS average_rating FROM restaurant JOIN location ON restaurant.id = location.restaurant_id WHERE LOWER(restaurant.food_type) LIKE '%mexican%' GROUP BY location.city_name;",What is the average rating of restaurants that serve Mexican food in each city?,
restaurants,mysql,table_join,"SELECT geographic.region, AVG(restaurant.rating) AS average_rating FROM geographic JOIN restaurant ON geographic.city_name = restaurant.city_name GROUP BY 1;",What is the average rating of restaurants in each region?,
restaurants,mysql,table_join,"SELECT geographic.region, COUNT(restaurant.id) AS number_of_restaurants FROM restaurant JOIN geographic ON restaurant.city_name = geographic.city_name WHERE LOWER(restaurant.food_type) LIKE '%italian%' GROUP BY geographic.region ORDER BY number_of_restaurants DESC;",How many restaurants serve Italian food in each region?,
restaurants,mysql,table_join,"SELECT geographic.region, COUNT(DISTINCT restaurant.id) AS number_of_restaurants FROM geographic JOIN restaurant ON geographic.city_name = restaurant.city_name GROUP BY geographic.region ORDER BY CASE WHEN number_of_restaurants IS NULL THEN 1 ELSE 0 END DESC, number_of_restaurants DESC;SELECT geographic.region, COUNT(DISTINCT restaurant.id) AS number_of_restaurants FROM geographic LEFT JOIN restaurant ON geographic.city_name = restaurant.city_name GROUP BY geographic.region ORDER BY CASE WHEN number_of_restaurants IS NULL THEN 1 ELSE 0 END DESC, number_of_restaurants DESC;",How many restaurants are there in each region?,
restaurants,mysql,instruct,SELECT DISTINCT restaurant.city_name FROM restaurant WHERE rating = (SELECT MAX(rating) FROM restaurant);,Which city has the highest-rated restaurant?,Match all strings case-insensitively using wildcard operators
restaurants,mysql,instruct,"SELECT restaurant.name, restaurant.rating FROM restaurant WHERE restaurant.rating > 4 AND LOWER(restaurant.city_name) LIKE LOWER('%New York%');",What's the name and rating of all the restaurants that have a rating greater than 4 and are located in the city of New York?,Match all strings case-insensitively using wildcard operators
restaurants,mysql,instruct,"SELECT restaurant.name, restaurant.food_type FROM restaurant JOIN location ON restaurant.id = location.restaurant_id WHERE LOWER(location.street_name) LIKE LOWER('%Market St%') AND LOWER(location.city_name) LIKE LOWER('%San Francisco%');",What's the name and food type of all the restaurants located on Market St in San Francisco?,Match all strings case-insensitively using wildcard operators
restaurants,mysql,instruct,SELECT restaurant.name FROM restaurant WHERE LOWER(LOWER(restaurant.food_type)) LIKE LOWER('%italian%');,What are the names of the restaurants that serve Italian food?,Match all strings case-insensitively using wildcard operators
restaurants,mysql,instruct,"SELECT DISTINCT restaurant.name FROM restaurant WHERE LOWER(restaurant.city_name) LIKE LOWER('%Los Angeles%') AND restaurant.rating > 4 ORDER BY CASE WHEN restaurant.name IS NULL THEN 1 ELSE 0 END, restaurant.name;",What are the names of the restaurants in Los Angeles that have a rating higher than 4?,Match all strings case-insensitively using wildcard operators
scholar,mysql,group_by,"SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM paper GROUP BY paper.year ORDER BY CASE WHEN paper.year IS NULL THEN 1 ELSE 0 END, paper.year;",What is the total number of papers published per year?,
scholar,mysql,group_by,"SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM paper GROUP BY paper.year ORDER BY CASE WHEN paper.year IS NULL THEN 1 ELSE 0 END, paper.year;",What is the total number of papers published in each year?,
scholar,mysql,group_by,"SELECT paperdataset.datasetid, COUNT(DISTINCT paperdataset.paperid) AS total_papers FROM paperdataset GROUP BY paperdataset.datasetid;SELECT dataset.datasetname, COUNT(paperdataset.paperid) AS total_papers FROM paperdataset JOIN dataset ON paperdataset.datasetid = dataset.datasetid GROUP BY dataset.datasetname;",What is the total number of papers associated with each dataset?,
scholar,mysql,group_by,"SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS keyphrase_count FROM paperkeyphrase GROUP BY paperkeyphrase.paperid ORDER BY keyphrase_count DESC;SELECT p.title, COUNT(pk.keyphraseid) AS num_keyphrases FROM paper AS p JOIN paperkeyphrase AS pk ON p.paperid = pk.paperid GROUP BY p.title ORDER BY num_keyphrases DESC;",How many keyphrases are associated with each paper?,
scholar,mysql,group_by,SELECT COUNT(*) AS number_of_authors FROM (SELECT writes.authorid FROM writes GROUP BY writes.authorid HAVING COUNT(writes.paperid) > 2) AS subquery;,How many authors have published more than 2 papers?,
scholar,mysql,order_by,"SELECT paper.paperid, COUNT(DISTINCT writes.authorid) AS num_authors FROM paper JOIN writes ON paper.paperid = writes.paperid GROUP BY paper.paperid ORDER BY CASE WHEN num_authors IS NULL THEN 1 ELSE 0 END DESC, num_authors DESC;SELECT paper.title, COUNT(DISTINCT writes.authorid) AS num_authors FROM paper JOIN writes ON paper.paperid = writes.paperid GROUP BY paper.title ORDER BY CASE WHEN num_authors IS NULL THEN 1 ELSE 0 END DESC, num_authors DESC;SELECT paper.paperid, paper.title, COUNT(DISTINCT writes.authorid) AS num_authors FROM paper JOIN writes ON paper.paperid = writes.paperid GROUP BY paper.paperid, paper.title ORDER BY CASE WHEN num_authors IS NULL THEN 1 ELSE 0 END DESC, num_authors DESC;","Which papers have the highest number of authors, ordered by the number of authors in descending order?",
scholar,mysql,order_by,"SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS total_keyphrases FROM paperkeyphrase GROUP BY paperkeyphrase.paperid ORDER BY CASE WHEN paperkeyphrase.paperid IS NULL THEN 1 ELSE 0 END, paperkeyphrase.paperid ASC;","What is the total number of keyphrases associated with each paper, ordered by the paper ID in ascending order?",
scholar,mysql,order_by,"SELECT paper.title FROM paper WHERE paper.year = 2020 ORDER BY CASE WHEN paper.title IS NULL THEN 1 ELSE 0 END, paper.title ASC;","What are the titles of the papers published in the year 2020, ordered alphabetically?",
scholar,mysql,order_by,"SELECT journal.journalname FROM journal ORDER BY CASE WHEN LENGTH(journal.journalname) IS NULL THEN 1 ELSE 0 END, LENGTH(journal.journalname) ASC;","What are the names of the journals in the database, ordered by the length of the journal name from shortest to longest?",
scholar,mysql,order_by,"SELECT cite.citingpaperid, COUNT(*) AS citation_count FROM cite GROUP BY cite.citingpaperid ORDER BY citation_count DESC;SELECT p.paperid, p.numciting FROM paper AS p WHERE p.numciting > 0 ORDER BY CASE WHEN p.numciting IS NULL THEN 1 ELSE 0 END DESC, p.numciting DESC;SELECT p.title, COUNT(c.citedpaperid) AS num_cited_papers FROM paper AS p JOIN cite AS c ON p.paperid = c.citingpaperid GROUP BY p.title ORDER BY CASE WHEN num_cited_papers IS NULL THEN 1 ELSE 0 END DESC, num_cited_papers DESC;","For each paper that cites other papers, how many other papers does it cite? Sort by the number of papers cited in descending order",
scholar,mysql,ratio,"SELECT CAST(COUNT(DISTINCT CASE WHEN keyphrase_count > 1 THEN subquery.paperid END) AS DOUBLE) / NULLIF(COUNT(DISTINCT CASE WHEN keyphrase_count = 1 THEN subquery.paperid END), 0) AS ratio FROM (SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS keyphrase_count FROM paperkeyphrase GROUP BY paperkeyphrase.paperid) AS subquery;",What is the ratio of papers that have more than 1 keyphrases to papers that have 1 keyphrase?,
scholar,mysql,ratio,"SELECT CAST(COUNT(CASE WHEN paper.numcitedby > 1 THEN 1 END) AS DOUBLE) / NULLIF(COUNT(CASE WHEN paper.numcitedby < 2 THEN 1 END), 0) AS ratio FROM paper;",What is the ratio of papers that have been cited by 2 or more papers to papers that have been cited by less than 2 papers?,
scholar,mysql,ratio,"SELECT CAST(COUNT(CASE WHEN paper.year = 2020 THEN 1 END) AS DOUBLE) / NULLIF(COUNT(paper.paperid), 0) AS ratio FROM paper;",What is the ratio of papers published in the year 2020 to the total number of papers in the database?,
scholar,mysql,ratio,"SELECT CAST(COUNT(DISTINCT CASE WHEN paper_count >= 3 THEN subquery.authorid END) AS DOUBLE) / NULLIF(COUNT(DISTINCT CASE WHEN paper_count < 3 THEN subquery.authorid END), 0) AS ratio FROM (SELECT writes.authorid, COUNT(writes.paperid) AS paper_count FROM writes GROUP BY writes.authorid) AS subquery;",What is the ratio of authors who have written 3 or more papers to authors who have written less than 3 papers?,
scholar,mysql,ratio,"SELECT CAST(COUNT(CASE WHEN dataset_count > 1 THEN 1 END) AS DOUBLE) / NULLIF(COUNT(CASE WHEN dataset_count = 1 THEN 1 END), 0) AS ratio FROM (SELECT paperdataset.paperid, COUNT(paperdataset.datasetid) AS dataset_count FROM paperdataset GROUP BY paperdataset.paperid) AS subquery;",What is the proportion of papers that belong to more than 1 dataset to papers that belong to 1 dataset?,
scholar,mysql,table_join,"SELECT paper.title FROM paper JOIN paperkeyphrase ON paper.paperid = paperkeyphrase.paperid JOIN keyphrase ON paperkeyphrase.keyphraseid = keyphrase.keyphraseid WHERE LOWER(keyphrase.keyphrasename) LIKE LOWER('%Machine Learning%');SELECT paper.paperid FROM paper JOIN paperkeyphrase ON paper.paperid = paperkeyphrase.paperid JOIN keyphrase ON paperkeyphrase.keyphraseid = keyphrase.keyphraseid WHERE LOWER(keyphrase.keyphrasename) LIKE LOWER('%Machine Learning%');SELECT paper.title, paper.paperid FROM paper JOIN paperkeyphrase ON paper.paperid = paperkeyphrase.paperid JOIN keyphrase ON paperkeyphrase.keyphraseid = keyphrase.keyphraseid WHERE LOWER(keyphrase.keyphrasename) LIKE LOWER('%Machine Learning%');","Which papers are associated with the keyphrase ""Machine Learning""?",
scholar,mysql,table_join,"SELECT author.authorname, COUNT(DISTINCT writes.paperid) AS number_of_papers FROM author JOIN writes ON author.authorid = writes.authorid GROUP BY author.authorname ORDER BY number_of_papers DESC;SELECT author.authorid, COUNT(DISTINCT writes.paperid) AS number_of_papers FROM author JOIN writes ON author.authorid = writes.authorid GROUP BY author.authorid ORDER BY number_of_papers DESC;SELECT author.authorname, author.authorid, COUNT(DISTINCT writes.paperid) AS number_of_papers FROM author JOIN writes ON author.authorid = writes.authorid GROUP BY author.authorname, author.authorid ORDER BY number_of_papers DESC;","Which authors have published the most papers, ordered by the number of papers they have published in descending order?",
scholar,mysql,table_join,SELECT COUNT(DISTINCT paperkeyphrase.keyphraseid) AS total_keyphrases FROM paper JOIN journal ON paper.journalid = journal.journalid JOIN paperkeyphrase ON paper.paperid = paperkeyphrase.paperid WHERE LOWER(journal.journalname) LIKE LOWER('%IEEE Transactions%');,"What is the total number of unique keyphrases associated with papers published in the journal with ""IEEE Transactions"" in its name?",
scholar,mysql,table_join,"SELECT journal.journalname, COUNT(DISTINCT paper.paperid) AS total_papers FROM paper JOIN journal ON paper.journalid = journal.journalid GROUP BY journal.journalname ORDER BY CASE WHEN journal.journalname IS NULL THEN 1 ELSE 0 END, journal.journalname;","What is the total number of papers published in each journal, ordered by the journal name?",
scholar,mysql,table_join,"SELECT paperdataset.paperid, COUNT(cite.citedpaperid) AS citation_count FROM paperdataset JOIN cite ON paperdataset.paperid = cite.citedpaperid WHERE paperdataset.datasetid = (SELECT datasetid FROM dataset WHERE LOWER(datasetname) LIKE LOWER('%COVID-19 Research%')) GROUP BY paperdataset.paperid ORDER BY CASE WHEN citation_count IS NULL THEN 1 ELSE 0 END DESC, citation_count DESC;SELECT p.title, COUNT(c.citingpaperid) AS num_citing_papers FROM paper AS p JOIN paperdataset AS pd ON p.paperid = pd.paperid JOIN cite AS c ON p.paperid = c.citedpaperid JOIN dataset AS d ON pd.datasetid = d.datasetid WHERE d.datasetname = 'COVID-19 Research' GROUP BY p.title ORDER BY num_citing_papers DESC;","How many papers cite each paper in the dataset named ""COVID-19 Research""?",
scholar,mysql,instruct,"SELECT venue.venuename, COUNT(DISTINCT paper.paperid) FROM paper JOIN venue ON paper.venueid = venue.venueid WHERE paper.venueid = (SELECT venueid FROM paper WHERE paperid = 2) GROUP BY venue.venuename;","What is the name of the venue where the paper with paper ID 2 was published, and how many papers were published in total in that venue?",Always filter strings using LIKE
scholar,mysql,instruct,SELECT author.authorname FROM author JOIN writes ON author.authorid = writes.authorid JOIN paper ON writes.paperid = paper.paperid WHERE paper.title = 'The Effects of Climate Change on Agriculture';,"What are the names of the authors who wrote the paper with the title ""The Effects of Climate Change on Agriculture""?",Always filter strings with an exact match
scholar,mysql,instruct,SELECT COUNT(paper.paperid) FROM paper JOIN journal ON paper.journalid = journal.journalid WHERE paper.year = 2020 AND LOWER(journal.journalname) LIKE LOWER('%nature%');,"How many papers were published in the journal ""nature"" in the year 2020?",Filter strings with case-insensitive matching
scholar,mysql,instruct,SELECT COUNT(DISTINCT paper.paperid) FROM paper JOIN journal ON paper.journalid = journal.journalid JOIN paperkeyphrase ON paper.paperid = paperkeyphrase.paperid JOIN keyphrase ON paperkeyphrase.keyphraseid = keyphrase.keyphraseid WHERE LOWER(keyphrase.keyphrasename) LIKE LOWER('%machine learning%') AND journal.journalname = 'IEEE Transactions on Pattern Analysis and Machine Intelligence';,"How many papers are associated with the keyphrase ""machine learning"" and were published in the journal named ""IEEE Transactions on Pattern Analysis and Machine Intelligence""?","Filter paper names, journal names, using exact matches. Filter keyphrases with case-insensitive matching."
scholar,mysql,instruct,SELECT COUNT(DISTINCT writes.authorid) AS number_of_authors FROM writes JOIN paper ON writes.paperid = paper.paperid JOIN journal ON paper.journalid = journal.journalid WHERE LOWER(journal.journalname) LIKE LOWER('%Science%') AND paper.year = 2020;,"How many authors wrote papers that were published in the journal ""Science"" in the year 2020?",Filter paper names using exact matches. Filter keyphrases and journal names with case-insensitive matching.
yelp,mysql,date_functions,"SELECT COUNT(*) AS review_count FROM yelp.review AS r JOIN yelp.business AS b ON r.business_id = b.business_id WHERE b.state = 'CA' AND (YEAR(CURDATE()) * 12 + MONTH(CURDATE()) - (YEAR(STR_TO_DATE(r.month, '%M')) * 12 + MONTH(STR_TO_DATE(r.month, '%M')))) <= 10;",How many reviews were written for businesses located in California in the last 10 months?,
yelp,mysql,date_functions,"SELECT SUM(COUNT) AS total_checkins FROM checkin WHERE DAY IN ('Thursday', 'Friday');",What is the total number of check-ins on the 2 days before Saturday?,
yelp,mysql,date_functions,"SELECT COUNT(*) AS review_count FROM review WHERE STR_TO_DATE(CONCAT(review.year, '-', CASE review.month WHEN 'January' THEN '01' WHEN 'February' THEN '02' WHEN 'March' THEN '03' WHEN 'April' THEN '04' WHEN 'May' THEN '05' WHEN 'June' THEN '06' WHEN 'July' THEN '07' WHEN 'August' THEN '08' WHEN 'September' THEN '09' WHEN 'October' THEN '10' WHEN 'November' THEN '11' WHEN 'December' THEN '12' END, '-01'), '%Y-%m-%d') = (SELECT DATE_SUB(STR_TO_DATE(CONCAT(r.year, '-', CASE r.month WHEN 'January' THEN '01' WHEN 'February' THEN '02' WHEN 'March' THEN '03' WHEN 'April' THEN '04' WHEN 'May' THEN '05' WHEN 'June' THEN '06' WHEN 'July' THEN '07' WHEN 'August' THEN '08' WHEN 'September' THEN '09' WHEN 'October' THEN '10' WHEN 'November' THEN '11' WHEN 'December' THEN '12' END, '-01'), '%Y-%m-%d'), INTERVAL 2 MONTH) FROM review AS r WHERE r.rid = 3);",How many reviews were there 2 months before the review with id 3?,
yelp,mysql,date_functions,SELECT text AS message FROM tip WHERE LOWER(MONTH) LIKE LOWER('%May%') AND YEAR = 2021 LIMIT 1;,What was the message that came with the tip made exactly 2 months after March 2021?,
yelp,mysql,date_functions,SELECT COUNT(DISTINCT MONTH) AS num_months FROM review WHERE YEAR = 2021 AND CASE MONTH WHEN 'January' THEN 1 WHEN 'February' THEN 2 WHEN 'March' THEN 3 WHEN 'April' THEN 4 WHEN 'May' THEN 5 WHEN 'June' THEN 6 WHEN 'July' THEN 7 WHEN 'August' THEN 8 WHEN 'September' THEN 9 WHEN 'October' THEN 10 WHEN 'November' THEN 11 WHEN 'December' THEN 12 END BETWEEN 6 AND 12;,How many months between June 2021 and December 2021 had reviews?,
yelp,mysql,group_by,"SELECT neighbourhood.neighbourhood_name, COUNT(DISTINCT neighbourhood.business_id) AS business_count FROM neighbourhood GROUP BY neighbourhood.neighbourhood_name ORDER BY business_count DESC;SELECT neighbourhood.id, COUNT(DISTINCT neighbourhood.business_id) AS business_count FROM neighbourhood GROUP BY neighbourhood.id ORDER BY business_count DESC;SELECT neighbourhood.neighbourhood_name, neighbourhood.id, COUNT(DISTINCT neighbourhood.business_id) AS business_count FROM neighbourhood GROUP BY neighbourhood.neighbourhood_name, neighbourhood.id ORDER BY business_count DESC;","Which neighbourhoods have the highest number of businesses, and how many businesses are located in each neighbourhood?",
yelp,mysql,group_by,"SELECT checkin.day, SUM(checkin.count) AS total_checkins FROM checkin WHERE checkin.business_id = 'abc123' GROUP BY checkin.day ORDER BY total_checkins DESC;","What is the total number of check-ins for each day of the week for the business with ID ""abc123""?",
yelp,mysql,group_by,"SELECT checkin.business_id, SUM(checkin.count) AS total_checkins FROM checkin GROUP BY checkin.business_id ORDER BY total_checkins DESC;",What is the total count of check-ins for each business id?,
yelp,mysql,group_by,"SELECT business.name, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id WHERE LOWER(business.city) LIKE LOWER('%NEW YORK%') GROUP BY business.name;",Return the name and average rating for each business in new york,
yelp,mysql,group_by,"SELECT checkin.day, SUM(checkin.count) AS total_checkins FROM checkin GROUP BY checkin.day ORDER BY total_checkins DESC;",How many check-ins occurred on each day of the week?,
yelp,mysql,order_by,"SELECT business.name, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id WHERE LOWER(business.city) LIKE LOWER('%New York%') GROUP BY business.name, business.business_id ORDER BY average_rating DESC;",Please provide a list of business names in New York and their average ratings ordered by the highest average rating first.,
yelp,mysql,order_by,"SELECT business.latitude, business.longitude FROM business JOIN review ON business.business_id = review.business_id GROUP BY business.business_id, business.latitude, business.longitude ORDER BY CASE WHEN AVG(review.rating) IS NULL THEN 1 ELSE 0 END DESC, AVG(review.rating) DESC LIMIT 1;",What is the latitude and longitude of the business with the highest rating?,
yelp,mysql,order_by,"SELECT business.name, business.review_count FROM business ORDER BY business.review_count DESC LIMIT 3;SELECT business.business_id, business.review_count FROM business ORDER BY business.review_count DESC LIMIT 3;SELECT business.bid, business.review_count FROM business ORDER BY business.review_count DESC LIMIT 3;SELECT business.name, business.business_id, business.review_count FROM business ORDER BY business.review_count DESC LIMIT 3;SELECT business.name, business.bid, business.review_count FROM business ORDER BY business.review_count DESC LIMIT 3;SELECT business.business_id, business.bid, business.review_count FROM business ORDER BY business.review_count DESC LIMIT 3;SELECT business.name, business.business_id, business.bid, business.review_count FROM business ORDER BY business.review_count DESC LIMIT 3;",What are the top 3 businesses in terms of review count?,
yelp,mysql,order_by,"SELECT business.name FROM business ORDER BY CASE WHEN business.name IS NULL THEN 1 ELSE 0 END, business.name ASC;","What are the names of the businesses in the database, ordered alphabetically?",
yelp,mysql,order_by,"SELECT review.month, COUNT(review.rid) AS review_count FROM yelp.review WHERE review.year = 2021 GROUP BY review.month ORDER BY STR_TO_DATE(CONCAT('1 ', review.month, ' 2021'), '%d %M %Y');","How many reviews were posted in each month of the year 2021, ordered by the month?",
yelp,mysql,ratio,"SELECT business.state, COUNT(business.business_id) / NULLIF(CAST((SELECT COUNT(*) FROM business) AS DOUBLE), 0) AS ratio FROM business GROUP BY business.state;",What is the ratio of the number of businesses in each state to the total number of businesses in the database?,
yelp,mysql,ratio,"SELECT CAST(SUM(CASE WHEN business.is_open = 1 THEN 1 ELSE 0 END) AS DOUBLE) / NULLIF(SUM(CASE WHEN business.is_open = 0 THEN 1 ELSE 0 END), 0) AS ratio FROM business WHERE LOWER(LOWER(business.city)) LIKE LOWER('%san francisco%');",What is the ratio of open businesses to closed businesses in the city of San Francisco?,
yelp,mysql,ratio,"SELECT CAST(SUM(CASE WHEN checkin.day IN ('Saturday', 'Sunday') THEN checkin.count ELSE 0 END) AS DOUBLE) / NULLIF(SUM(CASE WHEN NOT checkin.day IN ('Saturday', 'Sunday') THEN checkin.count ELSE 0 END), 0) AS ratio FROM checkin JOIN business ON checkin.business_id = business.business_id WHERE LOWER(business.name) LIKE LOWER('%Mark''s Bistro%');","What is the ratio of check-ins on weekends to check-ins on weekdays for the business named ""Mark's Bistro""?",
yelp,mysql,ratio,"SELECT CAST(COUNT(CASE WHEN business.state = 'CA' THEN 1 END) AS DOUBLE) / NULLIF(COUNT(CASE WHEN business.state = 'NY' THEN 1 END), 0) AS ratio FROM business;",What is the ratio of businesses in the state of California to businesses in the state of New York?,
yelp,mysql,ratio,"SELECT category.category_name, CAST(COUNT(CASE WHEN review.rating > 3 THEN 1 END) AS DOUBLE) / NULLIF(COUNT(CASE WHEN review.rating < 3 THEN 1 END), 0) AS ratio FROM review JOIN category ON review.business_id = category.business_id GROUP BY category.category_name ORDER BY ratio DESC;SELECT category.id, CAST(COUNT(CASE WHEN review.rating > 3 THEN 1 END) AS DOUBLE) / NULLIF(COUNT(CASE WHEN review.rating < 3 THEN 1 END), 0) AS ratio FROM review JOIN category ON review.business_id = category.business_id GROUP BY category.id ORDER BY ratio DESC;SELECT category.category_name, category.id, CAST(COUNT(CASE WHEN review.rating > 3 THEN 1 END) AS DOUBLE) / NULLIF(COUNT(CASE WHEN review.rating < 3 THEN 1 END), 0) AS ratio FROM review JOIN category ON review.business_id = category.business_id GROUP BY category.category_name, category.id ORDER BY ratio DESC;","How does the ratio of positive reviews (rating > 3) to negative reviews (rating < 3) vary across different categories of businesses, ordered by descending ratio?",
yelp,mysql,table_join,"SELECT users.name, COUNT(review.rid) AS review_count FROM review JOIN neighbourhood ON review.business_id = neighbourhood.business_id JOIN users ON review.user_id = users.user_id WHERE LOWER(neighbourhood.neighbourhood_name) LIKE LOWER('%Downtown%') GROUP BY users.name ORDER BY review_count DESC;SELECT users.user_id, COUNT(review.rid) AS review_count FROM review JOIN neighbourhood ON review.business_id = neighbourhood.business_id JOIN users ON review.user_id = users.user_id WHERE LOWER(neighbourhood.neighbourhood_name) LIKE LOWER('%Downtown%') GROUP BY users.user_id ORDER BY review_count DESC;SELECT users.name, users.user_id, COUNT(review.rid) AS review_count FROM review JOIN neighbourhood ON review.business_id = neighbourhood.business_id JOIN users ON review.user_id = users.user_id WHERE LOWER(neighbourhood.neighbourhood_name) LIKE LOWER('%Downtown%') GROUP BY users.name, users.user_id ORDER BY review_count DESC;","Which users have posted reviews for businesses located in the neighbourhood of ""Downtown"" and how many reviews have they posted?",
yelp,mysql,table_join,"SELECT category.category_name, SUM(business.review_count) AS total_reviews FROM business JOIN category ON business.business_id = category.business_id WHERE business.state = 'CA' GROUP BY category.category_name ORDER BY total_reviews DESC;SELECT category.id, SUM(business.review_count) AS total_reviews FROM business JOIN category ON business.business_id = category.business_id WHERE business.state = 'CA' GROUP BY category.id ORDER BY total_reviews DESC;SELECT category.category_name, category.id, SUM(business.review_count) AS total_reviews FROM business JOIN category ON business.business_id = category.business_id WHERE business.state = 'CA' GROUP BY category.category_name, category.id ORDER BY total_reviews DESC;","What is the total number of reviews for each category in the state of ""California""?",
yelp,mysql,table_join,"SELECT category.category_name, SUM(business.review_count) AS total_reviews FROM business JOIN category ON business.business_id = category.business_id GROUP BY category.category_name ORDER BY total_reviews DESC;SELECT category.id, SUM(business.review_count) AS total_reviews FROM business JOIN category ON business.business_id = category.business_id GROUP BY category.id ORDER BY total_reviews DESC;SELECT category.category_name, category.id, SUM(business.review_count) AS total_reviews FROM business JOIN category ON business.business_id = category.business_id GROUP BY category.category_name, category.id ORDER BY total_reviews DESC;",What is the total number of reviews for each business category?,
yelp,mysql,table_join,"SELECT business.business_id, SUM(checkin.count) AS total_checkins FROM business JOIN checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' GROUP BY business.business_id ORDER BY total_checkins DESC;SELECT business.name, SUM(checkin.count) AS total_checkins FROM business JOIN checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' GROUP BY business.name ORDER BY total_checkins DESC;SELECT business.bid, SUM(checkin.count) AS total_checkins FROM business JOIN checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' GROUP BY business.bid ORDER BY total_checkins DESC;SELECT business.business_id, business.name, SUM(checkin.count) AS total_checkins FROM business JOIN checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' GROUP BY business.business_id, business.name ORDER BY total_checkins DESC;SELECT business.business_id, business.bid, SUM(checkin.count) AS total_checkins FROM business JOIN checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' GROUP BY business.business_id, business.bid ORDER BY total_checkins DESC;SELECT business.name, business.bid, SUM(checkin.count) AS total_checkins FROM business JOIN checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' GROUP BY business.name, business.bid ORDER BY total_checkins DESC;SELECT business.business_id, business.name, business.bid, SUM(checkin.count) AS total_checkins FROM business JOIN checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' GROUP BY business.business_id, business.name, business.bid ORDER BY total_checkins DESC;",What is the total number of check-ins for each business in the state of California?,
yelp,mysql,table_join,"SELECT category.category_name FROM (SELECT business.business_id, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id GROUP BY business.business_id) AS business_rating JOIN category ON business_rating.business_id = category.business_id GROUP BY category.category_name ORDER BY AVG(business_rating.average_rating) DESC LIMIT 2;SELECT category.id FROM (SELECT business.business_id, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id GROUP BY business.business_id) AS business_rating JOIN category ON business_rating.business_id = category.business_id GROUP BY category.id ORDER BY AVG(business_rating.average_rating) DESC LIMIT 2;SELECT category.category_name, category.id FROM (SELECT business.business_id, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id GROUP BY business.business_id) AS business_rating JOIN category ON business_rating.business_id = category.business_id GROUP BY category.category_name, category.id ORDER BY AVG(business_rating.average_rating) DESC LIMIT 2;",What are the top 2 categories of businesses with the highest average rating?,
yelp,mysql,instruct,SELECT COUNT(review.rid) AS total_reviews FROM review JOIN category ON review.business_id = category.business_id WHERE review.year = 2021 AND LOWER(category.category_name) LIKE LOWER('%Cafe%');,"What is the total number of reviews posted in the year 2021 for businesses in the category ""Cafe""?","Filter strings of users, city, address, business.name using LIKE with wildcards.
Filter strings of state using exact upper case matches.
Assume the rating of a business to be its average rating, and compute it before computing other aggregates on it.
"
yelp,mysql,instruct,"SELECT AVG(sf.average_rating) AS sf_average_rating FROM (SELECT business.business_id, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id WHERE LOWER(LOWER(business.city)) LIKE LOWER('%san francisco%') GROUP BY business.business_id) AS sf;",What is the average rating of businesses in the city of San Francisco?,"Filter strings of users, city, address, business.name using LIKE with wildcards.
Filter strings of state using exact upper case matches.
The rating of businesses in a city refers to the average rating of the businesses in that city. I.e., you must compute the average rating of each business before computing the average rating of businesses in the city.
"
yelp,mysql,instruct,"SELECT review.business_id, COUNT(*) AS review_count FROM review WHERE review.year = 2021 GROUP BY review.business_id ORDER BY review_count DESC;",How many reviews were posted for each business id in the year 2021?,"Filter strings of users, city, address, business.name using LIKE with wildcards.
Filter strings of state using exact upper case matches.
Assume the rating of a business to be its average rating, and compute it before computing other aggregates on it.
"
yelp,mysql,instruct,SELECT COUNT(*) FROM review JOIN users ON review.user_id = users.user_id WHERE LOWER(users.name) LIKE LOWER('%Sarah Williams%') AND review.month = 'April' AND review.year = 2021;,"How many reviews were posted by users with the name ""Sarah Williams"" in the month of April 2021?","Filter strings of users, city, address, business.name using LIKE with wildcards.
Filter strings of state using exact upper case matches.
Assume the rating of a business to be its average rating, and compute it before computing other aggregates on it.
"
yelp,mysql,instruct,SELECT SUM(checkin.count) AS total_checkins FROM business JOIN checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' AND LOWER(checkin.day) LIKE LOWER('%Monday%');,How many check-ins occurred on Mondays at businesses in the state of California?,"Filter strings of users, city, address, business.name using LIKE with wildcards.
Filter strings of state using exact upper case matches.
Assume the rating of a business to be its average rating, and compute it before computing other aggregates on it.
"
broker,mysql,date_functions,"WITH SellTransactions AS (SELECT sbTxCustId, COUNT(*) AS num_tx FROM sbTransaction WHERE CAST(sbTxDateTime AS DATE) = '2023-04-01' AND sbTxType = 'sell' GROUP BY sbTxCustId) SELECT c.sbCustId, c.sbCustName, st.num_tx FROM sbCustomer AS c JOIN SellTransactions AS st ON c.sbCustId = st.sbTxCustId ORDER BY CASE WHEN st.num_tx IS NULL THEN 1 ELSE 0 END DESC, st.num_tx DESC LIMIT 1;","Return the customer who made the most sell transactions on 2023-04-01. Return the id, name and number of transactions.",
broker,mysql,date_functions,"SELECT DATE_ADD('0000-01-01 00:00:00', INTERVAL (TIMESTAMPDIFF(MONTH, '0000-01-01 00:00:00', sbTxDateTime)) MONTH) AS MONTH, AVG(sbTxPrice) AS avg_price FROM sbTransaction WHERE sbTxStatus = 'success' AND sbTxDateTime BETWEEN '2023-01-01' AND '2023-03-31' GROUP BY MONTH ORDER BY CASE WHEN MONTH IS NULL THEN 1 ELSE 0 END, MONTH;SELECT DATE_FORMAT(sbTxDateTime, '%Y-%m-01') AS MONTH, AVG(sbTxPrice) AS avg_price FROM sbTransaction WHERE sbTxStatus = 'success' AND sbTxDateTime BETWEEN '2023-01-01' AND '2023-03-31' GROUP BY MONTH ORDER BY CASE WHEN MONTH IS NULL THEN 1 ELSE 0 END, MONTH;",What is the monthly average transaction price for successful transactions in the 1st quarter of 2023?,
broker,mysql,date_functions,SELECT MIN(sdp.sbDpClose) AS lowest_price FROM sbDailyPrice AS sdp JOIN sbTicker AS st ON sdp.sbDpTickerId = st.sbTickerId WHERE st.sbTickerSymbol = 'VTI' AND sdp.sbDpDate >= CURRENT_DATE - INTERVAL '7' DAY;,Lowest daily closest price for symbol `VTI` in the past 7 days,
broker,mysql,date_functions,"SELECT c.sbCustId, DATEDIFF(t.FirstTxDate, c.sbCustJoinDate) AS DaysFromJoinToFirstTransaction FROM broker.sbCustomer AS c INNER JOIN (SELECT sbTxCustId, MIN(sbTxDateTime) AS FirstTxDate FROM broker.sbTransaction GROUP BY sbTxCustId) AS t ON c.sbCustId = t.sbTxCustId;",Return the customer id and the difference between their time from joining to their first transaction. Ignore customers who haven't made any transactions.,
broker,mysql,date_functions,SELECT COUNT(t.sbTxCustId) AS transaction_count FROM sbTransaction AS t JOIN sbCustomer AS c ON t.sbTxCustId = c.sbCustId WHERE c.sbCustJoinDate >= CURRENT_DATE - INTERVAL '70' DAY;,number of transactions by users who joined in the past 70 days,
derm_treatment,mysql,date_functions,"SELECT t.treatment_id, t.start_dt, ae.reported_dt, ae.description FROM adverse_events AS ae JOIN treatments AS t ON ae.treatment_id = t.treatment_id WHERE ae.reported_dt BETWEEN t.start_dt AND t.start_dt + INTERVAL '10' DAY;","Return the treatment id, treatment start date, adverse event date and description of all adverse events that occured within 10 days after starting treatment",
derm_treatment,mysql,date_functions,"WITH doc_first_treatment AS (SELECT d.doc_id, d.last_name, d.year_reg, t.treatment_id, t.start_dt, ROW_NUMBER() OVER (PARTITION BY d.doc_id ORDER BY CASE WHEN t.start_dt IS NULL THEN 1 ELSE 0 END, t.start_dt ASC) AS rn FROM doctors AS d JOIN treatments AS t ON d.doc_id = t.doc_id WHERE d.year_reg = EXTRACT(YEAR FROM CURRENT_DATE) - 2) SELECT last_name, year_reg, start_dt AS first_treatment_date, treatment_id AS first_treatment_id FROM doc_first_treatment WHERE rn = 1;","List the last name, year of registration, and first treatment (date and id) by doctors who were registered 2 years ago.",
derm_treatment,mysql,date_functions,SELECT AVG(YEAR(CURRENT_DATE) - YEAR(date_of_birth)) AS avg_age FROM derm_treatment.patients WHERE gender = 'Male' AND ins_type = 'private';,what is average age of all registered male patients with private insurance currently?,
derm_treatment,mysql,date_functions,"SELECT t.treatment_id, t.start_dt AS treatment_start_date, t.end_dt AS treatment_end_date, cm.start_dt AS concomitant_med_start_date, cm.end_dt AS concomitant_med_end_date FROM derm_treatment.treatments AS t JOIN derm_treatment.concomitant_meds AS cm ON t.treatment_id = cm.treatment_id WHERE t.is_placebo = TRUE AND cm.start_dt BETWEEN t.start_dt AND DATE_ADD(t.start_dt, INTERVAL 14 DAY) ORDER BY CASE WHEN t.treatment_id IS NULL THEN 1 ELSE 0 END, t.treatment_id;","show all placebo treatment id, start and end date, where there concomitant_meds were started within 2 weeks of starting the treatment. also return the start and end dates of all concomitant drug usage.",
derm_treatment,mysql,date_functions,"SELECT COUNT(*) FROM treatments t JOIN diagnoses d ON t.diag_id = d.diag_id JOIN drugs dr ON t.drug_id = dr.drug_id WHERE LOWER(d.diag_name) LIKE LOWER('%psoriasis%') AND dr.fda_appr_dt IS NOT NULL AND t.end_dt >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH);",How many treatments for diagnoses containing 'psoriasis' (match with wildcards case-insensitively) involve drugs that have been FDA-approved and the treatments have ended within the last 6 months from today?,
ewallet,mysql,date_functions,"SELECT AVG(tx_limit_daily) AS avg_daily_limit, AVG(tx_limit_monthly) AS avg_monthly_limit FROM user_setting_snapshot WHERE snapshot_date = (SELECT MIN(snapshot_date) FROM user_setting_snapshot WHERE snapshot_date >= '2023-01-01' AND snapshot_date < '2024-01-01');",What was the average transaction daily and monthly limit for the earliest setting snapshot in 2023?,
ewallet,mysql,date_functions,"SELECT u.username, u.email, u.created_at FROM users AS u LEFT JOIN notifications AS n ON u.uid = n.user_id AND n.created_at BETWEEN u.created_at AND u.created_at + INTERVAL '1' YEAR WHERE n.user_id IS NULL;","Which users did not get a notification within the first year of signing up? Return their usernames, emails and signup dates.",
ewallet,mysql,date_functions,"SELECT device_type, AVG(UNIX_TIMESTAMP((session_end_ts - session_start_ts))) AS avg_session_duration_seconds FROM user_sessions WHERE NOT session_end_ts IS NULL GROUP BY device_type;",what was the average user session duration in seconds split by device_type?,
ewallet,mysql,date_functions,"WITH retail_merchants AS (SELECT mid FROM merchants WHERE LOWER(category) LIKE LOWER('%retail%') AND status = 'active'), merchant_balances AS (SELECT balance FROM wallet_merchant_balance_daily AS wmbd JOIN retail_merchants AS rm ON wmbd.merchant_id = rm.mid WHERE DATE(wmbd.updated_at) = CURRENT_DATE), ranked_balances AS (SELECT balance, ROW_NUMBER() OVER (ORDER BY balance) as `row_number`, COUNT(*) OVER () as total_rows FROM merchant_balances) SELECT AVG(balance) AS median_balance FROM ranked_balances WHERE `row_number` IN (FLOOR((total_rows + 1) / 2), CEIL((total_rows + 1) / 2));",Give me today's median merchant wallet balance for all active merchants whose category contains 'retail',
ewallet,mysql,date_functions,"WITH earliest_coupons AS (SELECT c.merchant_id, MIN(c.start_date) AS earliest_coupon_start_date FROM coupons AS c GROUP BY c.merchant_id) SELECT m.mid AS merchant_id, m.created_at AS merchant_registration_date, ec.earliest_coupon_start_date, c.cid AS earliest_coupon_id FROM merchants AS m JOIN earliest_coupons AS ec ON m.mid = ec.merchant_id JOIN coupons AS c ON ec.merchant_id = c.merchant_id AND ec.earliest_coupon_start_date = c.start_date WHERE ec.earliest_coupon_start_date <= m.created_at + INTERVAL '1' YEAR;","Which merchants earliest coupon start date was within a year of the merchant's registration? Return the merchant id, registration date, and earliest coupon id and start date",
car_dealership,mysql,date_functions,"SELECT s.first_name, s.last_name, s.phone, s.termination_date - s.hire_date AS days_employed FROM salespersons AS s ORDER BY CASE WHEN days_employed IS NULL THEN 1 ELSE 0 END, days_employed ASC LIMIT 1;",Return the name and phone number of the salesperson with the shortest time from being hired to getting fired. Return the number of days he/she was employed for.,
car_dealership,mysql,date_functions,"SELECT COUNT(*) AS weekend_payments FROM payments_made WHERE vendor_name = 'Utility Company' AND DAYOFWEEK(payment_date) IN (1, 7);",Return the number of payments made on weekends to the vendor named 'Utility Company',
car_dealership,mysql,date_functions,"SELECT payment_date, payment_method, SUM(payment_amount) AS total_amount FROM payments_received WHERE payment_date >= DATE_SUB(DATE(DATE_ADD(CURDATE(), INTERVAL -WEEKDAY(CURDATE()) DAY)), INTERVAL 1 WEEK) AND payment_date < DATE(DATE_ADD(CURDATE(), INTERVAL -WEEKDAY(CURDATE()) DAY)) GROUP BY payment_date, payment_method ORDER BY payment_date DESC, payment_method ASC;","show me the daily total amount of payments received in the whole of the previous ISO week not including the current week, split by the payment_method",
car_dealership,mysql,date_functions,"WITH latest_snapshot AS (SELECT MAX(snapshot_date) AS snapshot_date FROM inventory_snapshots WHERE snapshot_date BETWEEN '2023-03-01' AND '2023-03-31'), latest_snapshot_data AS (SELECT inv.car_id FROM inventory_snapshots AS inv JOIN latest_snapshot AS ls ON inv.snapshot_date = ls.snapshot_date WHERE inv.is_in_inventory = TRUE) SELECT c.id, c.make, c.model, c.year FROM cars AS c JOIN latest_snapshot_data AS lsd ON c.id = lsd.car_id;","Which cars were in inventory in the latest snapshot for march 2023? Return the car id, make, model, and year. Cars are considered to be in inventory"" if is_in_inventory is True.""",
car_dealership,mysql,date_functions,"SELECT DATE_ADD('0000-01-01 00:00:00', INTERVAL (TIMESTAMPDIFF(QUARTER, '0000-01-01 00:00:00', s.sale_date)) QUARTER) AS QUARTER, c.state, SUM(s.sale_price) AS total_sales FROM sales AS s JOIN customers AS c ON s.customer_id = c.id WHERE EXTRACT(YEAR FROM s.sale_date) = 2023 GROUP BY c.state, QUARTER HAVING SUM(s.sale_price) > 0 ORDER BY CASE WHEN QUARTER IS NULL THEN 1 ELSE 0 END, QUARTER, CASE WHEN c.state IS NULL THEN 1 ELSE 0 END, c.state;",What were the total quarterly sales in 2023 grouped by customer's state? Represent each quarter as the first date in the quarter.,
