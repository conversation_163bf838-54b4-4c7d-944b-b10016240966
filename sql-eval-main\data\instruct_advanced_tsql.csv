db_name,db_type,query_category,query,question,instructions,full_instructions
broker,tsql,instructions_cte_join,"WITH cust_tx AS (SELECT c.sbCustId AS sbCustId, c.sbCustName AS sbCustName, SUM(t.sbTxAmount) AS total_amount FROM sbCustomer AS c JOIN sbTransaction AS t ON c.sbCustId = t.sbTxCustId GROUP BY c.sbCustId, c.sbCustName) SELECT TOP 5 sbCustName, total_amount FROM cust_tx ORDER BY CASE WHEN total_amount IS NULL THEN 1 ELSE 0 END DESC, total_amount DESC;",Who are the top 5 customers by total transaction amount? Return their name and total amount.,"To get the total transaction amount per customer, join the customer and transaction tables, group by customer, and sum the transaction amounts.","To get the total transaction amount per customer, join the customer and transaction tables, group by customer, and sum the transaction amounts.
TAC = Total Active Customers who have recently joined
MoMC = Month-over-month change in average closing price for each ticker.
ACP = Average Closing Price of tickers over a recent period"
broker,tsql,instructions_cte_join,"WITH popular_stocks AS (SELECT t.sbTickerSymbol AS sbTickerSymbol, COUNT(*) AS tx_count FROM sbTransaction AS tx JOIN sbTicker AS t ON tx.sbTxTickerId = t.sbTickerId WHERE tx.sbTxType = 'buy' AND tx.sbTxDateTime >= DATEADD(DAY, -10, GETDATE()) GROUP BY t.sbTickerSymbol) SELECT TOP 2 sbTickerSymbol, tx_count FROM popular_stocks ORDER BY tx_count DESC;",What are the 2 most frequently bought stock ticker symbols in the past 10 days? Return the ticker symbol and number of buy transactions.,"To find the most popular stocks in the past 10 days, join the transaction and ticker tables, filter for buy transactions in the last 10 days, group by ticker and count transactions.","MoMC = month-over-month change in average closing price Weekend days refer to Saturday and Sunday; adjust dates to weeks for aggregation. To find the most popular stocks in the past 10 days, join the transaction and ticker tables, filter for buy transactions in the last 10 days, group by ticker and count transactions. CR = customer rank by total transaction volume, where rank 1 belongs to the customer with the highest volume"
broker,tsql,instructions_cte_join,"WITH cust_tx_stats AS (SELECT c.sbCustId AS sbCustId, c.sbCustName AS sbCustName, COUNT(t.sbTxId) AS total_tx, SUM(CASE WHEN t.sbTxStatus = 'success' THEN 1 ELSE 0 END) AS success_tx FROM sbCustomer AS c JOIN sbTransaction AS t ON c.sbCustId = t.sbTxCustId GROUP BY c.sbCustId, c.sbCustName) SELECT sbCustName, CAST(success_tx AS FLOAT) / total_tx * 100 AS success_rate FROM cust_tx_stats WHERE total_tx >= 5 ORDER BY CAST(success_tx AS FLOAT) / total_tx;","For customers with at least 5 total transactions, what is their transaction success rate? Return the customer name and success rate, ordered from lowest to highest success rate.","To get the success rate of transactions per customer, join customer and transaction tables, group by customer, and calculate the percentage of successful transactions.","CR = customer rank by total transaction amount, with different rankings based on transaction amounts MoMC = month-over-month change in average closing price based on previous month's averages for each ticker each month To get the success rate of transactions per customer, join customer and transaction tables, group by customer, and calculate the percentage of successful transactions. Always join transactions with customers before using the transactions table. TAC = Total Active Customers who joined after a certain date"
broker,tsql,instructions_cte_join,"WITH stock_stats AS (SELECT t.sbTickerSymbol AS sbTickerSymbol, MIN(d.sbDpLow) AS min_price, MAX(d.sbDpHigh) AS max_price FROM sbDailyPrice AS d JOIN sbTicker AS t ON d.sbDpTickerId = t.sbTickerId WHERE d.sbDpDate BETWEEN '2023-04-01' AND '2023-04-04' GROUP BY t.sbTickerSymbol) SELECT TOP 3 sbTickerSymbol, max_price - min_price AS price_change FROM stock_stats ORDER BY CASE WHEN max_price - min_price IS NULL THEN 1 ELSE 0 END DESC, max_price - min_price DESC;","Which 3 distinct stocks had the highest price change between the low and high from April 1 2023 to April 4 2023? I want the different in the low and high throughout this timerange, not just the intraday price changes. Return the ticker symbol and price change.","To analyze stock performance, join the daily price and ticker tables, filter for a specific date range, and calculate price change.","PMCS = per month customer signups
TAC = Total Active Customers who joined after a certain date
PMAT = per month average transaction amount, using date truncation for aggregation.
To analyze stock performance, join the daily price and ticker tables, filter for a specific date range, and calculate price change.
CR = customer rank by total transaction amount, with the highest transaction amount getting the top rank"
broker,tsql,instructions_cte_window,"WITH monthly_price_stats AS (SELECT DATEFROMPARTS(YEAR(sbDpDate), MONTH(sbDpDate), 1) AS month, sbDpTickerId, AVG(sbDpClose) AS avg_close, MAX(sbDpHigh) AS max_high, MIN(sbDpLow) AS min_low FROM sbDailyPrice GROUP BY DATEFROMPARTS(YEAR(sbDpDate), MONTH(sbDpDate), 1), sbDpTickerId) SELECT t.sbTickerSymbol, mps.month, mps.avg_close, mps.max_high, mps.min_low, (mps.avg_close - LAG(mps.avg_close) OVER (PARTITION BY mps.sbDpTickerId ORDER BY mps.month)) / LAG(mps.avg_close) OVER (PARTITION BY mps.sbDpTickerId ORDER BY mps.month) AS mom_change FROM monthly_price_stats AS mps JOIN sbTicker AS t ON mps.sbDpTickerId = t.sbTickerId;WITH monthly_price_stats AS (SELECT DATEADD(MONTH, DATEDIFF(MONTH, 0, sbDpDate) , 0) AS month, sbDpTickerId, AVG(sbDpClose) AS avg_close, MAX(sbDpHigh) AS max_high, MIN(sbDpLow) AS min_low FROM sbDailyPrice GROUP BY DATEADD(MONTH, DATEDIFF(MONTH, 0, sbDpDate) , 0), sbDpTickerId) SELECT t.sbTickerSymbol, mps.month, mps.avg_close, mps.max_high, mps.min_low, (mps.avg_close - LAG(mps.avg_close) OVER (PARTITION BY mps.sbDpTickerId ORDER BY mps.month)) / LAG(mps.avg_close) OVER (PARTITION BY mps.sbDpTickerId ORDER BY mps.month) AS mom_change FROM monthly_price_stats AS mps JOIN sbTicker AS t ON mps.sbDpTickerId = t.sbTickerId;WITH monthly_price_stats AS (SELECT FORMAT(DATEADD(MONTH, DATEDIFF(MONTH, 0, sbDpDate), 0), 'yyyy-MM') AS month, sbDpTickerId, AVG(sbDpClose) AS avg_close, MAX(sbDpHigh) AS max_high, MIN(sbDpLow) AS min_low FROM sbDailyPrice GROUP BY FORMAT(DATEADD(MONTH, DATEDIFF(MONTH, 0, sbDpDate), 0), 'yyyy-MM'), sbDpTickerId) SELECT t.sbTickerSymbol, mps.month, mps.avg_close, mps.max_high, mps.min_low, (mps.avg_close - LAG(mps.avg_close) OVER (PARTITION BY mps.sbDpTickerId ORDER BY mps.month)) / LAG(mps.avg_close) OVER (PARTITION BY mps.sbDpTickerId ORDER BY mps.month) AS mom_change FROM monthly_price_stats AS mps JOIN sbTicker AS t ON mps.sbDpTickerId = t.sbTickerId;","What is the ticker symbol, month, average closing price, highest price, lowest price, and MoMC for each ticker by month?","MoMC = month-over-month change in average closing price = (avg_close_given_month - avg_close_previous_month) / avg_close_previous_month for each ticker symbol each month. Recall that we want the symbol, and not just the ticker id.","NPM (Net Profit Margin) = (Total Income from Sales - (Tax + Commission Expenses)) / Total Income from Sales * 100, for recent transactions. TAC = Total Active Customers who have recently joined ACP = Average Closing Price of tickers over a recent period MoMC = month-over-month change in average closing price = (avg_close_given_month - avg_close_previous_month) / avg_close_previous_month for each ticker each month"
broker,tsql,instructions_cte_window,"WITH cust_tx_counts AS (SELECT sbTxCustId AS sbTxCustId, COUNT(*) AS num_tx, SUM(sbTxAmount) AS total_amount FROM sbTransaction GROUP BY sbTxCustId) SELECT c.sbCustName, ct.num_tx, ct.total_amount, RANK() OVER (ORDER BY CASE WHEN ct.total_amount IS NULL THEN 1 ELSE 0 END DESC, ct.total_amount DESC) AS cust_rank FROM cust_tx_counts AS ct JOIN sbCustomer AS c ON ct.sbTxCustId = c.sbCustId;","Return the customer name, number of transactions, total transaction amount, and CR for all customers","CR = customer rank by total transaction amount, with rank 1 being the customer with the highest total transaction amount","ACP = Average Closing Price of tickers over a select period of days
TAC = Total Active Customers who have recently joined
NPM (Net Profit Margin) = Net income divided by net sales for a specific time frame, expressed as a percentage for transactions in a recent period.
CR = customer rank by total transaction amount, with rank 1 being the customer with the highest total transaction amount"
broker,tsql,instructions_date_join,"SELECT FORMAT(sbCustJoinDate, 'yyyy-MM') AS MONTH, COUNT(sbCustId) AS customer_signups, AVG(t.sbTxAmount) AS avg_tx_amount FROM sbCustomer AS c LEFT JOIN sbTransaction AS t ON c.sbCustId = t.sbTxCustId AND FORMAT(t.sbTxDateTime, 'yyyy-MM') = FORMAT(c.sbCustJoinDate, 'yyyy-MM') WHERE sbCustJoinDate >= DATEADD(MONTH, -6, DATEADD(DAY, 1, EOMONTH(GETDATE(), -1))) AND sbCustJoinDate < DATEADD(DAY, 1, EOMONTH(GETDATE(), -1)) GROUP BY FORMAT(sbCustJoinDate, 'yyyy-MM');SELECT FORMAT(sbCustJoinDate, 'yyyy-MM-dd HH:mm:ss') AS JoinDateTime, COUNT(sbCustId) AS customer_signups, AVG(t.sbTxAmount) AS avg_tx_amount FROM sbCustomer AS c LEFT JOIN sbTransaction AS t ON c.sbCustId = t.sbTxCustId AND FORMAT(t.sbTxDateTime, 'yyyy-MM') = FORMAT(c.sbCustJoinDate, 'yyyy-MM') WHERE sbCustJoinDate >= DATEADD(MONTH, -6, DATEADD(DAY, 1, EOMONTH(GETDATE(), -1))) AND sbCustJoinDate < DATEADD(DAY, 1, EOMONTH(GETDATE(), -1)) GROUP BY FORMAT(sbCustJoinDate, 'yyyy-MM-dd HH:mm:ss');SELECT FORMAT(sbCustJoinDate, 'yyyy-MM-dd') AS JoinDateTime, COUNT(sbCustId) AS customer_signups, AVG(t.sbTxAmount) AS avg_tx_amount FROM sbCustomer AS c LEFT JOIN sbTransaction AS t ON c.sbCustId = t.sbTxCustId AND FORMAT(t.sbTxDateTime, 'yyyy-MM') = FORMAT(c.sbCustJoinDate, 'yyyy-MM') WHERE sbCustJoinDate >= DATEADD(MONTH, -6, DATEADD(DAY, 1, EOMONTH(GETDATE(), -1))) AND sbCustJoinDate < DATEADD(DAY, 1, EOMONTH(GETDATE(), -1)) GROUP BY FORMAT(sbCustJoinDate, 'yyyy-MM-dd');",What are the PMCS and PMAT for customers who signed up in the last 6 months excluding the current month?,PMCS = per month customer signups. PMAT = per month average transaction amount. Truncate date to month for aggregation.,"ACP = Average Closing Price of tickers over a selected period, including the end day
CR = customer rank by total transaction amount, where the top customer has the highest amount.
PMCS = per month customer signups. PMAT = per month average transaction amount. Truncate date to month for aggregation.
TAC = Total Active Customers who joined after a specified date"
broker,tsql,instructions_date_join,"SELECT COUNT(t.sbTxId) AS num_transactions, SUM(t.sbTxAmount) AS total_amount FROM sbTransaction AS t JOIN sbCustomer AS c ON t.sbTxCustId = c.sbCustId WHERE c.sbCustCountry = 'USA' AND t.sbTxDateTime BETWEEN DATEADD(WEEK, DATEDIFF(WEEK, 0, GETDATE()) - 1, 0) AND DATEADD(SECOND, -1, DATEADD(WEEK, DATEDIFF(WEEK, 0, GETDATE()), 0));",How many transactions were made by customers from the USA last week (exclusive of the current week)? Return the number of transactions and total transaction amount.,Last week = (start of this week - 1 week) to (start of this week - 1 second). Always join transactions with customers before using the transactions table.,"To analyze stock performance, join the daily price and ticker tables and calculate price change To get the total transaction amount per customer, join the customer and transaction tables, group by customer, and sum the transaction amounts. Last week = (start of this week - 1 week) to (start of this week - 1 second). Always join transactions with customers before using the transactions table. To get the success rate of transactions per customer, join customer and transaction tables, group by customer, and calculate the percentage of successful transactions"
broker,tsql,instructions_date_join,"SELECT DATEADD(WEEK, DATEDIFF(WEEK, 0, t.sbTxDateTime), 0) AS WEEK, COUNT(t.sbTxId) AS num_transactions, COUNT(CASE WHEN DATEPART(WEEKDAY, t.sbTxDateTime) IN (1, 7) THEN 1 END) AS weekend_transactions FROM sbTransaction AS t JOIN sbTicker AS tk ON t.sbTxTickerId = tk.sbTickerId WHERE tk.sbTickerType = 'stock' AND t.sbTxDateTime >= DATEADD(WEEK, -8, DATEADD(WEEK, DATEDIFF(WEEK, 0, GETDATE()), 0)) AND t.sbTxDateTime < DATEADD(WEEK, DATEDIFF(WEEK, 0, GETDATE()), 0) GROUP BY DATEADD(WEEK, DATEDIFF(WEEK, 0, t.sbTxDateTime), 0);",How many transactions for stocks occurred in each of the last 8 weeks excluding the current week? How many of these transactions happened on weekends?,Weekend days are Saturday and Sunday. Truncate date to week for aggregation.,"For an analysis of stock performance, link daily price data with ticker information, select a relevant time period, and calculate the change in price
To determine the success rate of transactions for each customer, merge customer and transaction records, group by customer ID, and compute the ratio of successful transactions.
Weekend days are Saturday and Sunday. Truncate date to week for aggregation.
To get the total amount of transactions per customer, perform a join between customer and transaction datasets, then group by customer ID and sum up the transaction amounts"
broker,tsql,instructions_date_join,"WITH active_customers AS (SELECT c.sbCustId AS sbCustId, COUNT(t.sbTxId) AS num_transactions FROM sbCustomer AS c JOIN sbTransaction AS t ON c.sbCustId = t.sbTxCustId AND DATEPART(YEAR, c.sbCustJoinDate) = DATEPART(YEAR, t.sbTxDateTime) AND DATEPART(MONTH, c.sbCustJoinDate) = DATEPART(MONTH, t.sbTxDateTime) GROUP BY c.sbCustId) SELECT TOP 1 ac.sbCustId, c.sbCustName, ac.num_transactions FROM active_customers AS ac JOIN sbCustomer AS c ON ac.sbCustId = c.sbCustId ORDER BY ac.num_transactions DESC;","Which customer made the highest number of transactions in the same month as they signed up? Return the customer's id, name and number of transactions.","If transactions from 2 different tables from the same interval are to be joined, join on the respective truncated date fields eg `FROM t1 JOIN t2 ON DATEADD('<interval>', DATEDIFF('<interval>', 0, t1.date), 0) = DATEADD('<interval>', DATEDIFF('<interval>', 0, t2.date), 0).","To analyze the performance of stocks, join the tables for daily prices and tickers, filter for a designated time frame, and calculate the change in price To get the total transaction amount by each customer, you should join the customer and transaction tables, group by the customer, and sum up the amounts of the transactions If transactions from 2 different tables from the same interval are to be joined, join on the respective truncated date fields eg `FROM t1 JOIN t2 ON DATEADD('<interval>', DATEDIFF('<interval>', 0, t1.date), 0) = DATEADD('<interval>', DATEDIFF('<interval>', 0, t2.date), 0). To identify the most popular stocks within a recent period, join the transaction table with the ticker table, filter for buy transactions during the last several days, group by the ticker symbol, and count the number of transactions."
broker,tsql,instructions_string_matching,SELECT COUNT(DISTINCT t.sbTxCustId) FROM sbTransaction AS t JOIN sbCustomer AS c ON t.sbTxCustId = c.sbCustId JOIN sbTicker AS tk ON t.sbTxTickerId = tk.sbTickerId WHERE c.sbCustEmail LIKE '%.com' AND (tk.sbTickerSymbol LIKE 'AMZN' OR tk.sbTickerSymbol LIKE 'AAPL' OR tk.sbTickerSymbol LIKE 'GOOGL' OR tk.sbTickerSymbol LIKE 'META' OR tk.sbTickerSymbol LIKE 'NFLX');,"How many distinct customers with a .com email address bought stocks of FAANG companies (Amazon, Apple, Google, Meta or Netflix)?","Customers with a '.com' email address who bought FAANG stocks (ticker symbol contains 'AMZN', 'AAPL', 'GOOGL', 'META' or 'NFLX')","MoMC = month-over-month change in average closing price = (average closing price of given month - average closing price of previous month) / average closing price of previous month for each ticker. ACP = Average Closing Price of tickers within a defined time frame TAC = Total Active Customers who have joined since a specific starting date Customers with a '.com' email address who bought FAANG stocks (ticker symbol contains 'AMZN', 'AAPL', 'GOOGL', 'META' or 'NFLX')"
broker,tsql,instructions_string_matching,SELECT COUNT(sbCustId) FROM sbCustomer WHERE (LOWER(sbCustName) LIKE 'j%' OR LOWER(sbCustName) LIKE '%ez') AND LOWER(sbCustState) LIKE '%a';,"What is the number of customers whose name starts with J or ends with 'ez', and who live in a state ending with the letter 'a'?",all customer attributes should be matched case insensitively,"To get the total transaction amount per customer, join the customer and transaction tables, group by customer, and sum the transaction amounts To find the most popular stocks over a recent period, join the transaction and ticker tables, filter for buy transactions within that period, group by ticker, and count transactions. To analyze stock performance, join the daily price and ticker tables, filter for a specific date range, and calculate price change all customer attributes should be matched case insensitively"
broker,tsql,keywords_aggregate,"SELECT sbCustCountry, COUNT(sbCustId) AS TAC FROM sbCustomer WHERE sbCustJoinDate >= '2023-01-01' GROUP BY sbCustCountry;","How many TAC are there from each country, for customers who joined on or after January 1, 2023? Return the country and the count.","TAC = Total Active Customers who joined on or after January 1, 2023","To analyze stock performance, join the daily price and stock symbol tables, filter for a designated date range, and compute the variation in price
To calculate the success rate of transactions for each customer, merge the customer and transaction records, aggregate by customer, and assess the rate of successful transactions
TAC = Total Active Customers who joined on or after January 1, 2023
To ascertain the most sought-after stocks within a recent timeframe, combine the transaction and stock symbol datasets, focus on purchase actions during the specified period, group by stock symbol, and tally the transactions."
broker,tsql,keywords_aggregate,"SELECT sbTicker.sbTickerType, AVG(sbDailyPrice.sbDpClose) AS ACP FROM sbDailyPrice JOIN sbTicker ON sbDailyPrice.sbDpTickerId = sbTicker.sbTickerId WHERE sbDailyPrice.sbDpDate >= DATEADD(DAY, -7, GETDATE()) GROUP BY sbTicker.sbTickerType;","What is the ACP for each ticker type in the past 7 days, inclusive of today? Return the ticker type and the average closing price.","ACP = Average Closing Price of tickers in the last 7 days, inclusive of today","To determine the Activity Ratio (AR), divide the number of active customers by the total customer count and then multiply by 100, focusing on customers who joined within a particular year
Customer Rank (CR) is determined by ranking customers based on their total transaction amount, with rank 1 assigned to the customer with the highest amount.
To calculate Net Profit Margin (NPM), subtract the sum of tax and commission from the total amount obtained from sales, then divide by the total sales amount and multiply by 100, assessing transactions within a specific period
ACP = Average Closing Price of tickers in the last 7 days, inclusive of today"
broker,tsql,keywords_ratio,"SELECT c.sbCustCountry, COALESCE(100.0 * COUNT(DISTINCT CASE WHEN c.sbCustStatus = 'active' THEN c.sbCustId END) / NULLIF(COUNT(DISTINCT t.sbTxCustId), 0), 0) AS AR FROM sbCustomer AS c JOIN sbTransaction AS t ON c.sbCustId = t.sbTxCustId WHERE c.sbCustJoinDate BETWEEN '2022-01-01' AND '2022-12-31' GROUP BY c.sbCustCountry;",What is the AR for each country for customers who joined in 2022? Return the country and AR.,AR (Activity Ratio) = (Number of Active Customers with Transactions / Total Number of Customers with Transactions) * 100.,"To get the total transaction amount per customer, join the customer and transaction tables, group by customer, and sum the transaction amounts If transactions from 2 different tables from the same interval are to be joined, join on the respective truncated date fields. AR (Activity Ratio) = (Number of Active Customers with Transactions / Total Number of Customers with Transactions) * 100. To analyze stock performance, join the daily price and ticker tables, filter for a specific date range, and calculate the price change"
broker,tsql,keywords_ratio,"SELECT sbTickerSymbol, CASE WHEN SUM(sbTxAmount) = 0 THEN NULL ELSE (SUM(sbTxAmount) - SUM(sbTxTax + sbTxCommission)) / SUM(sbTxAmount) * 100 END AS SPM FROM sbTransaction JOIN sbTicker ON sbTransaction.sbTxTickerId = sbTicker.sbTickerId WHERE sbTxType = 'sell' AND sbTxDateTime >= DATEADD(MONTH, -1, GETDATE()) GROUP BY sbTickerSymbol;","What is the SPM for each ticker symbol from sell transactions in the past month, inclusive of 1 month ago? Return the ticker symbol and SPM.",SPM (Selling Profit Margin) = (Total Amount from Sells - (Tax + Commission)) / Total Amount from Sells * 100,"To calculate the Total Active Customers who have recently joined, the Average Closing Price of tickers within a recent period, and the Customer Rank by their total transaction volume, you would need different queries and calculations that are not directly related to calculating the Net Profit Margin (NPM) for sell transactions of ticker symbols in the past month. ACP = Calculate the Average Closing Price of tickers over a chosen recent time span Analyze customer signups over a certain period.
SPM (Selling Profit Margin) = (Total Amount from Sells - (Tax + Commission)) / Total Amount from Sells * 100
TAC = Total Active Customers who joined within a specified timeframe
CR = Rank customers by their total transaction volume, identifying the customer with the highest transaction volume as rank 1. This involves joining price data with ticker identifiers and filtering for a specified date range."
car_dealership,tsql,instructions_cte_join,"WITH sale_payments AS (SELECT s.id AS sale_id, s.sale_date AS sale_date, MAX(p.payment_date) AS latest_payment_date FROM sales AS s JOIN payments_received AS p ON s.id = p.sale_id GROUP BY s.id, s.sale_date) SELECT ROUND(AVG(DATEDIFF(day, sale_date, latest_payment_date)), 2) AS avg_days_to_payment FROM sale_payments;","What is the average number of days between the sale date and payment received date, rounded to 2 decimal places?","When getting duration between sale and payment date for each sale, get the latest payment for sale by aggregating over the payments_received table first.","When getting duration between sale and payment date for each sale, get the latest payment for sale by aggregating over the payments_received table first. ASP = Calculate the average price of sales within a specific timeframe Last 30 days = Use a range from the current date minus a certain interval to the current date, always ensure to make the necessary joins before utilizing the sales data. TSC = Count of sales within a specified period"
car_dealership,tsql,instructions_cte_join,"WITH latest_inventory_status AS (SELECT car_id AS car_id, is_in_inventory AS is_in_inventory, ROW_NUMBER() OVER (PARTITION BY car_id ORDER BY CASE WHEN snapshot_date IS NULL THEN 1 ELSE 0 END DESC, snapshot_date DESC) AS rn FROM inventory_snapshots) SELECT c.make, c.model, MAX(s.sale_price) AS highest_sale_price FROM cars AS c JOIN sales AS s ON c.id = s.car_id JOIN latest_inventory_status AS lis ON c.id = lis.car_id WHERE lis.is_in_inventory = 0 AND lis.rn = 1 GROUP BY c.make, c.model ORDER BY MAX(s.sale_price) DESC;","Return the highest sale price for each make and model of cars that have been sold and are no longer in inventory, ordered by the sale price from highest to lowest. Use the most recent date in the inventory_snapshots table to determine that car's inventory status.","When getting a car's inventory status, always take the latest status from the inventory_snapshots table","Recall that a car can have multiple entries in the inventory_snapshot table. 
TSC = Count of sales within a specified period
MoM = Change in total receivable amounts from one month to the next, comparing with the immediately preceding month.
ASP = Mean sale price for a designated start period
When getting a car's inventory status, always take the latest status from the inventory_snapshots table"
car_dealership,tsql,instructions_cte_join,"WITH salesperson_sales AS (SELECT s.id, s.first_name, s.last_name, SUM(sa.sale_price) AS total_sales FROM salespersons AS s LEFT JOIN sales AS sa ON s.id = sa.salesperson_id GROUP BY s.id, s.first_name, s.last_name) SELECT TOP 5 id, first_name, last_name, total_sales FROM salesperson_sales ORDER BY total_sales DESC;","Who are the top 5 salespersons by total sales amount? Return their ID, first name, last name and total sales amount.","To get the total sales amount per salesperson, join the salespersons and sales tables, group by salesperson, and sum the sale_price. Always order results with NULLS last.","PMSR = per month sales revenue
Always join sales with cars before using the sales table
Weekend days are Saturday and Sunday
Truncate date to month for aggregation
Last 30 days = DATEADD(DAY, -30, CAST(GETDATE() AS DATE)) to CAST(GETDATE() AS DATE)
PMSPS = per month salesperson signups
To get the total sales amount per salesperson, join the salespersons and sales tables, group by salesperson, and sum the sale_price. Always order results with NULLS last.
Truncate date to week for aggregation."
car_dealership,tsql,instructions_cte_join,"WITH recent_sales AS (SELECT sp.id AS id, sp.first_name AS first_name, sp.last_name AS last_name, COUNT(s.id) AS num_sales FROM salespersons AS sp LEFT JOIN sales AS s ON sp.id = s.salesperson_id WHERE s.sale_date >= DATEADD(DAY, -30, GETDATE()) GROUP BY sp.id, sp.first_name, sp.last_name) SELECT id, first_name, last_name, num_sales FROM recent_sales ORDER BY num_sales DESC;WITH recent_sales AS (SELECT sp.id AS id, sp.first_name AS first_name, sp.last_name AS last_name, COUNT(s.id) AS num_sales FROM salespersons AS sp LEFT JOIN sales AS s ON sp.id = s.salesperson_id AND s.sale_date >= DATEADD(DAY, -30, GETDATE()) GROUP BY sp.id, sp.first_name, sp.last_name) SELECT id, first_name, last_name, num_sales FROM recent_sales ORDER BY num_sales DESC;","How many sales did each salesperson make in the past 30 days, inclusive of today's date? Return their ID, first name, last name and number of sales made, ordered from most to least sales.","To get the number of sales made by each salesperson in the past 30 days, join the salespersons and sales tables and filter for sales in the last 30 days.","When using car makes, model names, engine_type, and vin_number, ensure matching is case-insensitive and allows for partial matches using LIKE with wildcards.
To get the number of sales made by each salesperson in the past 30 days, join the salespersons and sales tables and filter for sales in the last 30 days.
ASP = Calculate the average sale price without specifying the period
GPM = Define gross profit margin as a ratio without specifying how to calculate total revenue or total cost"
car_dealership,tsql,instructions_cte_window,"WITH salesperson_sales AS (SELECT salesperson_id AS salesperson_id, SUM(sale_price) AS total_sales, COUNT(*) AS num_sales FROM sales GROUP BY salesperson_id) SELECT s.first_name, s.last_name, ss.total_sales, ss.num_sales, RANK() OVER (ORDER BY CASE WHEN ss.total_sales IS NULL THEN 1 ELSE 0 END DESC, ss.total_sales DESC) AS sales_rank FROM salesperson_sales AS ss JOIN salespersons AS s ON ss.salesperson_id = s.id;","Return the first name, last name, total sales amount, number of sales, and SR for each salesperson",SR = sales rank of each salesperson ordered by their total sales amount descending,"SR = sales rank of each salesperson ordered by their total sales amount descending To determine the sales performance per territory, sum the sales amount and count the sales, grouping by territory To calculate the average sale price, join the sales table with itself on the salesperson_id and find the ratio of total sales amount to number of sales To assess inventory turnover, compare inventory snapshots with sales on matching days, focusing on the quantity of items sold."
car_dealership,tsql,instructions_cte_window,"WITH RecursiveDates AS (SELECT DATEADD(MONTH, DATEDIFF(MONTH, 0, MIN(payment_date)), 0) AS dt, DATEADD(MONTH, DATEDIFF(MONTH, 0, MAX(payment_date)), 0) AS max_date FROM payments_received UNION ALL SELECT DATEADD(MONTH, 1, dt), max_date FROM RecursiveDates WHERE dt < max_date ), monthly_totals AS (SELECT DATEADD(MONTH, DATEDIFF(MONTH, 0, payment_date), 0) AS dt, SUM(payment_amount) AS total_payments FROM payments_received GROUP BY DATEADD(MONTH, DATEDIFF(MONTH, 0, payment_date), 0)), monthly_totals_with_zero AS (SELECT rd.dt, COALESCE(mt.total_payments, 0) AS total_payments FROM RecursiveDates rd LEFT JOIN monthly_totals mt ON rd.dt = mt.dt) SELECT CAST(m.dt AS DATE) AS MONTH, m.total_payments, m.total_payments - LAG(m.total_payments, 1) OVER (ORDER BY m.dt) AS mom_change FROM monthly_totals_with_zero m ORDER BY m.dt OPTION (MAXRECURSION 0);WITH RecursiveDates AS (SELECT DATEADD(MONTH, DATEDIFF(MONTH, 0, MIN(payment_date)), 0) AS dt, DATEADD(MONTH, DATEDIFF(MONTH, 0, MAX(payment_date)), 0) AS max_date FROM payments_received UNION ALL SELECT DATEADD(MONTH, 1, dt), max_date FROM RecursiveDates WHERE dt < max_date ), monthly_totals AS (SELECT DATEADD(MONTH, DATEDIFF(MONTH, 0, payment_date), 0) AS dt, SUM(payment_amount) AS total_payments FROM payments_received GROUP BY DATEADD(MONTH, DATEDIFF(MONTH, 0, payment_date), 0)), monthly_totals_with_zero AS (SELECT rd.dt, COALESCE(mt.total_payments, 0) AS total_payments FROM RecursiveDates rd LEFT JOIN monthly_totals mt ON rd.dt = mt.dt) SELECT m.dt AS MONTH, m.total_payments, m.total_payments - LAG(m.total_payments, 1) OVER (ORDER BY m.dt) AS mom_change FROM monthly_totals_with_zero m ORDER BY m.dt OPTION (MAXRECURSION 0);WITH RecursiveDates AS (SELECT DATEADD(MONTH, DATEDIFF(MONTH, 0, MIN(payment_date)), 0) AS dt, DATEADD(MONTH, DATEDIFF(MONTH, 0, MAX(payment_date)), 0) AS max_date FROM payments_received UNION ALL SELECT DATEADD(MONTH, 1, dt), max_date FROM RecursiveDates WHERE dt < max_date ), monthly_totals AS (SELECT DATEADD(MONTH, DATEDIFF(MONTH, 0, payment_date), 0) AS dt, SUM(payment_amount) AS total_payments FROM payments_received GROUP BY DATEADD(MONTH, DATEDIFF(MONTH, 0, payment_date), 0)), monthly_totals_with_zero AS (SELECT rd.dt, COALESCE(mt.total_payments, 0) AS total_payments FROM RecursiveDates rd LEFT JOIN monthly_totals mt ON rd.dt = mt.dt) SELECT FORMAT(m.dt, 'yyyy-MM') AS MONTH, m.total_payments, m.total_payments - LAG(m.total_payments, 1) OVER (ORDER BY m.dt) AS mom_change FROM monthly_totals_with_zero m ORDER BY m.dt OPTION (MAXRECURSION 0);",What is the total payments received per month? Also calculate the MoM change for each month.,"MoM change = (current month value - prev month value). Return all months in your answer, including those where there were no payments.","To ascertain the volume of sales conducted by each salesperson over a recent period, merge the salespersons and sales tables, applying a filter for recent sales transactions.
To determine the average duration from sale date to payment date, perform a join between the sales and payments tables
To calculate the average selling price, join the sales and products tables, group by product name, and compute the ratio of total sales amount to the number of sales
MoM change = (current month value - prev month value). Return all months in your answer, including those where there were no payments."
car_dealership,tsql,instructions_date_join,"WITH date_range AS (SELECT DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()) - number - 1, 0) AS month_start FROM master..spt_values WHERE type = 'P' AND number BETWEEN 0 AND 5), sales_metrics AS (SELECT DATEADD(MONTH, DATEDIFF(MONTH, 0, s.sale_date), 0) AS sale_month, COUNT(s.id) AS PMSPS, SUM(s.sale_price) AS PMSR FROM sales AS s JOIN salespersons AS sp ON s.salesperson_id = sp.id WHERE YEAR(sp.hire_date) BETWEEN 2022 AND 2023 AND s.sale_date >= DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()) - 6, 0) AND s.sale_date < DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0) GROUP BY DATEADD(MONTH, DATEDIFF(MONTH, 0, s.sale_date), 0)) SELECT dr.month_start, COALESCE(sm.PMSPS, 0) AS PMSPS, COALESCE(sm.PMSR, 0) AS PMSR FROM date_range AS dr LEFT JOIN sales_metrics AS sm ON dr.month_start = sm.sale_month ORDER BY dr.month_start ASC;","What are the PMSPS and PMSR in the last 6 months excluding the current month, for salespersons hired between 2022 and 2023 (both inclusive)? Return all months in your answer, including those where metrics are 0. Order by month ascending.",PMSPS = per month salesperson sales count. PMSR = per month sales revenue in dollars. Truncate date to month for aggregation.,"PMSPS = per month salesperson sales count. PMSR = per month sales revenue in dollars. Truncate date to month for aggregation.
ASP = Average Sale Price during a specific timeframe
To calculate the average days between a sale date and when the payment was received, join the relevant tables.
TSC = Total Sales Count for a given period"
car_dealership,tsql,instructions_date_join,"SELECT COUNT(s.id) AS num_sales, SUM(s.sale_price) AS total_revenue FROM sales AS s JOIN cars AS c ON s.car_id = c.id WHERE c.make = 'Toyota' AND s.sale_date BETWEEN DATEADD(DAY, -30, GETDATE()) AND GETDATE();",How many Toyota cars were sold in the last 30 days inclusive of today? Return the number of sales and total revenue.,"Last 30 days = DATEADD(DAY, -30, CAST(GETDATE() AS DATE)) to CAST(GETDATE() AS DATE). Always join sales with cars before using the sales table.","To calculate the average days between sale date and payment received date, join the sales and payments_received tables
To get the list of cars that were sold and their sale price, join the cars and sales tables
Last 30 days = DATEADD(DAY, -30, CAST(GETDATE() AS DATE)) to CAST(GETDATE() AS DATE). Always join sales with cars before using the sales table.
When using car makes, model names, engine_type, and vin_number, match case-insensitively and allow partial matches using LIKE with wildcards."
car_dealership,tsql,instructions_date_join,"SELECT DATEADD(WEEK, DATEDIFF(WEEK, 0, p.payment_date), 0) AS WEEK, COUNT(p.id) AS total_payments, COUNT(CASE WHEN DATEPART(WEEKDAY, p.payment_date) IN (1, 7) THEN 1 END) AS weekend_payments FROM payments_received AS p JOIN sales AS s ON p.sale_id = s.id WHERE s.sale_price > 30000 AND p.payment_date >= DATEADD(WEEK, -8, DATEADD(WEEK, DATEDIFF(WEEK, 0, GETDATE()), 0)) AND p.payment_date < DATEADD(WEEK, DATEDIFF(WEEK, 0, GETDATE()), 0) GROUP BY DATEADD(WEEK, DATEDIFF(WEEK, 0, p.payment_date), 0) ORDER BY WEEK;","For sales with sale price over $30,000, how many payments were received in total and on weekends in each of the last 8 calendar weeks (excluding the current week)? Return the week (as a date), total payments received, and weekend payments received in ascending order.",Weekend days are Saturday (6) and Sunday (0). Truncate date to week for aggregation.,"To calculate the average days between sale date and payment received date, join the sales and payments received tables Weekend days are Saturday (6) and Sunday (0). Truncate date to week for aggregation. When using car makes, model names, engine_type and vin_number, match case-insensitively and allow partial matches using LIKE with wildcards. To get the total sales amount per salesperson, join the salespersons and sales tables, group by salesperson, and sum the sale_price"
car_dealership,tsql,instructions_date_join,"SELECT TOP 1 c.make, c.model, s.sale_price FROM cars AS c JOIN sales AS s ON c.id = s.car_id JOIN inventory_snapshots AS i ON c.id = i.car_id AND DATEADD(day, DATEDIFF(day, 0, s.sale_date), 0) = DATEADD(day, DATEDIFF(day, 0, i.snapshot_date), 0) WHERE i.is_in_inventory = 0 ORDER BY s.sale_price DESC;","What is the make, model and sale price of the car with the highest sale price that was sold on the same day it went out of inventory?","If inventory snapshots and sales from the same day are to be joined, join on the truncated date fields eg FROM inventory_snapshots i JOIN sales s ON CONVERT(DATETIME, CONVERT(DATE, i.snapshot_date)) = CONVERT(DATETIME, CONVERT(DATE, s.sale_date)).","ASP (average selling price) = total sales amount / number of sales
To calculate the gross profit margin, join the appropriate tables and calculate the margin
If inventory snapshots and sales from the same day are to be joined, join on the truncated date fields eg FROM inventory_snapshots i JOIN sales s ON CONVERT(DATETIME, CONVERT(DATE, i.snapshot_date)) = CONVERT(DATETIME, CONVERT(DATE, s.sale_date)).
To rank salespersons by their sales volume, join the salespersons and sales data, order by sales volume.

So, the rephrased instructions would be: ""GPM (gross profit margin) = (total revenue - total cost) / total cost * 100
To determine the average sale price, join the sales data and calculate the average
SR = sales rank of each salesperson ordered by their total sales amount descending."""
car_dealership,tsql,instructions_string_matching,"SELECT c.make, c.model, c.year, c.color, c.vin_number, AVG(s.sale_price) AS avg_sale_price FROM cars AS c JOIN sales AS s ON c.id = s.car_id WHERE LOWER(c.make) LIKE '%ford%' OR LOWER(c.model) LIKE '%mustang%' GROUP BY c.make, c.model, c.year, c.color, c.vin_number;","What are the details and average sale price for cars that have 'Ford' in their make name or 'Mustang' in the model name? Return make, model, year, color, vin_number and avg_sale_price.","When using car makes, model names, engine_type and vin_number, match case-insensitively and allow partial matches using LIKE with wildcards.","To calculate the gross profit margin, use the formula where GPM is the quotient of total revenue minus total cost over total cost, multiplied by 100 When using car makes, model names, engine_type and vin_number, match case-insensitively and allow partial matches using LIKE with wildcards. To compute the average sales price, divide the total sales amount by the number of sales To determine the sales rank of each salesperson, order by their total sales amount in descending order."
car_dealership,tsql,instructions_string_matching,"SELECT c.make, c.model, COUNT(s.id) AS num_sales FROM cars AS c LEFT JOIN sales AS s ON c.id = s.car_id WHERE LOWER(c.vin_number) LIKE '%m5%' GROUP BY c.make, c.model;","How many sales were made for each car model that has 'M5' in its VIN number? Return the make, model and number of sales.","When using car makes, model names, engine_type and vin_number, match case-insensitively and allow partial matches using LIKE with wildcards.","To determine the total sales amount for each salesperson, combine data from the salespersons and sales tables, grouping by salesperson and summing the sale_price
When using car makes, model names, engine_type and vin_number, match case-insensitively and allow partial matches using LIKE with wildcards.
To calculate the average selling price, join the sales and cars tables, and divide the total sales amount by the number of sales
For understanding the number of sales achieved by each salesperson within a specified period, merge the salespersons and sales tables and apply a filter based on the given time frame."
car_dealership,tsql,keywords_aggregate,"SELECT COUNT(id) AS TSC FROM sales WHERE sale_date >= DATEADD(DAY, -7, GETDATE());","What is the TSC in the past 7 days, inclusive of today?",TSC = Total Sales Count.,"To determine the average selling price, divide the total sales amount by the number of sales
To calculate the gross profit margin, subtract the total cost from the total revenue, then divide by the total cost and multiply by 100
To ascertain the sales rank of each salesperson, order them by their total sales amount in descending order.
TSC = Total Sales Count."
car_dealership,tsql,keywords_aggregate,SELECT AVG(sale_price) AS ASP FROM sales WHERE sale_date >= '2023-01-01' AND sale_date <= '2023-03-31';,What is the ASP for sales made in the first quarter of 2023?,ASP = Average Sale Price in the first quarter of 2023.,"Always join sales with cars before using the sales table. ASP = Average Sale Price in the first quarter of 2023. To calculate the average days between sale date and payment received date, join the sales and payments tables To get the list of cars that were sold along with their sale prices, join the cars and sales tables Last 30 days = DATEADD(DAY, -30, CAST(GETDATE() AS DATE)) to CAST(GETDATE() AS DATE)"
car_dealership,tsql,keywords_ratio,"SELECT TOP 3 salespersons.first_name, salespersons.last_name, AVG(sales.sale_price) AS ASP FROM sales JOIN salespersons ON sales.salesperson_id = salespersons.id GROUP BY salespersons.first_name, salespersons.last_name ORDER BY ASP DESC;","Who are the top 3 salespersons by ASP? Return their first name, last name and ASP.",ASP (average selling price) = total sales amount / number of sales,"To get the total sales amount per salesperson, join the salespersons and sales tables, group by salesperson, and sum the sale_amount. To calculate the average days between the sale date and payment received date, join the sales and payments_received tables. ASP (average selling price) = total sales amount / number of sales. To get the list of cars that were sold and their sale price, join the cars and sales tables."
car_dealership,tsql,keywords_ratio,"SELECT (SUM(sale_price) - SUM(cars.cost)) / SUM(cars.cost) * 100 AS gpm FROM sales JOIN cars ON sales.car_id = cars.id WHERE DATEPART(YEAR, sale_date) = 2023;",What is the GPM for all car sales in 2023?,GPM (gross profit margin) = (total revenue - total cost) / total cost * 100,"Analyze salesperson activity over a selected period by connecting salespersons with sales records
GPM (gross profit margin) = (total revenue - total cost) / total cost * 100
For weekly trends, consider the start of the week
To calculate the TSC = Total Sales Count within a given period
Identify sold vehicles and pricing by linking car and sales data
MoM = assess the month-over-month change in total financial transactions
SR = sales rank of each salesperson based on their total sales without specifying the order
Utilize LIKE for flexible matching in vehicle information queries.
Use inventory and sales data for concurrent analysis, ensuring date alignment
To summarize sales performance, combine sales and salesperson data, focusing on total sales
PMSPS and PMSR aim to measure salesperson engagement and monthly revenue, respectively
For insights on sale to payment duration, correlate sales with payment dates
To find the ASP = Average Sale Price for a specific time frame
For timing analysis, apply specific date intervals"
derm_treatment,tsql,instructions_cte_join,"WITH doctor_treatment AS (SELECT d.doc_id AS doc_id, d.loc_state AS loc_state FROM doctors AS d JOIN treatments AS t ON d.doc_id = t.doc_id JOIN drugs AS dr ON t.drug_id = dr.drug_id WHERE dr.drug_type = 'biologic') SELECT DISTINCT loc_state FROM doctor_treatment;",Which states do doctors who have prescribed biologic drugs reside in? Return the distinct states.,"To identify doctors who have prescribed a certain drug type and their respective states, first join doctors with treatments on doc_id, then filter by the drug type.","AWF = average weight for male patients TPC = total count of female patients Each of these metrics, while providing valuable insights for other analyses, would not alter the scope or outcome of the provided SQL query aimed at identifying states with doctors who have prescribed biologic drugs. PMPD = counts of patient diagnoses per month To identify doctors who have prescribed a certain drug type and their respective states, first join doctors with treatments on doc_id, then filter by the drug type."
derm_treatment,tsql,instructions_cte_join,"WITH patient_treatment AS (SELECT p.patient_id AS patient_id, p.weight_kg AS weight_kg FROM patients AS p JOIN treatments AS t ON p.patient_id = t.patient_id WHERE t.drug_id = (SELECT drug_id FROM drugs WHERE drug_name = 'Drugalin')) SELECT AVG(weight_kg) FROM patient_treatment;",What is the average weight in kg of patients treated with the drug named 'Drugalin'? Return the average weight.,"To find the average weight of patients treated with a specific drug, first join patients with treatments on patient_id, then filter by the drug name.","To find the average weight of patients treated with a specific drug, first join patients with treatments on patient_id, then filter by the drug name.
PMPD = Determine the number of diagnoses made for patients each month.
DDD = Compute the overall consumed medication amount divided by the total number of treatment days
AWF = Calculate the average weight in kilograms for patients identified as male"
derm_treatment,tsql,instructions_cte_join,"SELECT a.description, a.treatment_id, d.drug_id, d.drug_name FROM adverse_events AS a JOIN treatments AS t ON a.treatment_id = t.treatment_id JOIN drugs AS d ON t.drug_id = d.drug_id WHERE d.drug_type = 'topical';","I want the adverse events that have been reported for treatments involving topical drugs. Give me the description, treatment id, drug id and name.","To get adverse events reported for treatments involving certain drugs, first join treatments with adverse_events on treatment_id, then join with drugs on drug_id to filter on the specific drug(s).","AWF = Average weight of female patients (kg)
PASI improvement rate is calculated as ((average PASI score on day 100 - average PASI score on day 7) / average PASI score on day 7) * 100, including only patients with non-null PASI scores at both timepoints.
To calculate the total number of adverse events reported for treatments involving topical drugs, first join treatments with adverse_events on treatment_id, then filter by the drug type.
TPC = total patient count"
derm_treatment,tsql,instructions_cte_join,WITH patient_diagnosis_treatment AS (SELECT p.patient_id AS patient_id FROM patients AS p JOIN treatments AS t ON p.patient_id = t.patient_id JOIN diagnoses AS d ON t.diag_id = d.diag_id JOIN drugs AS dr ON t.drug_id = dr.drug_id WHERE d.diag_name = 'Psoriasis vulgaris' AND dr.drug_type = 'biologic') SELECT COUNT(DISTINCT patient_id) FROM patient_diagnosis_treatment;,How many patients have been diagnosed with 'Psoriasis vulgaris' and treated with a biologic drug? Return the distinct count of patients.,"To find the number of patients who have been diagnosed with a specific type of psoriasis and treated with a biologic drug, first join patients with treatments on patient_id, then join with diagnoses on diag_id, filtering by diagnosis and drug type.","TPC: Determine the total count of female patients To find the number of patients who have been diagnosed with a specific type of psoriasis and treated with a biologic drug, first join patients with treatments on patient_id, then join with diagnoses on diag_id, filtering by diagnosis and drug type. D7D100PIR: Compute the improvement rate of the PASI score from day 7 to day 100, considering only patients with available PASI scores for both days. AWF: Calculate the mean weight for male patients in kilograms"
derm_treatment,tsql,instructions_cte_window,"WITH FirstTreatment AS (SELECT p.patient_id AS patient_id, MIN(t.start_dt) AS first_treatment_date FROM patients AS p JOIN treatments AS t ON p.patient_id = t.patient_id GROUP BY p.patient_id), NewPatientsPerYear AS (SELECT DATEPART(YEAR, first_treatment_date) AS YEAR, COUNT(patient_id) AS new_patients FROM FirstTreatment GROUP BY DATEPART(YEAR, first_treatment_date)), NPI AS (SELECT YEAR AS YEAR, new_patients AS new_patients, new_patients - LAG(new_patients, 1) OVER (ORDER BY CASE WHEN YEAR IS NULL THEN 1 ELSE 0 END, YEAR) AS npi FROM NewPatientsPerYear) SELECT YEAR, new_patients, npi FROM NPI ORDER BY CASE WHEN YEAR IS NULL THEN 1 ELSE 0 END, YEAR;","What is the NPI for each year? Return the year, number of new patients, and NPI",NPI (new patients increase) = the increase in number of new patients compared to the previous year. New patients are defined as patients starting their first treatment and require joining the patients table with the earliest record of each patient from the treatment table on patient_id,"NPI (new patients increase) = the increase in number of new patients compared to the previous year. New patients are defined as patients starting their first treatment and require joining the patients table with the earliest record of each patient from the treatment table on patient_id
If events from two different sources need to be linked based on time, it's advised to align them by truncating both dates to the same interval for the join condition.
To find the average weight of patients treated with any drug, join the patients and treatments tables using patient_id, then filter as needed by the drug
To identify doctors who have prescribed any drug type along with their states, join the doctors with treatments on doc_id, then apply the necessary filter for the drug type"
derm_treatment,tsql,instructions_cte_window,"WITH doc_drug_counts AS (SELECT d.doc_id AS doc_id, d.specialty AS specialty, COUNT(DISTINCT t.drug_id) AS num_drugs_prescribed FROM doctors AS d JOIN treatments AS t ON d.doc_id = t.doc_id GROUP BY d.doc_id, d.specialty) SELECT doc_id, specialty, num_drugs_prescribed, DENSE_RANK() OVER (PARTITION BY specialty ORDER BY num_drugs_prescribed DESC) AS specialty_drug_rank FROM doc_drug_counts;","Return each doctor's doc_id, specialty, number of distinct drugs prescribed, and SDR",SDR = a doctor's rank within their specialty by number of distinct drugs prescribed. Doctors prescribing more drugs will have a higher rank,"To calculate the average weight of all patients who have been given a certain medication, join the patients and treatments tables on the patient's ID, then apply a filter for the specific medication To discover physicians who have prescribed a particular type of medication and identify their locations, link the doctors and treatments tables via the doctor's ID, then use a condition to filter by the medication type To ascertain the total count of adverse reactions recorded for treatments that involve topical medications, merge the treatments and adverse_events tables using the treatment ID, and apply a filter for the medication type. SDR = a doctor's rank within their specialty by number of distinct drugs prescribed, with doctors prescribing the most distinct drugs having rank 1"
derm_treatment,tsql,instructions_date_join,"SELECT COUNT(t.treatment_id) FROM patients AS p JOIN treatments AS t ON p.patient_id = t.patient_id WHERE p.first_name = 'Alice' AND t.start_dt BETWEEN DATEADD(MONTH, -6, DATEADD(DAY, 1, EOMONTH(GETDATE(), -1))) AND EOMONTH(GETDATE(), -1);","How many treatments did the patient Alice have in the last 6 months, not including the current month?","Last 6 months = DATEADD(MONTH, -6, DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0)) to DATEADD(DAY, -1, DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0)). Always join treatments with patients before using the treatments table. If not mentioned, the patient name provided is the first name.","Last 6 months = DATEADD(MONTH, -6, DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0)) to DATEADD(DAY, -1, DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0)). Always join treatments with patients before using the treatments table. If not mentioned, the patient name provided is the first name. To identify doctors with certain name characteristics; To calculate the total adverse event counts for specific drug treatments; To examine patient diagnosis and treatment correlations with certain drugs."
derm_treatment,tsql,instructions_date_join,"SELECT DATEADD(MONTH, DATEDIFF(MONTH, 0, t.start_dt), 0) AS MONTH, COUNT(DISTINCT t.patient_id) AS patient_count, COUNT(DISTINCT t.treatment_id) AS treatment_count FROM treatments AS t JOIN diagnoses AS d ON t.diag_id = d.diag_id WHERE t.start_dt >= DATEADD(MONTH, -12, DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0)) AND t.start_dt < DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0) GROUP BY DATEADD(MONTH, DATEDIFF(MONTH, 0, t.start_dt), 0);","What are the PMPD and PMTC for each of the last 12 months, not including the current month",PMPD = per month patient diagnoses. PMTC = per month treatment count. Truncate start_dt to month for aggregation.,PMPD = per month patient diagnoses. PMTC = per month treatment count. Truncate start_dt to month for aggregation. TPC = count of female patients; AWF = average weight for male patients; D7D100PIR = (average PASI score at day 100 - average PASI score at day 7) / average PASI score at day 7 * 100 with non-null PASI scores at both timepoints; DDD = total consumed drug amount divided by total treatment days.
derm_treatment,tsql,instructions_date_join,"SELECT FORMAT(DATEADD(MONTH, DATEDIFF(MONTH, 0, t.start_dt), 0), 'yyyy-MM') AS [Month], COUNT(DISTINCT t.patient_id) AS patient_count, COUNT(DISTINCT CASE WHEN d.drug_type = 'biologic' THEN t.patient_id ELSE NULL END) AS biologic_count FROM treatments AS t JOIN drugs AS d ON t.drug_id = d.drug_id WHERE t.start_dt >= DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()) - 3, 0) AND t.start_dt < DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0) GROUP BY FORMAT(DATEADD(MONTH, DATEDIFF(MONTH, 0, t.start_dt), 0), 'yyyy-MM');SELECT FORMAT(DATEADD(MONTH, DATEDIFF(MONTH, 0, t.start_dt), 0), 'yyyy-MM') + '-01' AS [Month], COUNT(DISTINCT t.patient_id) AS patient_count, COUNT(DISTINCT CASE WHEN d.drug_type = 'biologic' THEN t.patient_id ELSE NULL END) AS biologic_count FROM treatments AS t JOIN drugs AS d ON t.drug_id = d.drug_id WHERE t.start_dt >= DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()) - 3, 0) AND t.start_dt < DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0) GROUP BY FORMAT(DATEADD(MONTH, DATEDIFF(MONTH, 0, t.start_dt), 0), 'yyyy-MM') + '-01';SELECT DATEADD(MONTH, DATEDIFF(MONTH, 0, t.start_dt), 0) AS [Month], COUNT(DISTINCT t.patient_id) AS patient_count, COUNT(DISTINCT CASE WHEN d.drug_type = 'biologic' THEN t.patient_id ELSE NULL END) AS biologic_count FROM treatments AS t JOIN drugs AS d ON t.drug_id = d.drug_id WHERE t.start_dt >= DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()) - 3, 0) AND t.start_dt < DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0) GROUP BY DATEADD(MONTH, DATEDIFF(MONTH, 0, t.start_dt), 0);","How many distinct patients had treatments in each of the last 3 months, not including the current month? Out of these, how many had treatments with biologic drugs? Return the month, patient count, and biologic treatment count.",Biologic drugs have drug_type = 'biologic'. Truncate start_dt to month for aggregation.,"To calculate the DDD (Defined Daily Dose) = the total amount of drug consumed divided by the total number of days of treatment
To count the number of adverse events reported for treatments involving any specified drug type, first join the treatments table with the adverse_events table based on treatment_id, then apply a filter by the specified drug type.
To determine the SDR (Specialty Doctor Rank) = rank doctors within their specialty by the number of distinct drugs prescribed, with those prescribing more distinct drugs achieving a higher rank
Biologic drugs have drug_type = 'biologic'. Truncate start_dt to month for aggregation."
derm_treatment,tsql,instructions_date_join,"WITH adverse_events_per_drug AS (SELECT d.drug_id AS drug_id, COUNT(ae.id) AS num_events FROM adverse_events AS ae JOIN treatments AS t ON ae.treatment_id = t.treatment_id AND DATEPART(YEAR, ae.reported_dt) = DATEPART(YEAR, t.start_dt) AND DATEPART(MONTH, ae.reported_dt) = DATEPART(MONTH, t.start_dt) JOIN drugs AS d ON t.drug_id = d.drug_id GROUP BY d.drug_id) SELECT TOP 1 ae.drug_id, d.drug_name, ae.num_events FROM adverse_events_per_drug AS ae JOIN drugs AS d ON ae.drug_id = d.drug_id ORDER BY ae.num_events DESC;",Which drug had the highest number of adverse events reported within the same month as the treatment start date (adverse event or treatment can be earlier than the other)? Return the number of adverse events along with the drug's id and name.,"If events from 2 different tables from the same interval are to be joined, join on the respective truncated date fields eg `FROM t1 JOIN t2 ON DATEADD('<interval>', DATEDIFF('<interval>', 0, t1.date), 0) = DATEADD('<interval>', DATEDIFF('<interval>', 0, t2.date), 0).","If events from 2 different tables from the same interval are to be joined, join on the respective truncated date fields eg `FROM t1 JOIN t2 ON DATEADD('<interval>', DATEDIFF('<interval>', 0, t1.date), 0) = DATEADD('<interval>', DATEDIFF('<interval>', 0, t2.date), 0). To determine the total count of adverse reactions reported for treatments that include topical medications, associate treatments with adverse_events through the treatment identifier, applying a filter based on the type of medication. To identify the healthcare providers who have prescribed a specific category of medication along with their state, connect doctors to treatments by doctor identifier, filtering by the type of medication To calculate the average weight of patients who have received a certain medication, link patients to treatments via the patient identifier, then narrow down by the name of the medication"
derm_treatment,tsql,instructions_string_matching,SELECT COUNT(*) FROM patients WHERE email LIKE '%@gmail.com' OR email LIKE '%@yahoo.com';,How many patients have a Gmail or Yahoo email address?,Patients with gmail or yahoo email addresses,"To calculate the total number of adverse events for treatments involving topical medications, join the treatments and adverse_events tables on treatment_id, and then filter based on the drug's form.
Patients with gmail or yahoo email addresses
To find the average weight of patients treated with a specific drug, join the patients and treatments tables by patient_id, then select the drug of interest
To identify doctors who have prescribed a certain type of drug and their practice states, join the doctors and treatments tables on doc_id, filtering by the drug's category"
derm_treatment,tsql,instructions_string_matching,"SELECT first_name, last_name, specialty FROM doctors WHERE LOWER(first_name) LIKE 'j%' OR LOWER(last_name) LIKE '%son%';","Return the first name, last name and specialty of doctors whose first name starts with 'J' or last name contains 'son', case-insensitive.","Doctors whose first name starts with 'J' or last name contains 'son', case-insensitive","Doctors whose first name starts with 'J' or last name contains 'son', case-insensitive To calculate the total number of adverse events reported for treatments involving topical drugs, first join treatments with adverse_events on treatment_id, then filter by the drug type. To identify doctors who have prescribed a certain drug type and their respective states, first join doctors with treatments on doc_id, then filter by the drug type To find the average weight of patients treated with a specific drug, first join patients with treatments on patient_id, then filter by the drug name"
derm_treatment,tsql,keywords_aggregate,SELECT COUNT(patient_id) AS pic FROM patients WHERE gender = 'Female' AND ins_type = 'private';,What is the PIC for female patients?,PIC = private insured patient count,"PIC = private insured patient count. To calculate the D7D100PIR, subtract the average PASI score at day 100 from the average at day 7, divide by the average at day 7, and multiply by 100. PI calculates the month-over-month growth in new patients. For DDD, divide the total drug quantity used by the total treatment days"
derm_treatment,tsql,keywords_aggregate,SELECT AVG(weight_kg) AS caw FROM patients WHERE gender = 'Male';,What is the CAW for male patients,CAW = cohort average weight in kilograms,"A doctor's Specialty Distinct Drug Rank (SDR) is established by the variety of distinct drugs they've prescribed, with the highest prescribers ranked first.
CAW = cohort average weight in kilograms
To calculate the D7D100PIR, subtract the average PASI score at the beginning of the period from the average at the end, divide by the initial average, and multiply by 100
The Defined Daily Dose (DDD) is calculated as the total consumed medication divided by the treatment duration"
derm_treatment,tsql,keywords_ratio,"SELECT d.drug_name, AVG(t.tot_drug_amt / NULLIF(DATEDIFF(day, t.start_dt, t.end_dt), 0)) AS ddd FROM treatments AS t JOIN drugs AS d ON t.drug_id = d.drug_id WHERE t.end_dt IS NOT NULL GROUP BY d.drug_name;",Calculate the average DDD for each drug. Return the drug name and average DDD value.,"DDD (defined daily dose) = total drug amount consumed during one treatment / total days of treatment (end - start date in days), where end date is not null","DDD (defined daily dose) = total drug amount consumed during one treatment / total days of treatment (end - start date in days). To find the average weight of patients treated with a specific drug, first join patients with treatments on patient_id, then filter by the drug name. To identify doctors who have prescribed a certain drug type and their respective locations, first join doctors with treatments on doc_id, then filter by the drug type. To calculate the total number of adverse events reported for treatments involving certain drug types, first join treatments with adverse_events on treatment_id, then filter by the drug type."
derm_treatment,tsql,keywords_ratio,SELECT (AVG(day100_pasi_score) - AVG(day7_pasi_score)) / AVG(day7_pasi_score) * 100 AS d7d100pir FROM outcomes WHERE NOT day7_pasi_score IS NULL AND NOT day100_pasi_score IS NULL;,What is the overall D7D100PIR across all treatments? Return the percentage value.,D7D100PIR (day 7 to day 100 PASI improvement rate) = (avg PASI score on day 100 - avg PASI score on day 7) / avg PASI score on day 7 * 100. This should only include patients who have non-null PASI scores for both timepoints.,"To discover the average weight of patients who have been prescribed a specific medication, begin by associating patients with treatments on patient_id, and then apply a filter by the drug name.
D7D100PIR (day 7 to day 100 PASI improvement rate) = (avg PASI score on day 100 - avg PASI score on day 7) / avg PASI score on day 7 * 100. This should only include patients who have non-null PASI scores for both timepoints.
To identify doctors who have prescribed a certain type of drug and their state of practice, initially join doctors with treatments on doc_id, followed by filtering based on the drug type
To calculate the total number of adverse events reported for treatments involving topical drugs, first link treatments with adverse_events on treatment_id, then use a filter for the drug type"
ewallet,tsql,instructions_cte_join,"WITH user_transactions AS (SELECT u.uid AS uid, t.txid AS txid FROM consumer_div.users AS u JOIN consumer_div.wallet_transactions_daily AS t ON u.uid = t.sender_id WHERE t.sender_type = 0) SELECT UID, COUNT(txid) AS total_transactions FROM user_transactions GROUP BY UID;",What is the total number of wallet transactions sent by each user that is not a merchant? Return the user ID and total transaction count.,"To get the total number of transactions per user, join the users and wallet_transactions_daily tables in a CTE, then aggregate by user_id and count the number of transactions","PMDAU = Aggregate daily active users by truncating the date to the month for aggregation.
AMB = Average balance of user wallets
LUB = Most recent balance for each user
To get the total number of transactions per user, join the users and wallet_transactions_daily tables in a CTE, then aggregate by user_id and count the number of transactions"
ewallet,tsql,instructions_cte_join,"WITH merchant_coupon_usage AS (SELECT c.cid AS cid, t.amount AS amount FROM consumer_div.coupons AS c JOIN consumer_div.wallet_transactions_daily AS t ON c.cid = t.coupon_id WHERE c.merchant_id = 1) SELECT cid, SUM(amount) AS total_discount FROM merchant_coupon_usage GROUP BY cid;",What is the total transaction amount for each coupon offered by merchant with ID 1? Return the coupon ID and total amount transacted with it.,"To get coupon usage, join the coupons and wallet_transactions_daily on coupon_id ","AMB = Average balance of user wallets updated recently
To get coupon usage, join the coupons and wallet_transactions_daily on coupon_id 
STR (success transaction rate) = Number of successful transactions divided by total transactions
PMDAU = Aggregation based on truncating creation dates to months for active user metrics."
ewallet,tsql,instructions_cte_join,"WITH user_session_duration AS (SELECT u.uid AS uid, s.session_start_ts AS session_start_ts, s.session_end_ts AS session_end_ts FROM consumer_div.users AS u JOIN consumer_div.user_sessions AS s ON u.uid = s.user_id WHERE s.session_start_ts >= '2023-06-01' AND s.session_end_ts < '2023-06-08') SELECT uid, SUM(DATEDIFF(SECOND, session_start_ts, session_end_ts)) AS total_duration FROM user_session_duration GROUP BY uid ORDER BY total_duration DESC;",What is the total session duration in seconds for each user between 2023-06-01 inclusive and 2023-06-08 exclusive? Return the user ID and their total duration as an integer sorted by total duration with the longest duration first,"To analyze user engagement, join the users and user_sessions tables in a CTE, then aggregate to calculate total session duration per user for a given date range. Remember to convert your answer into seconds, using EPOCH function","LUB = Most recent account balance for each user. CPUR (coupon usage rate) = Ratio of distinct coupons used to the number of distinct transactions AMB = Average balance of user wallets over a specified period To analyze user engagement, join the users and user_sessions tables in a CTE, then aggregate to calculate total session duration per user for a given date range"
ewallet,tsql,instructions_cte_join,"WITH user_latest_setting AS (SELECT u.uid AS uid, s.marketing_opt_in AS marketing_opt_in, s.created_at AS created_at, ROW_NUMBER() OVER (PARTITION BY u.uid ORDER BY CASE WHEN s.created_at IS NULL THEN 1 ELSE 0 END DESC, s.created_at DESC) AS rn FROM consumer_div.users AS u JOIN consumer_div.user_setting_snapshot AS s ON u.uid = s.user_id) SELECT UID, marketing_opt_in FROM user_latest_setting WHERE rn = 1;",What is the marketing opt-in preference for each user? Return the user ID and boolean opt-in value,"To get any user's settings, only select the latest snapshot of user_setting_snapshot for each user","CPUR (coupon usage rate) = Ratio of distinct coupons used to number of distinct transactions.
AMB = Average balance of user wallets over a recent period
STR (success transaction rate) = Ratio of successful transactions to total transactions
To get any user's settings, only select the latest snapshot of user_setting_snapshot for each user"
ewallet,tsql,instructions_cte_window,"WITH user_balances AS (SELECT user_id AS user_id, balance AS balance, ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY CASE WHEN updated_at IS NULL THEN 1 ELSE 0 END DESC, updated_at DESC) AS rn FROM consumer_div.wallet_user_balance_daily) SELECT user_id, balance FROM user_balances WHERE rn = 1;",What is the LUB for each user.,"LUB = Latest User Balance, which is the most recent balance for each user","To get the total number of transactions per user, join the users and wallet_transactions_daily tables in a common table expression (CTE), then group by user_id to count transactions
LUB = Latest User Balance, which is the most recent balance for each user
To determine user notification preferences, use a join between the users and user_setting_snapshot tables in a CTE, focusing on selecting the most recent snapshot for each user.
For analyzing coupon usage, start with a join between the coupons and wallet_transactions_daily tables in a CTE, apply filtering as needed, and then perform aggregation for the total discount amount"
ewallet,tsql,instructions_cte_window,"WITH merchant_revenue AS (SELECT m.mid AS mid, m.category AS merchant_category, SUM(w.amount) AS total_revenue FROM consumer_div.merchants AS m INNER JOIN consumer_div.wallet_transactions_daily AS w ON m.mid = w.receiver_id AND w.receiver_type = 1 WHERE w.status = 'success' GROUP BY m.mid, m.category) SELECT *, RANK() OVER (ORDER BY CASE WHEN total_revenue IS NULL THEN 1 ELSE 0 END DESC, total_revenue DESC) AS mrr FROM merchant_revenue;WITH merchant_revenue AS (SELECT m.name AS name, m.category AS merchant_category, SUM(w.amount) AS total_revenue FROM consumer_div.merchants AS m INNER JOIN consumer_div.wallet_transactions_daily AS w ON m.mid = w.receiver_id AND w.receiver_type = 1 WHERE w.status = 'success' GROUP BY m.name, m.category) SELECT *, RANK() OVER (ORDER BY CASE WHEN total_revenue IS NULL THEN 1 ELSE 0 END DESC, total_revenue DESC) AS mrr FROM merchant_revenue;WITH merchant_revenue AS (SELECT m.mid AS mid, m.name AS name, m.category AS merchant_category, SUM(w.amount) AS total_revenue FROM consumer_div.merchants AS m INNER JOIN consumer_div.wallet_transactions_daily AS w ON m.mid = w.receiver_id AND w.receiver_type = 1 WHERE w.status = 'success' GROUP BY m.mid, m.name, m.category) SELECT *, RANK() OVER (ORDER BY CASE WHEN total_revenue IS NULL THEN 1 ELSE 0 END DESC, total_revenue DESC) AS mrr FROM merchant_revenue;","What is the MRR for each merchant? Return the merchant name, category, revenue amount, and revenue rank.","MRR = Merchant Revenue Rank, which ranks merchants based on amounts from successfully received transactions only. Filter receiver_type=1 in consumer_div.wallet_transactions_daily for merchants. Merchant with rank 1 has the highest revenue.","To get user notification preferences, join the users and user_setting_snapshot tables in a CTE, then select the latest snapshot for each user
Merchant category should be matched case-insensitively with wildcards, e.g., using LOWER(merchants.category) LIKE '%...%'.
MRR = Merchant Revenue Rank, which ranks merchants based on their total successful received transaction amounts. Filter receiver_type=1 in consumer_div.wallet_transactions_daily for merchants. Merchant with rank 1 has the highest revenue.
To analyze user engagement, join the users and user_sessions tables in a CTE, then aggregate to calculate total session duration per user"
ewallet,tsql,instructions_date_join,"SELECT DATEADD(WEEK, DATEDIFF(WEEK, 0, n.created_at), 0) AS WEEK, COUNT(*) AS total_notifications, COUNT(CASE WHEN DATEPART(WEEKDAY, n.created_at) IN (1, 7) THEN 1 END) AS weekend_notifications FROM consumer_div.notifications AS n JOIN consumer_div.users AS u ON n.user_id = u.uid WHERE u.country IN ('US', 'CA') AND n.created_at >= DATEADD(WEEK, -3, DATEADD(WEEK, DATEDIFF(WEEK, 0, GETDATE()), 0)) AND n.created_at < DATEADD(WEEK, DATEDIFF(WEEK, 0, GETDATE()), 0) GROUP BY DATEADD(WEEK, DATEDIFF(WEEK, 0, n.created_at), 0);","For users in the US and Canada, how many total notifications were sent in each of the last 3 weeks excluding the current week? How many of those were sent on weekends?",Weekends are Saturdays and Sundays. Truncate created_at to week for aggregation.,"To gauge user engagement, link users to their sessions without defining a specific date range for total session duration analysis
To analyze coupon usage, join the coupons and transactions tables to aggregate total discount amounts without specifying a merchant
Merchant categories should be matched using a case-insensitive pattern without necessitating wildcards.
Weekends are Saturdays and Sundays. Truncate created_at to week for aggregation."
ewallet,tsql,instructions_date_join,"SELECT COUNT(*) AS num_transactions, SUM(amount) AS total_amount FROM consumer_div.wallet_transactions_daily AS t JOIN consumer_div.users AS u ON t.sender_id = u.uid WHERE u.country = 'US' AND t.created_at >= DATEADD(DAY, -7, GETDATE()) AND t.created_at < DATEADD(DAY, 1, GETDATE());",How many wallet transactions were made by users from the US in the last 7 days inclusive of today? Return the number of transactions and total transaction amount.,"Last 7 days = DATEADD(DAY, -7, CAST(GETDATE() AS DATE)) to CAST(GETDATE() AS DATE). Always join wallet_transactions_daily with users before using the wallet_transactions_daily table.","To analyze coupon usage, join the coupons and wallet_transactions tables in a CTE, filter for a specific merchant, then aggregate to get the total discount amount
Last 7 days = DATEADD(DAY, -7, CAST(GETDATE() AS DATE)) to CAST(GETDATE() AS DATE). Always join wallet_transactions_daily with users before using the wallet_transactions_daily table.
To get user notification preferences, join the users and user_setting_snapshot tables in a CTE, then select the latest snapshot for each user.
To get the total number of transactions per user, join the users and wallet_transactions tables in a CTE, then aggregate by user_uid and count the number of transactions"
ewallet,tsql,instructions_date_join,"WITH coupons_per_merchant AS (SELECT m.mid AS mid, COUNT(c.cid) AS num_coupons FROM consumer_div.coupons AS c JOIN consumer_div.merchants AS m ON m.mid = c.merchant_id AND DATEPART(YEAR, c.created_at) = DATEPART(YEAR, m.created_at) AND DATEPART(MONTH, c.created_at) = DATEPART(MONTH, m.created_at) GROUP BY m.mid) SELECT TOP 1 coupons_per_merchant.mid, m.name, coupons_per_merchant.num_coupons FROM coupons_per_merchant JOIN consumer_div.merchants AS m ON coupons_per_merchant.mid = m.mid ORDER BY coupons_per_merchant.num_coupons DESC;",Which merchant created the highest number of coupons within the same month that the merchant was created (coupon or merchant can be created earlier than the other)? Return the number of coupons along with the merchant's id and name.,"If events from 2 different tables from the same interval are to be joined, join on the respective truncated date fields eg `FROM t1 JOIN t2 ON DATEADD('<interval>', DATEDIFF('<interval>', 0, t1.date), 0) = DATEADD('<interval>', DATEDIFF('<interval>', 0, t2.date), 0).","To analyze coupon usage, join the coupons and wallet_transactions_daily tables in a CTE, filtering for specific merchants, then aggregate If events from 2 different tables from the same interval are to be joined, join on the respective truncated date fields eg `FROM t1 JOIN t2 ON DATEADD('<interval>', DATEDIFF('<interval>', 0, t1.date), 0) = DATEADD('<interval>', DATEDIFF('<interval>', 0, t2.date), 0). To get the total number of transactions per user, join the users and wallet_transactions_daily tables in a CTE, then aggregate by user_id To get user notification preferences, join the users and user_setting_snapshot tables in a CTE, selecting the most recent snapshot for each user."
ewallet,tsql,instructions_date_join,"SELECT DATEADD(MONTH, DATEDIFF(MONTH, 0, t.created_at), 0) AS MONTH, COUNT(DISTINCT t.sender_id) AS active_users FROM consumer_div.wallet_transactions_daily AS t WHERE t.sender_type = 0 AND t.created_at >= DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()) - 2, 0) AND t.created_at < DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0) GROUP BY DATEADD(MONTH, DATEDIFF(MONTH, 0, t.created_at), 0);",What is the PMDAU (Per Month Daily Active Users) for wallet transactions in the last 2 months excluding the current month?,PMDAU (Per Month Daily Active Users) = COUNT(DISTINCT(sender_id) ... WHERE t.sender_type = 0. Truncate created_at to month for aggregation.,"PMDAU (Per Month Daily Active Users) = COUNT(DISTINCT(sender_id) ... WHERE t.sender_type = 0. Truncate created_at to month for aggregation. To analyze coupon usage, join the coupons and wallet_transactions_daily tables in a CTE, filter for transactions associated with a specific merchant, then aggregate to calculate the total discount amount received To get the total number of transactions per user, join the users and wallet_transactions_daily tables in a common table expression (CTE), then aggregate by user_id and count the transactions To get user notification preferences, join the users and user_setting_snapshot tables in a CTE, then select the most recent snapshot for each user."
ewallet,tsql,instructions_string_matching,"SELECT u.username, COUNT(n.id) AS total_notifications FROM consumer_div.users AS u JOIN consumer_div.notifications AS n ON u.uid = n.user_id WHERE n.type = 'promotion' AND n.status = 'unread' AND LOWER(u.country) = 'us' GROUP BY u.username;",Which users from the US have unread promotional notifications? Return the username and the total number of unread promotional notifications.,"User country should be matched case-insensitively, e.g., LOWER(users.country) = 'us'. Notification type and status should be matched exactly.","For merchant revenue analysis, rank merchants by their total successful transaction amounts without specifying any rank number To derive user engagement metrics, aggregate total session durations for users by joining with the user_sessions table over an unspecified date range. To calculate the Average Balance of user wallets, join the users and wallet_transactions_daily tables, then compute the average balance for a specified period User country should be matched case-insensitively, e.g., LOWER(users.country) = 'us'. Notification type and status should be matched exactly."
ewallet,tsql,instructions_string_matching,"SELECT m.name, COUNT(c.cid) AS total_coupons FROM consumer_div.merchants AS m JOIN consumer_div.coupons AS c ON m.mid = c.merchant_id WHERE m.status = 'active' AND LOWER(m.category) LIKE '%retail%' GROUP BY m.name;",How many active retail merchants have issued coupons? Return the merchant name and the total number of coupons issued.,"Merchant category should be matched case-insensitively with wildcards, e.g., LOWER(merchants.category) LIKE '%...%'.","To determine user notification settings, combine the users and user_settings tables in a CTE, selecting the most recent settings for each user.
Merchant category should be matched case-insensitively with wildcards, e.g., LOWER(merchants.category) LIKE '%...%'.
To get the total number of transactions per user, join the users and wallet_transactions tables in a common table expression (CTE), then sum up transactions by user ID
To assess coupon utilization, link the coupons and wallet_transactions tables in a CTE, filter for particular merchants, then calculate the total discount amounts"
ewallet,tsql,keywords_aggregate,"SELECT COUNT(*) AS TUC FROM consumer_div.user_sessions WHERE session_start_ts >= DATEADD(MONTH, -1, GETDATE()) OR session_end_ts >= DATEADD(MONTH, -1, GETDATE());","What is the TUC in the past month, inclusive of 1 month ago? Return the total count.",TUC = Total number of user sessions in the past month,"To analyze coupon usage, join the coupons and transactions tables in a CTE, filtering for any required criteria, then aggregate to find the total discount amount TUC = Total number of user sessions in the past month To get user notification preferences, join the users and settings tables in a CTE, selecting the most recent settings snapshot for each user. To get the total number of transactions per user, join the users and transactions tables in a common table expression (CTE), then aggregate by user_id to count transactions"
ewallet,tsql,keywords_aggregate,"SELECT AVG(balance) AS AMB FROM consumer_div.wallet_user_balance_daily WHERE updated_at >= DATEADD(DAY, -7, GETDATE());","What is the average AMB for user wallets updated in the past week, inclusive of 7 days ago? Return the average balance.",AMB = average balance per user (for the given time duration),"For coupon usage analysis, join the coupons and wallet_transactions_daily tables in a CTE, filtering for merchants of interest, and aggregate to compute the total discount amount utilized
To get the total number of transactions per user, join the users and wallet_transactions_daily tables in a Common Table Expression (CTE), then aggregate by user_id to count the transactions
To determine user notification preferences, join the users table with the user_setting_snapshot table in a CTE, selecting the most recent settings snapshot for each user.
AMB = average balance per user (for the given time duration)"
ewallet,tsql,keywords_ratio,"SELECT m.name, (COUNT(DISTINCT wtd.coupon_id) * 1.0 / NULLIF(COUNT(DISTINCT wtd.txid), 0)) AS CPUR FROM consumer_div.wallet_transactions_daily AS wtd JOIN consumer_div.merchants AS m ON wtd.receiver_id = m.mid WHERE wtd.status = 'success' GROUP BY m.name;","Calculate the CPUR for each merchant, considering only successful transactions. Return the merchant name and CPUR.",CPUR (coupon usage rate) = number of distinct coupons used / number of distinct transactions,"To get the total number of transactions per user, join the users and wallet_transactions tables, then aggregate by user_id To analyze user engagement, join the users and user_sessions tables, then aggregate to calculate the total session duration per user for a given period. CPUR (coupon usage rate) = number of distinct coupons used / number of distinct transactions To get user notification preferences, join the users and user_settings tables, then select the latest settings for each user"
ewallet,tsql,keywords_ratio,"SELECT (SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) * 1.0 / COUNT(*)) AS STR FROM consumer_div.wallet_transactions_daily WHERE created_at >= DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()) - 1, 0) AND created_at < DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0);",What was the STR for wallet transactions in the previous month?,STR (success transaction rate) = number of successful transactions / total number of transactions,"To analyze user engagement, join the users and sessions tables in a CTE, then aggregate to measure total session length per user for a specified period.
To get user notification preferences, join the users and settings tables in a CTE, then filter for the most recent settings per user
STR (success transaction rate) = number of successful transactions / total number of transactions
To analyze coupon usage, join the coupons and transactions tables in a CTE, filter for a particular merchant, then aggregate to calculate the total discount value"
