name: tests

on: [pull_request]

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: psf/black@stable
  test:
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'
          cache: 'pip'
      - name: Install pip dependencies
        run: |
          pip install --upgrade pip setuptools
          pip install -r requirements_test.txt
          pip install pytest
      - name: Run tests
        run: |
          pytest tests/test*.py
