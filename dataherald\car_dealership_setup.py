#!/usr/bin/env python3
"""
Create a sample car dealership database for DataHerald evaluation
Based on the sql-eval car_dealership schema
"""

import sqlite3
import os
from datetime import datetime, timedelta
import random

def create_car_dealership_db():
    """Create a comprehensive car dealership database with sample data"""
    
    # Remove existing database if it exists
    db_path = 'car_dealership.db'
    if os.path.exists(db_path):
        os.remove(db_path)
    
    # Create database connection
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print("🚗 Creating Car Dealership Database...")
    
    # Create customers table
    cursor.execute('''
    CREATE TABLE customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        first_name TEXT NOT NULL,
        last_name TEXT NOT NULL,
        email TEXT UNIQUE,
        phone TEXT,
        address TEXT,
        city TEXT,
        state TEXT,
        zip_code TEXT,
        created_at DATE DEFAULT CURRENT_DATE
    )
    ''')
    
    # Create cars table
    cursor.execute('''
    CREATE TABLE cars (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        make TEXT NOT NULL,
        model TEXT NOT NULL,
        year INTEGER,
        color TEXT,
        price DECIMAL(10,2),
        mileage INTEGER,
        condition TEXT CHECK(condition IN ('new', 'used', 'certified')),
        vin TEXT UNIQUE,
        created_at DATE DEFAULT CURRENT_DATE
    )
    ''')
    
    # Create salespersons table
    cursor.execute('''
    CREATE TABLE salespersons (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        first_name TEXT NOT NULL,
        last_name TEXT NOT NULL,
        email TEXT UNIQUE,
        phone TEXT,
        hire_date DATE,
        commission_rate DECIMAL(5,2) DEFAULT 0.05,
        created_at DATE DEFAULT CURRENT_DATE
    )
    ''')
    
    # Create sales table
    cursor.execute('''
    CREATE TABLE sales (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER,
        car_id INTEGER,
        salesperson_id INTEGER,
        sale_date DATE,
        sale_price DECIMAL(10,2),
        financing_type TEXT CHECK(financing_type IN ('cash', 'loan', 'lease')),
        created_at DATE DEFAULT CURRENT_DATE,
        FOREIGN KEY (customer_id) REFERENCES customers (id),
        FOREIGN KEY (car_id) REFERENCES cars (id),
        FOREIGN KEY (salesperson_id) REFERENCES salespersons (id)
    )
    ''')
    
    # Create payments_received table
    cursor.execute('''
    CREATE TABLE payments_received (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        sale_id INTEGER,
        payment_date DATE,
        payment_amount DECIMAL(10,2),
        payment_method TEXT CHECK(payment_method IN ('cash', 'check', 'credit_card', 'bank_transfer')),
        created_at DATE DEFAULT CURRENT_DATE,
        FOREIGN KEY (sale_id) REFERENCES sales (id)
    )
    ''')
    
    print("📊 Inserting sample data...")
    
    # Insert sample customers
    customers_data = [
        ('John', 'Smith', '<EMAIL>', '555-0101', '123 Main St', 'New York', 'NY', '10001'),
        ('Sarah', 'Johnson', '<EMAIL>', '555-0102', '456 Oak Ave', 'Los Angeles', 'CA', '90210'),
        ('Michael', 'Brown', '<EMAIL>', '555-0103', '789 Pine St', 'Chicago', 'IL', '60601'),
        ('Emily', 'Davis', '<EMAIL>', '555-0104', '321 Elm St', 'Houston', 'TX', '77001'),
        ('David', 'Wilson', '<EMAIL>', '555-0105', '654 Maple Ave', 'Phoenix', 'AZ', '85001'),
        ('Lisa', 'Garcia', '<EMAIL>', '555-0106', '987 Cedar St', 'Philadelphia', 'PA', '19101'),
        ('James', 'Martinez', '<EMAIL>', '555-0107', '147 Birch Ave', 'San Antonio', 'TX', '78201'),
        ('Jennifer', 'Anderson', '<EMAIL>', '555-0108', '258 Spruce St', 'San Diego', 'CA', '92101'),
        ('Robert', 'Taylor', '<EMAIL>', '555-0109', '369 Willow Ave', 'Dallas', 'TX', '75201'),
        ('Amanda', 'Thomas', '<EMAIL>', '555-0110', '741 Ash St', 'San Jose', 'CA', '95101')
    ]
    
    cursor.executemany('''
    INSERT INTO customers (first_name, last_name, email, phone, address, city, state, zip_code)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', customers_data)
    
    # Insert sample cars
    cars_data = [
        ('Toyota', 'Camry', 2023, 'Silver', 28500.00, 0, 'new', 'JT2BF28K0X0123456'),
        ('Honda', 'Civic', 2022, 'Blue', 24000.00, 15000, 'used', 'JHMFC36217S123456'),
        ('Ford', 'F-150', 2023, 'Black', 45000.00, 0, 'new', '1FTFW1ET5DFC12345'),
        ('Chevrolet', 'Malibu', 2021, 'White', 22000.00, 25000, 'certified', '1G1ZD5ST5MF123456'),
        ('Nissan', 'Altima', 2023, 'Red', 26000.00, 0, 'new', '1N4AL3AP8JC123456'),
        ('BMW', '3 Series', 2022, 'Gray', 42000.00, 12000, 'certified', 'WBA8E1C50HN123456'),
        ('Mercedes-Benz', 'C-Class', 2023, 'Black', 48000.00, 0, 'new', 'WDDGF4HB1JR123456'),
        ('Audi', 'A4', 2021, 'White', 38000.00, 18000, 'used', 'WAUZZZF47MA123456'),
        ('Hyundai', 'Elantra', 2023, 'Blue', 23000.00, 0, 'new', 'KMHL14JA1PA123456'),
        ('Kia', 'Optima', 2022, 'Silver', 25000.00, 20000, 'used', 'KNAGM4A78J5123456')
    ]
    
    cursor.executemany('''
    INSERT INTO cars (make, model, year, color, price, mileage, condition, vin)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', cars_data)
    
    # Insert sample salespersons
    salespersons_data = [
        ('Alex', 'Rodriguez', '<EMAIL>', '555-0201', '2023-01-15', 0.06),
        ('Maria', 'Lopez', '<EMAIL>', '555-0202', '2022-06-01', 0.05),
        ('Kevin', 'Chen', '<EMAIL>', '555-0203', '2023-03-10', 0.055),
        ('Rachel', 'Kim', '<EMAIL>', '555-0204', '2022-09-20', 0.05),
        ('Daniel', 'White', '<EMAIL>', '555-0205', '2023-02-01', 0.065)
    ]
    
    cursor.executemany('''
    INSERT INTO salespersons (first_name, last_name, email, phone, hire_date, commission_rate)
    VALUES (?, ?, ?, ?, ?, ?)
    ''', salespersons_data)
    
    # Insert sample sales (recent dates for testing)
    base_date = datetime.now() - timedelta(days=90)
    sales_data = []
    
    for i in range(15):
        sale_date = base_date + timedelta(days=random.randint(0, 90))
        customer_id = random.randint(1, 10)
        car_id = random.randint(1, 10)
        salesperson_id = random.randint(1, 5)
        
        # Get car price for sale price calculation
        cursor.execute('SELECT price FROM cars WHERE id = ?', (car_id,))
        car_price = cursor.fetchone()[0]
        sale_price = car_price * random.uniform(0.95, 1.05)  # 5% variance
        
        financing_types = ['cash', 'loan', 'lease']
        financing_type = random.choice(financing_types)
        
        sales_data.append((customer_id, car_id, salesperson_id, sale_date.strftime('%Y-%m-%d'), 
                          round(sale_price, 2), financing_type))
    
    cursor.executemany('''
    INSERT INTO sales (customer_id, car_id, salesperson_id, sale_date, sale_price, financing_type)
    VALUES (?, ?, ?, ?, ?, ?)
    ''', sales_data)
    
    # Insert sample payments
    cursor.execute('SELECT id, sale_price FROM sales')
    sales_records = cursor.fetchall()
    
    payment_methods = ['cash', 'check', 'credit_card', 'bank_transfer']
    
    for sale_id, sale_price in sales_records:
        # Most sales have full payment
        if random.random() < 0.8:
            payment_amount = sale_price
            payment_method = random.choice(payment_methods)
            payment_date = datetime.now() - timedelta(days=random.randint(1, 30))
            
            cursor.execute('''
            INSERT INTO payments_received (sale_id, payment_date, payment_amount, payment_method)
            VALUES (?, ?, ?, ?)
            ''', (sale_id, payment_date.strftime('%Y-%m-%d'), payment_amount, payment_method))
    
    # Commit changes and close connection
    conn.commit()
    conn.close()
    
    print(f"✅ Car dealership database created successfully!")
    print(f"📁 Database file: {db_path}")
    print(f"📊 Tables created: customers, cars, salespersons, sales, payments_received")
    print(f"📈 Sample data inserted for evaluation")
    
    return db_path

if __name__ == "__main__":
    create_car_dealership_db()
