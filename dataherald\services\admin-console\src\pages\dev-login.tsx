// Development Login Page - Only available in development mode
import { useEffect } from 'react'
import { useRouter } from 'next/router'

export default function DevLogin() {
  const router = useRouter()

  useEffect(() => {
    // Automatically redirect to databases page in development mode
    if (process.env.NODE_ENV === 'development') {
      // Set a development session cookie
      document.cookie = 'dev-session=true; path=/; max-age=86400'
      
      // Redirect to databases page
      router.push('/databases')
    } else {
      // In production, redirect to normal login
      router.push('/api/auth/login')
    }
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Development Mode
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Redirecting to DataHerald dashboard...
          </p>
        </div>
      </div>
    </div>
  )
}
