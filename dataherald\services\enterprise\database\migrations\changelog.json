{"databaseChangeLog": [{"changeSet": {"id": "2", "author": "<PERSON><PERSON><PERSON><PERSON>", "labels": "createCollectionLabel", "context": "createCollectionContext", "comment": "create_collection_comment", "changes": [{"createCollection": {"collectionName": "keys"}}, {"createCollection": {"collectionName": "credits"}}, {"createCollection": {"collectionName": "usages"}}, {"createCollection": {"collectionName": "prompts"}}, {"createCollection": {"collectionName": "nl_generations"}}, {"createCollection": {"collectionName": "sql_generations"}}, {"createCollection": {"collectionName": "users"}}, {"createCollection": {"collectionName": "organizations"}}, {"createCollection": {"collectionName": "golden_sqls"}}, {"createCollection": {"collectionName": "instructions"}}, {"createCollection": {"collectionName": "finetunings"}}, {"createCollection": {"collectionName": "table_descriptions"}}, {"createCollection": {"collectionName": "database_connections"}}, {"createCollection": {"collectionName": "query_history"}}]}}]}