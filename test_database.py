#!/usr/bin/env python3
"""Test the car dealership database"""

import sqlite3

def test_database():
    conn = sqlite3.connect('car_dealership.db')
    cursor = conn.cursor()
    
    print('🚗 Testing Car Dealership Database:')
    print()
    
    # Test 1: Check tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]
    print(f'📊 Tables: {", ".join(tables)}')
    print()
    
    # Test 2: Count records
    for table in ['customers', 'cars', 'sales', 'salespersons', 'payments_received']:
        cursor.execute(f'SELECT COUNT(*) FROM {table}')
        count = cursor.fetchone()[0]
        print(f'{table}: {count} records')
    
    print()
    
    # Test 3: Sample query - Top car models by revenue
    print('📈 Top 5 Car Models by Revenue:')
    cursor.execute('''
    SELECT c.make, c.model, COUNT(s.id) AS sales_count, SUM(s.sale_price) AS total_revenue 
    FROM sales s 
    JOIN cars c ON s.car_id = c.id 
    GROUP BY c.make, c.model 
    ORDER BY total_revenue DESC 
    LIMIT 5
    ''')
    
    results = cursor.fetchall()
    for i, (make, model, sales_count, revenue) in enumerate(results, 1):
        print(f'{i}. {make} {model}: {sales_count} sales, ${revenue:,.2f}')
    
    conn.close()
    print()
    print('✅ Database test completed successfully!')

if __name__ == "__main__":
    test_database()
