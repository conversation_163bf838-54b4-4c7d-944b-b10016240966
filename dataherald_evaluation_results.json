[{"question": "What are the top 5 best selling car models by total revenue?", "expected_sql": "SELECT c.make, c.model, COUNT(s.id) AS total_sales, SUM(s.sale_price) AS total_revenue \n                                  FROM sales AS s \n                                  JOIN cars AS c ON s.car_id = c.id \n                                  GROUP BY c.make, c.model \n                                  ORDER BY total_revenue DESC \n                                  LIMIT 5;", "generated_sql": null, "results_match": false, "expected_rows": 5, "generated_rows": 0}, {"question": "Who were the top 3 sales representatives by total revenue?", "expected_sql": "SELECT sp.first_name, sp.last_name, COUNT(s.id) AS total_sales, SUM(s.sale_price) AS total_revenue \n                                  FROM sales AS s \n                                  JOIN salespersons AS sp ON s.salesperson_id = sp.id \n                                  GROUP BY sp.first_name, sp.last_name \n                                  ORDER BY total_revenue DESC \n                                  LIMIT 3;", "generated_sql": null, "results_match": false, "expected_rows": 3, "generated_rows": 0}, {"question": "Return the top 5 states by total revenue", "expected_sql": "SELECT c.state, COUNT(DISTINCT s.customer_id) AS unique_customers, SUM(s.sale_price) AS total_revenue \n                                  FROM sales AS s \n                                  JOIN customers AS c ON s.customer_id = c.id \n                                  GROUP BY c.state \n                                  ORDER BY total_revenue DESC \n                                  LIMIT 5;", "generated_sql": null, "results_match": false, "expected_rows": 5, "generated_rows": 0}]