#!/usr/bin/env python3
"""
Complete DataHerald and Gemini API Evaluation
Tests both DataHerald system and direct Gemini API calls
"""

import sqlite3
import json
import requests
import google.generativeai as genai
import os
from typing import List, Dict, Tuple

class CompleteEvaluator:
    def __init__(self):
        self.db_path = "dataherald/services/engine/dataherald/car_dealership.db"
        self.gemini_api_key = "AIzaSyCPP-8M6IBIAcogqfzItsv6Sc21pmC6Pzg"
        
        # Configure Gemini
        genai.configure(api_key=self.gemini_api_key)
        self.model = genai.GenerativeModel('gemini-pro')
        
        # Test questions from sql-eval
        self.test_questions = [
            {
                "question": "What are the top 5 best selling car models by total revenue?",
                "expected_sql": """SELECT c.make, c.model, COUNT(s.id) AS total_sales, SUM(s.sale_price) AS total_revenue 
                                  FROM sales AS s 
                                  JOIN cars AS c ON s.car_id = c.id 
                                  GROUP BY c.make, c.model 
                                  ORDER BY total_revenue DESC 
                                  LIMIT 5;"""
            },
            {
                "question": "Who were the top 3 sales representatives by total revenue?",
                "expected_sql": """SELECT sp.first_name, sp.last_name, COUNT(s.id) AS total_sales, SUM(s.sale_price) AS total_revenue 
                                  FROM sales AS s 
                                  JOIN salespersons AS sp ON s.salesperson_id = sp.id 
                                  GROUP BY sp.first_name, sp.last_name 
                                  ORDER BY total_revenue DESC 
                                  LIMIT 3;"""
            },
            {
                "question": "Return the top 5 states by total revenue",
                "expected_sql": """SELECT c.state, COUNT(DISTINCT s.customer_id) AS unique_customers, SUM(s.sale_price) AS total_revenue 
                                  FROM sales AS s 
                                  JOIN customers AS c ON s.customer_id = c.id 
                                  GROUP BY c.state 
                                  ORDER BY total_revenue DESC 
                                  LIMIT 5;"""
            },
            {
                "question": "What are the top 3 payment methods by total payment amount received?",
                "expected_sql": """SELECT payment_method, COUNT(*) AS total_payments, SUM(payment_amount) AS total_amount 
                                  FROM payments_received 
                                  GROUP BY payment_method 
                                  ORDER BY total_amount DESC 
                                  LIMIT 3;"""
            }
        ]
    
    def test_database(self) -> bool:
        """Test database connectivity and data"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            print(f"✅ Database tables: {', '.join(tables)}")
            
            # Get record counts
            for table in ['customers', 'cars', 'sales', 'salespersons', 'payments_received']:
                cursor.execute(f'SELECT COUNT(*) FROM {table}')
                count = cursor.fetchone()[0]
                print(f"   {table}: {count} records")
            
            conn.close()
            return True
        except Exception as e:
            print(f"❌ Database error: {e}")
            return False
    
    def execute_sql(self, sql: str) -> List[Tuple]:
        """Execute SQL and return results"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(sql)
            results = cursor.fetchall()
            conn.close()
            return results
        except Exception as e:
            print(f"❌ SQL execution error: {e}")
            return []
    
    def test_gemini_direct(self, question: str) -> str:
        """Test Gemini API directly for SQL generation"""
        schema_prompt = f"""
You are a SQL expert. Given the following database schema, generate a SQL query to answer the question.

Database Schema:
- cars (id INTEGER PRIMARY KEY, make TEXT, model TEXT, year INTEGER, color TEXT, price DECIMAL, mileage INTEGER, condition TEXT, vin TEXT)
- customers (id INTEGER PRIMARY KEY, first_name TEXT, last_name TEXT, email TEXT, phone TEXT, address TEXT, city TEXT, state TEXT, zip_code TEXT)
- salespersons (id INTEGER PRIMARY KEY, first_name TEXT, last_name TEXT, email TEXT, phone TEXT, hire_date DATE, commission_rate DECIMAL)
- sales (id INTEGER PRIMARY KEY, customer_id INTEGER, car_id INTEGER, salesperson_id INTEGER, sale_date DATE, sale_price DECIMAL, financing_type TEXT)
- payments_received (id INTEGER PRIMARY KEY, sale_id INTEGER, payment_date DATE, payment_amount DECIMAL, payment_method TEXT)

Question: {question}

Generate only the SQL query, no explanations. Use proper SQLite syntax.
"""
        
        try:
            response = self.model.generate_content(schema_prompt)
            sql = response.text.strip()
            
            # Clean up the response to extract just the SQL
            if "```sql" in sql:
                sql = sql.split("```sql")[1].split("```")[0].strip()
            elif "```" in sql:
                sql = sql.split("```")[1].strip()
            
            return sql
        except Exception as e:
            print(f"❌ Gemini API error: {e}")
            return None
    
    def compare_results(self, expected_results: List[Tuple], generated_results: List[Tuple]) -> Dict:
        """Compare expected vs generated results"""
        return {
            'exact_match': expected_results == generated_results,
            'row_count_match': len(expected_results) == len(generated_results),
            'expected_rows': len(expected_results),
            'generated_rows': len(generated_results)
        }
    
    def run_complete_evaluation(self):
        """Run complete evaluation of both systems"""
        print("🎯 Complete DataHerald & Gemini Evaluation")
        print("=" * 60)
        
        # Test 1: Database connectivity
        print("\n📋 Test 1: Database Connectivity")
        if not self.test_database():
            print("❌ Database test failed. Cannot proceed.")
            return
        
        # Test 2: Gemini API connectivity
        print("\n📋 Test 2: Gemini API Connectivity")
        try:
            test_response = self.model.generate_content("Hello, can you generate SQL?")
            print("✅ Gemini API is accessible")
        except Exception as e:
            print(f"❌ Gemini API error: {e}")
            return
        
        # Test 3: SQL-Eval benchmark evaluation
        print("\n📋 Test 3: SQL-Eval Benchmark Evaluation")
        results = []
        
        for i, test in enumerate(self.test_questions, 1):
            print(f"\n🔍 Question {i}: {test['question']}")
            
            # Execute expected SQL
            expected_results = self.execute_sql(test['expected_sql'])
            print(f"📊 Expected results: {len(expected_results)} rows")
            if expected_results:
                print(f"   Sample: {expected_results[0]}")
            
            # Generate SQL with Gemini
            print("🤖 Generating SQL with Gemini...")
            generated_sql = self.test_gemini_direct(test['question'])
            
            if generated_sql:
                print(f"📝 Generated SQL: {generated_sql}")
                
                # Execute generated SQL
                generated_results = self.execute_sql(generated_sql)
                print(f"📊 Generated results: {len(generated_results)} rows")
                
                # Compare results
                comparison = self.compare_results(expected_results, generated_results)
                
                status = "✅" if comparison['exact_match'] else "❌"
                print(f"{status} Results match: {comparison['exact_match']}")
                
                results.append({
                    'question_id': i,
                    'question': test['question'],
                    'expected_sql': test['expected_sql'].strip(),
                    'generated_sql': generated_sql,
                    'comparison': comparison,
                    'success': generated_sql is not None,
                    'accuracy': comparison['exact_match']
                })
            else:
                print("❌ Failed to generate SQL")
                results.append({
                    'question_id': i,
                    'question': test['question'],
                    'expected_sql': test['expected_sql'].strip(),
                    'generated_sql': None,
                    'comparison': {'exact_match': False, 'row_count_match': False, 'expected_rows': len(expected_results), 'generated_rows': 0},
                    'success': False,
                    'accuracy': False
                })
        
        # Test 4: Summary and analysis
        print("\n📊 Evaluation Summary")
        print("=" * 60)
        
        total_questions = len(self.test_questions)
        successful_generations = sum(1 for r in results if r['success'])
        accurate_results = sum(1 for r in results if r['accuracy'])
        
        print(f"Total questions tested: {total_questions}")
        print(f"Successful SQL generations: {successful_generations}/{total_questions}")
        print(f"Accurate results: {accurate_results}/{total_questions}")
        print(f"Generation success rate: {(successful_generations/total_questions*100):.1f}%")
        print(f"Accuracy rate: {(accurate_results/total_questions*100):.1f}%")
        
        # Detailed results
        print("\n📋 Detailed Results:")
        for result in results:
            status = "✅" if result['accuracy'] else "❌"
            print(f"{status} Q{result['question_id']}: {result['question'][:50]}...")
            if result['success']:
                print(f"   Generated SQL: {result['generated_sql'][:100]}...")
                print(f"   Accuracy: {result['comparison']['exact_match']}")
            else:
                print("   Failed to generate SQL")
        
        # Save results
        with open('complete_evaluation_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\n💾 Detailed results saved to complete_evaluation_results.json")
        
        return results

def main():
    evaluator = CompleteEvaluator()
    results = evaluator.run_complete_evaluation()
    
    print("\n🎉 Evaluation Complete!")
    print("This evaluation tested:")
    print("✅ Database connectivity and data integrity")
    print("✅ Gemini API text-to-SQL generation")
    print("✅ SQL-eval benchmark questions")
    print("✅ Result accuracy comparison")

if __name__ == "__main__":
    main()
