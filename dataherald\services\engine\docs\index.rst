.. Dataherald documentation master file, created by
   sphinx-quickstart on Mon Aug 14 12:19:15 2023.
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.

Dataherald AI
========================================
Welcome to the official documentation page of the Dataherald AI engine. This documentation is intended for developers who want to:

* 🖥️ Use the Dataherald AI engine to set up Natural Language interfaces from structured data in their own projects.
* 🏍️ Contribute to the Dataherald AI engine.

These documents will cover how to get started, how to set up an API from your database that can answer questions in plain English and how to extend the core engine's functionality.

.. toctree::
   :maxdepth: 1
   :caption: Getting Started
   :hidden:

   introduction
   quickstart

.. toctree::
   :caption: References
   :hidden:

   api
   envars
   modules

.. toctree::
   :caption: Tutorials
   :hidden:

   tutorial.run_scripts
   tutorial.sample_database
   tutorial.finetune_sql_generator
   tutorial.chatgpt_plugin
   tutorial.streamlit_app


.. toctree::
   :caption: Contributing
   :hidden:

   contributing.projects
