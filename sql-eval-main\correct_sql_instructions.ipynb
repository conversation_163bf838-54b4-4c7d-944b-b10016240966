{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Correct invalid/inaccurate SQL or instruction translations\n", "\n", "Jump to sections:\n", "- <a href=\"#invalid\">Correct invalid translated SQL</a>\n", "- <a href=\"#inaccurate\">Correct valid but inaccurate translated SQL</a>\n", "- <a href=\"#instructions\">Translate just the instructions column with the instructions_to_dialect function</a>\n", "- <a href=\"#inspectinstr\">Inspect the translated instructions column</a>\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "from tqdm import tqdm\n", "import time\n", "from utils.creds import db_creds_all\n", "import sqlparse\n", "tqdm.pandas()\n", "pd.set_option('display.max_colwidth', None)\n", "\n", "dialect = \"mysql\"\n", "csv_file = f\"data/instruct_advanced_{dialect}.csv\"\n", "df = pd.read_csv(csv_file)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for all invalid SQL in the csv file\n", "# NOTE: You are encouraged to update defog-data databases first before running this cell\n", "from utils.dialects import test_valid_tsql, test_valid_mysql, test_valid_bq, test_valid_sqlite\n", "from eval.eval import get_all_minimal_queries\n", "\n", "# First check if sql col contains <INVALID ERR MSG>\n", "if len(df[df[\"query\"].str.contains(\"<INVALID ERR MSG>\")].copy()) > 0:\n", "    # split query into list by semicolon\n", "    df[\"sql_list\"] = df[\"err_msg_list\"] = df[\"valid_list\"] = df[\"query\"].apply(lambda x: [s for s in x.split(\";\") if s])\n", "    \n", "    # Extract the translated query, error message and validity\n", "    df[\"sql_list\"] = df[\"sql_list\"].apply(lambda x: [item.split(\"<INVALID TRANSLATION>: \")[1].split(\"-----------------\")[0] if \"<INVALID TRANSLATION>:\" in item else item for item in x])\n", "    df[\"err_msg_list\"] = df[\"err_msg_list\"].apply(lambda x: [item.split(\"<INVALID ERR MSG>: \")[1].split(\"-----------------\")[0] if \"<INVALID ERR MSG>:\" in item else \"\" for item in x])\n", "    df[\"valid_list\"] = df[\"valid_list\"].apply(lambda x: [False if \"<INVALID ERR MSG>:\" in item else True for item in x])\n", "\n", "else:\n", "    # Check validity of all queries on defog-data databases\n", "    df[\"result_tuple_list\"] = \"\"\n", "    df[\"sql_list\"] = \"\"\n", "    sql_col = \"query\"\n", "\n", "    for i, row in tqdm(df.iterrows(), total=len(df)):\n", "        sqls = row[sql_col]\n", "        sql_list = get_all_minimal_queries(sqls)\n", "        df.at[i, \"sql_list\"] = sql_list\n", "        if dialect == \"bigquery\":\n", "            result_tuple_list = test_valid_bq(db_creds_all[\"bigquery\"], sql_list, row.db_name)\n", "        elif dialect == \"mysql\":\n", "            result_tuple_list = test_valid_mysql(db_creds_all[\"mysql\"], sql_list, row.db_name)\n", "        elif dialect == \"sqlite\":\n", "            result_tuple_list = test_valid_sqlite(db_creds_all[\"sqlite\"], sql_list, row.db_name)\n", "        elif dialect == \"tsql\":\n", "            result_tuple_list = test_valid_tsql(db_creds_all[\"tsql\"], sql_list, row.db_name)\n", "        else:\n", "            raise ValueError(\"Dialect not supported\")\n", "        df.at[i, \"result_tuple_list\"] = result_tuple_list\n", "    df[f\"valid_list\"] = df[\"result_tuple_list\"].apply(lambda x: [item[0] for item in x])\n", "    df[f\"err_msg_list\"] = df[\"result_tuple_list\"].apply(lambda x: [item[1] for item in x])\n", "    df.drop(columns=[\"result_tuple_list\"], inplace=True)\n", "\n", "invalid_df = df[df[\"valid_list\"].apply(lambda x: False in x)]\n", "    \n", "print(\"No. of invalid queries:\", len(invalid_df))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"invalid\"></a>\n", "### Correct invalid translated SQL\n", "\n", "Use the next few cells to correct a single SQL that was translated to a different dialect but found to be invalid when executed on a database."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sample 1 row from invalid_df\n", "invalid_eg = invalid_df.sample(1)\n", "\n", "# Get the postgres query for the invalid index\n", "postgres_csv_file = csv_file.replace(dialect, \"postgres\")\n", "postgres_df = pd.read_csv(postgres_csv_file)\n", "postgres_query = postgres_df.loc[invalid_eg.index[0], \"query\"]\n", "print(\"Postgres query for comparison:\\n\", sqlparse.format(postgres_query, reindent=True, keyword_case='upper'))\n", "\n", "invalid_eg"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get only invalid indices of sql_list\n", "invalid_indices = [i for i, val in enumerate(invalid_eg[\"valid_list\"].iloc[0]) if not val]\n", "print(invalid_indices)\n", "\n", "# Store values\n", "db_name = invalid_eg[\"db_name\"].values[0]\n", "query = invalid_eg[\"query\"].values[0]\n", "question = invalid_eg[\"question\"].values[0]\n", "instructions = invalid_eg[\"instructions\"].values[0] if \"instructions\" in invalid_eg.columns else \"\"\n", "sql_list = invalid_eg[\"sql_list\"].values[0]\n", "err_msg_list = invalid_eg[\"err_msg_list\"].values[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Use LLM to rewrite the SQL for the dialect\n", "from openai import OpenAI\n", "from defog_utils.utils_sql import normalize_sql\n", "import json\n", "openai = OpenAI(api_key=os.environ.get(\"OPENAI_API_KEY\"))\n", "model = \"gpt-4o\"\n", "\n", "# TODO: Specify special SQL syntax rules for the dialect. e.g. \"STR_TO_DATE function is not supported in T-SQL. Instead use the DATEFROMPARTS function to concatenate the date parts.\"\n", "special_instructions = \"\"\"\n", "\"\"\"\n", "\n", "def rewrite_invalid_sql(\n", "    model: str,\n", "    sql: str,\n", "    question: str,\n", "    instructions: str,\n", "    err_msg: str,\n", "    to_dialect: str,\n", "    special_instructions: str,\n", ") -> str:\n", "    \"\"\"\n", "    Use LLM to rewrite invalid SQL for the dialect\n", "    \"\"\"\n", "    if \"Or data values in defog-data databases could be outdated.\" in err_msg:\n", "        err_msg = err_msg.replace(\"Or data values in defog-data databases could be outdated.\", \"\")\n", "    messages = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": f\"\"\"Your task is to rewrite an invalid SQL query in the {to_dialect} dialect to answer a specific question.\n", "{special_instructions}\"\"\",\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": f\"\"\"Question to answer: {question}\n", "Instructions: {instructions}\n", "Invalid SQL: {sql}\n", "Error to fix: {err_msg}\n", "\n", "Format your response as a valid JSON string with reason and sql keys. \n", "Your response should look like the string below:\n", "{{\n", "    \"reason\": \"Your reasoning for the response\",\n", "    \"sql\": \"The valid rewritten query for {to_dialect}\"\n", "}}\n", "\n", "Do not include any other information before and after the JSON string.\n", "\"\"\",\n", "        },\n", "    ]\n", "    completion = openai.chat.completions.create(\n", "        model=model,\n", "        messages=messages,\n", "        max_tokens=2000,\n", "        temperature=0,\n", "        response_format={\"type\": \"json_object\"},\n", "    )\n", "    completion = completion.choices[0].message.content\n", "    try:\n", "        completion_dict = json.loads(completion)\n", "    except:\n", "        print(f\"Error parsing completion {completion}\")\n", "        completion_dict = {\n", "            \"sql\": None,\n", "            \"reason\": None,\n", "        }  \n", "    return completion_dict\n", "\n", "rewritten_sql_list = []\n", "for i in range(len(sql_list)):\n", "    if i in invalid_indices:\n", "        sql = sql_list[i]\n", "        err_msg = err_msg_list[i]\n", "        completion_dict = rewrite_invalid_sql(model, sql, question, instructions, err_msg, dialect, special_instructions)\n", "        sql_rewritten = completion_dict['sql']\n", "\n", "        if sql_rewritten is not None:\n", "            sql_rewritten = normalize_sql(sql_rewritten)\n", "            rewritten_sql_list.append(sql_rewritten)\n", "            print(\"Reason: \", completion_dict['reason'])\n", "            print(\"Rewritten SQL: \", sqlparse.format(sql_rewritten, reindent=True, keyword_case='upper'))\n", "            print(\"\\n\")\n", "    else:\n", "        print(i)\n", "        rewritten_sql_list.append(sql_list[i])\n", "\n", "# ensure no duplicates in rewritten_sql_list\n", "rewritten_sql_list = list(set(rewritten_sql_list))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# RUN THIS CELL ONLY if you want to manually rewrite the SQL for the dialect\n", "rewritten_sql_list = [\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test validity of all SQL in rewritten_sql_list\n", "if dialect == \"bigquery\":\n", "    result_tuple_list = test_valid_bq(db_creds_all[\"bigquery\"], rewritten_sql_list, db_name)\n", "elif dialect == \"mysql\":\n", "    result_tuple_list = test_valid_mysql(db_creds_all[\"mysql\"], rewritten_sql_list, db_name)\n", "elif dialect == \"sqlite\":\n", "    result_tuple_list = test_valid_sqlite(db_creds_all[\"sqlite\"], rewritten_sql_list, db_name)\n", "elif dialect == \"tsql\":\n", "    result_tuple_list = test_valid_tsql(db_creds_all[\"tsql\"], rewritten_sql_list, db_name)\n", "else:\n", "    raise ValueError(\"Dialect not supported\")\n", "valid_list, err_msg_list = map(list, zip(*result_tuple_list))\n", "for i in rewritten_sql_list:\n", "    print(i)\n", "print(\"Valid list:\", valid_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Replace invalid sql with rewritten sql in the original dataset\n", "# If all items True in valid_list, then replace the original sql with rewritten sql\n", "if all(valid_list):\n", "    df_index = invalid_eg.index[0]\n", "    df.at[df_index, \"sql_list\"] = rewritten_sql_list\n", "    df.at[df_index, \"valid_list\"] = valid_list\n", "    df.at[df_index, \"err_msg_list\"] = err_msg_list\n", "    df.at[df_index, \"query\"] = \";\".join(rewritten_sql_list).replace(\";;\", \";\")\n", "    print(\"Updated original dataset with rewritten SQL at index\", df_index)\n", "    df2 = df.drop(columns=[\"valid_list\", \"err_msg_list\", \"sql_list\"])\n", "    df2.to_csv(csv_file, index=False)\n", "\n", "    # remove the row from invalid_df\n", "    invalid_df = invalid_df[invalid_df.index != df_index]\n", "    print(\"Removed invalid query from the invalid dataset\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"inaccurate\"></a>\n", "### Correct valid but inaccurate translated SQL\n", "\n", "Use the next few cells if you've discovered a wrong SQL that does not accurately answer the question."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#TODO: Insert question that has inaccurate SQL\n", "qn_inaccurate = \"\" \n", "\n", "# Get row from df where qn_inaccurate is a substring in the query column\n", "inacc_eg = df[df[\"question\"].str.contains(qn_inaccurate, case=False, na=False)]\n", "inacc_eg"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#TODO: Specify the indices of inaccurate SQL in the sql_list\n", "inacc_indices = [0]\n", "\n", "# Check that indices are in range of valid_list\n", "if any([i >= len(inacc_eg[\"valid_list\"].iloc[0]) for i in inacc_indices]):\n", "    raise Exception(\"Index out of range. Please check the indices again.\")\n", "\n", "# Store values\n", "db_name = inacc_eg[\"db_name\"].values[0]\n", "query = inacc_eg[\"query\"].values[0]\n", "question = inacc_eg[\"question\"].values[0]\n", "instructions = inacc_eg[\"instructions\"].values[0] if \"instructions\" in inacc_eg.columns else \"\"\n", "sql_list = inacc_eg[\"sql_list\"].values[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# use LLM to rewrite the SQL for the dialect\n", "from openai import OpenAI\n", "from defog_utils.utils_sql import normalize_sql\n", "import json\n", "openai = OpenAI(api_key=os.environ.get(\"OPENAI_API_KEY\"))\n", "model = \"gpt-4o\"\n", "\n", "# TODO: Specify reasons for why the SQL is inaccurate for the question\"\n", "special_instructions = \"\"\"\n", "\"\"\"\n", "\n", "def rewrite_inacc_sql(\n", "    model: str,\n", "    sql: str,\n", "    question: str,\n", "    instructions: str,\n", "    to_dialect: str,\n", "    special_instructions: str,\n", ") -> str:\n", "    \"\"\"\n", "    Use LLM to rewrite inaccurate SQL for the dialect\n", "    \"\"\"\n", "    messages = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": f\"\"\"Your task is to rewrite an inaccurate SQL query in the {to_dialect} dialect to answer a specific question. Analyze the question and SQL query to determine why the SQL query is inaccurate before rewriting it.\"\"\",\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": f\"\"\"Question to answer: {question}\n", "Instructions: {instructions}\n", "Inaccurate SQL: {sql}\n", "{special_instructions}\n", "\n", "Format your response as a valid JSON string with reason and sql keys. \n", "Your response should look like the string below:\n", "{{\n", "    \"reason\": \"Your reasoning for the response\",\n", "    \"sql\": \"The valid rewritten query for {to_dialect}\"\n", "}}\n", "\n", "Do not include any other information before and after the JSON string.\n", "\"\"\",\n", "        },\n", "    ]\n", "    completion = openai.chat.completions.create(\n", "        model=model,\n", "        messages=messages,\n", "        max_tokens=2000,\n", "        temperature=0,\n", "        response_format={\"type\": \"json_object\"},\n", "    )\n", "    completion = completion.choices[0].message.content\n", "    try:\n", "        completion_dict = json.loads(completion)\n", "    except:\n", "        print(f\"Error parsing completion {completion}\")\n", "        completion_dict = {\n", "            \"sql\": None,\n", "            \"reason\": None,\n", "        }  \n", "    return completion_dict\n", "\n", "rewritten_sql_list = []\n", "for i in range(len(sql_list)):\n", "    if i in inacc_indices:\n", "        sql = sql_list[i]\n", "        completion_dict = rewrite_inacc_sql(model, sql, question, instructions, dialect, special_instructions)\n", "        sql_rewritten = completion_dict['sql']\n", "\n", "        if sql_rewritten is not None:\n", "            sql_rewritten = normalize_sql(sql_rewritten)\n", "            rewritten_sql_list.append(sql_rewritten)\n", "            print(\"Reason: \", completion_dict['reason'])\n", "            print(\"Rewritten SQL: \", sqlparse.format(sql_rewritten, reindent=True, keyword_case='upper'))\n", "            print(\"\\n\")\n", "    else:\n", "        print(i)\n", "        rewritten_sql_list.append(sql_list[i])\n", "\n", "# Ensure no duplicates in rewritten_sql_list\n", "rewritten_sql_list = list(set(rewritten_sql_list))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test validity of all SQL in rewritten_sql_list\n", "if dialect == \"bigquery\":\n", "    result_tuple_list = test_valid_bq(db_creds_all[\"bigquery\"], rewritten_sql_list, db_name)\n", "elif dialect == \"mysql\":\n", "    result_tuple_list = test_valid_mysql(db_creds_all[\"mysql\"], rewritten_sql_list, db_name)\n", "elif dialect == \"sqlite\":\n", "    result_tuple_list = test_valid_sqlite(db_creds_all[\"sqlite\"], rewritten_sql_list, db_name)\n", "elif dialect == \"tsql\":\n", "    result_tuple_list = test_valid_tsql(db_creds_all[\"tsql\"], rewritten_sql_list, db_name)\n", "else:\n", "    raise ValueError(\"Dialect not supported\")\n", "valid_list, err_msg_list = map(list, zip(*result_tuple_list))\n", "for i in rewritten_sql_list:\n", "    print(i)\n", "print(\"Valid list:\", valid_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Replace inaccurate sql with rewritten sql in the original dataset\n", "# If all items True in valid_list, then replace the original sql with rewritten sql\n", "if all(valid_list):\n", "    df_index = inacc_eg.index[0]\n", "    df.at[df_index, \"sql_list\"] = rewritten_sql_list\n", "    df.at[df_index, \"valid_list\"] = valid_list\n", "    df.at[df_index, \"err_msg_list\"] = err_msg_list\n", "    df.at[df_index, \"query\"] = \";\".join(rewritten_sql_list).replace(\";;\", \";\")\n", "    print(\"Updated original dataset with rewritten SQL at index\", df_index)\n", "    df2 = df.drop(columns=[\"valid_list\", \"err_msg_list\", \"sql_list\"])\n", "    df2.to_csv(csv_file, index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"instructions\"></a>\n", "### Translate just the instructions column\n", "\n", "Use the next cell to translate the instructions with dialect-specific SQL syntax using the instructions_to_{dialect} functions in `utils/dialects.py`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Feel free to modify this cell for future dialects\n", "\n", "from utils.dialects import instructions_to_sqlite, instructions_to_tsql, instructions_to_mysql\n", "\n", "if \"instructions\" in df.columns:\n", "    if dialect == \"sqlite\":\n", "        df['instructions'] = df['instructions'].fillna(\"\")\n", "        df[\"instructions\"] = df.progress_apply(\n", "            lambda x: instructions_to_sqlite(x[\"instructions\"]), axis=1\n", "        )\n", "    elif dialect == \"tsql\":\n", "        df['instructions'] = df['instructions'].fillna(\"\")\n", "        df[\"instructions\"] = df.progress_apply(\n", "            lambda x: instructions_to_tsql(x[\"instructions\"]), axis=1\n", "        )\n", "    elif dialect == \"mysql\":\n", "        print(df['instructions'].nunique())\n", "        df['instructions'] = df['instructions'].fillna(\"\")\n", "        df[\"instructions\"] = df.progress_apply(\n", "            lambda x: instructions_to_mysql(x[\"instructions\"]), axis=1\n", "        )\n", "    else:\n", "        raise ValueError(f\"Dialect not yet supported for instructions translation. Please add an instructions_to_{dialect} function in utils/dialects.py\")\n", "else:\n", "    print(\"No instructions column in the dataframe\")\n", "    \n", "if \"full_instructions\" in df.columns:\n", "    if dialect == \"sqlite\":\n", "        df['full_instructions'] = df['full_instructions'].fillna(\"\")\n", "        df[\"full_instructions\"] = df.progress_apply(\n", "            lambda x: instructions_to_sqlite(x[\"full_instructions\"]), axis=1\n", "        )\n", "    elif dialect == \"tsql\":\n", "        df['full_instructions'] = df['full_instructions'].fillna(\"\")\n", "        df[\"full_instructions\"] = df.progress_apply(\n", "            lambda x: instructions_to_tsql(x[\"full_instructions\"]), axis=1\n", "        )\n", "    elif dialect == \"mysql\":\n", "        df['full_instructions'] = df['full_instructions'].fillna(\"\")\n", "        df[\"full_instructions\"] = df.progress_apply(\n", "            lambda x: instructions_to_mysql(x[\"full_instructions\"]), axis=1\n", "        )\n", "    else:\n", "        raise ValueError(f\"Dialect not yet supported for instructions translation. Please add an instructions_to_{dialect} function in utils/dialects.py\")\n", "else:\n", "    print(\"No full_instructions column in the dataframe\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"inspectinstr\"></a>\n", "### Inspect translated instructions column"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Show all unique values in the instructions column\n", "if \"instructions\" in df.columns:\n", "    instructions = df[\"instructions\"].unique()\n", "    print(\"Instructions in the dataset:\")\n", "    for i in instructions:\n", "        print(\"-\", i)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Show all unique values in the full_instructions column\n", "if \"full_instructions\" in df.columns:\n", "    full_instructions = df[\"full_instructions\"].unique()\n", "    print(\"Full instructions in the dataset:\")\n", "    for i in full_instructions:\n", "        print(i, \"\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Update the csv file with the new translated instructions\n", "df.to_csv(csv_file, index=False)"]}], "metadata": {"kernelspec": {"display_name": "defog", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}