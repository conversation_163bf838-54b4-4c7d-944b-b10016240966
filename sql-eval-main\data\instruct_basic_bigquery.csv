db_name,db_type,query_category,query,question
broker,bigquery,basic_join_date_group_order_limit,"SELECT c.sbCustCountry, COUNT(t.sbTxId) AS num_transactions, SUM(t.sbTxAmount) AS total_amount FROM broker.sbCustomer AS c JOIN broker.sbTransaction AS t ON c.sbCustId = t.sbTxCustId WHERE t.sbTxDateTime >= CURRENT_DATE - INTERVAL '30' DAY GROUP BY c.sbCustCountry ORDER BY total_amount DESC NULLS FIRST LIMIT 5;","What are the top 5 countries by total transaction amount in the past 30 days, inclusive of 30 days ago? Return the country name, number of transactions and total transaction amount."
broker,bigquery,basic_join_date_group_order_limit,"SELECT t.sbTxType, COUNT(DISTINCT t.sbTxCustId) AS num_customers, AVG(t.sbTxShares) AS avg_shares FROM broker.sbTransaction AS t WHERE t.sbTxDateTime BETWEEN '2023-01-01' AND '2023-03-31 23:59:59' GROUP BY t.sbTxType ORDER BY num_customers DESC NULLS FIRST LIMIT 3;","How many distinct customers made each type of transaction between Jan 1, 2023 and Mar 31, 2023 (inclusive of start and end dates)? Return the transaction type, number of distinct customers and average number of shares, for the top 3 transaction types by number of customers."
broker,bigquery,basic_join_group_order_limit,"SELECT tk.sbTickerSymbol, COUNT(tx.sbTxId) AS num_transactions, SUM(tx.sbTxAmount) AS total_amount FROM broker.sbTicker AS tk JOIN broker.sbTransaction AS tx ON tk.sbTickerId = tx.sbTxTickerId GROUP BY tk.sbTickerSymbol ORDER BY total_amount DESC NULLS FIRST LIMIT 10;","What are the top 10 ticker symbols by total transaction amount? Return the ticker symbol, number of transactions and total transaction amount."
broker,bigquery,basic_join_group_order_limit,"SELECT c.sbCustState, t.sbTickerType, COUNT(*) AS num_transactions FROM broker.sbTransaction AS tx JOIN broker.sbCustomer AS c ON tx.sbTxCustId = c.sbCustId JOIN broker.sbTicker AS t ON tx.sbTxTickerId = t.sbTickerId GROUP BY c.sbCustState, t.sbTickerType ORDER BY num_transactions DESC NULLS FIRST LIMIT 5;","What are the top 5 combinations of customer state and ticker type by number of transactions? Return the customer state, ticker type and number of transactions."
broker,bigquery,basic_join_distinct,SELECT DISTINCT c.sbCustId FROM broker.sbCustomer AS c JOIN broker.sbTransaction AS t ON c.sbCustId = t.sbTxCustId WHERE t.sbTxType = 'buy';,Return the distinct list of customer IDs who have made a 'buy' transaction.
broker,bigquery,basic_join_distinct,SELECT DISTINCT tk.sbTickerId FROM broker.sbTicker AS tk JOIN broker.sbDailyPrice AS dp ON tk.sbTickerId = dp.sbDpTickerId WHERE dp.sbDpDate >= '2023-04-01';,"Return the distinct list of ticker IDs that have daily price records on or after Apr 1, 2023."
broker,bigquery,basic_group_order_limit,"SELECT sbTxStatus, COUNT(*) AS num_transactions FROM broker.sbTransaction GROUP BY sbTxStatus ORDER BY num_transactions DESC NULLS FIRST LIMIT 3;",What are the top 3 transaction statuses by number of transactions? Return the status and number of transactions.
broker,bigquery,basic_group_order_limit,"SELECT sbCustCountry, COUNT(*) AS num_customers FROM broker.sbCustomer GROUP BY sbCustCountry ORDER BY num_customers DESC NULLS FIRST LIMIT 5;",What are the top 5 countries by number of customers? Return the country name and number of customers.
broker,bigquery,basic_left_join,"SELECT c.sbCustId, c.sbCustName FROM broker.sbCustomer AS c LEFT JOIN broker.sbTransaction AS t ON c.sbCustId = t.sbTxCustId WHERE t.sbTxCustId IS NULL;",Return the customer ID and name of customers who have not made any transactions.
broker,bigquery,basic_left_join,"SELECT tk.sbTickerId, tk.sbTickerSymbol FROM broker.sbTicker AS tk LEFT JOIN broker.sbDailyPrice AS dp ON tk.sbTickerId = dp.sbDpTickerId WHERE dp.sbDpTickerId IS NULL;",Return the ticker ID and symbol of tickers that do not have any daily price records.
car_dealership,bigquery,basic_join_date_group_order_limit,"SELECT c.first_name, c.last_name, COUNT(s.id) AS total_sales, SUM(s.sale_price) AS total_revenue FROM car_dealership.sales AS s JOIN car_dealership.salespersons AS c ON s.salesperson_id = c.id WHERE s.sale_date >= CURRENT_DATE - INTERVAL '3' MONTH GROUP BY c.first_name, c.last_name ORDER BY total_revenue DESC NULLS FIRST LIMIT 3;","Who were the top 3 sales representatives by total revenue in the past 3 months, inclusive of today's date? Return their first name, last name, total number of sales and total revenue. Note that revenue refers to the sum of sale_price in the sales table."
car_dealership,bigquery,basic_join_date_group_order_limit,"SELECT sp.first_name, sp.last_name, COUNT(s.id) AS total_sales, SUM(s.sale_price) AS total_revenue FROM car_dealership.sales AS s JOIN car_dealership.salespersons AS sp ON s.salesperson_id = sp.id WHERE s.sale_date >= CURRENT_DATE - INTERVAL '30' DAY GROUP BY sp.first_name, sp.last_name, sp.id ORDER BY total_sales DESC NULLS FIRST LIMIT 5;","Return the top 5 salespersons by number of sales in the past 30 days? Return their first and last name, total sales count and total revenue amount."
car_dealership,bigquery,basic_join_group_order_limit,"SELECT c.state, COUNT(DISTINCT s.customer_id) AS unique_customers, SUM(s.sale_price) AS total_revenue FROM car_dealership.sales AS s JOIN car_dealership.customers AS c ON s.customer_id = c.id GROUP BY c.state ORDER BY total_revenue DESC NULLS FIRST LIMIT 5;","Return the top 5 states by total revenue, showing the number of unique customers and total revenue (based on sale price) for each state."
car_dealership,bigquery,basic_join_group_order_limit,"SELECT c.make, c.model, COUNT(s.id) AS total_sales, SUM(s.sale_price) AS total_revenue FROM car_dealership.sales AS s JOIN car_dealership.cars AS c ON s.car_id = c.id GROUP BY c.make, c.model ORDER BY total_revenue DESC NULLS FIRST LIMIT 5;","What are the top 5 best selling car models by total revenue? Return the make, model, total number of sales and total revenue."
car_dealership,bigquery,basic_join_distinct,SELECT DISTINCT c.id AS customer_id FROM car_dealership.customers AS c JOIN car_dealership.sales AS s ON c.id = s.customer_id;,"Return the distinct list of customer IDs that have made a purchase, based on joining the customers and sales tables."
car_dealership,bigquery,basic_join_distinct,SELECT DISTINCT s.id AS salesperson_id FROM car_dealership.salespersons AS s JOIN car_dealership.sales AS sa ON s.id = sa.salesperson_id JOIN car_dealership.payments_received AS p ON sa.id = p.sale_id WHERE p.payment_method = 'cash';,"Return the distinct list of salesperson IDs that have received a cash payment, based on joining the salespersons, sales and payments_received tables."
car_dealership,bigquery,basic_group_order_limit,"SELECT payment_method, COUNT(*) AS total_payments, SUM(payment_amount) AS total_amount FROM car_dealership.payments_received GROUP BY payment_method ORDER BY total_amount DESC NULLS FIRST LIMIT 3;","What are the top 3 payment methods by total payment amount received? Return the payment method, total number of payments and total amount."
car_dealership,bigquery,basic_group_order_limit,"SELECT state, COUNT(*) AS total_signups FROM car_dealership.customers GROUP BY state ORDER BY total_signups DESC NULLS FIRST LIMIT 2;","What are the total number of customer signups for the top 2 states? Return the state and total signups, starting from the top."
car_dealership,bigquery,basic_left_join,"SELECT c.id AS car_id, c.make, c.model, c.year FROM car_dealership.cars AS c LEFT JOIN car_dealership.sales AS s ON c.id = s.car_id WHERE s.car_id IS NULL;","Return the car ID, make, model and year for cars that have no sales records, by doing a left join from the cars to sales table."
car_dealership,bigquery,basic_left_join,"SELECT s.id AS salesperson_id, s.first_name, s.last_name FROM car_dealership.salespersons AS s LEFT JOIN car_dealership.sales AS sa ON s.id = sa.salesperson_id WHERE sa.salesperson_id IS NULL;","Return the salesperson ID, first name and last name for salespersons that have no sales records, by doing a left join from the salespersons to sales table."
derm_treatment,bigquery,basic_join_date_group_order_limit,"SELECT d.specialty, COUNT(*) AS num_treatments, SUM(t.tot_drug_amt) AS total_drug_amt FROM derm_treatment.treatments AS t JOIN derm_treatment.doctors AS d ON t.doc_id = d.doc_id WHERE t.start_dt >= TIMESTAMP_TRUNC(CURRENT_DATE - INTERVAL '6' MONTH, MONTH) GROUP BY d.specialty ORDER BY total_drug_amt DESC NULLS FIRST LIMIT 3;","What are the top 3 doctor specialties by total drug amount prescribed for treatments started in the past 6 calendar months? Return the specialty, number of treatments, and total drug amount."
derm_treatment,bigquery,basic_join_date_group_order_limit,"SELECT p.ins_type, COUNT(DISTINCT t.patient_id) AS num_patients, AVG(o.day100_pasi_score) AS avg_pasi_score FROM derm_treatment.treatments AS t JOIN derm_treatment.patients AS p ON t.patient_id = p.patient_id JOIN derm_treatment.outcomes AS o ON t.treatment_id = o.treatment_id WHERE t.end_dt BETWEEN '2022-01-01' AND '2022-12-31' GROUP BY p.ins_type ORDER BY avg_pasi_score NULLS LAST LIMIT 5;","For treatments that ended in the year 2022 (from Jan 1st to Dec 31st inclusive), what is the average PASI score at day 100 and number of distinct patients per insurance type? Return the top 5 insurance types sorted by lowest average PASI score first."
derm_treatment,bigquery,basic_join_group_order_limit,"SELECT d.drug_name, COUNT(*) AS num_treatments, AVG(t.tot_drug_amt) AS avg_drug_amt FROM derm_treatment.treatments AS t JOIN derm_treatment.drugs AS d ON t.drug_id = d.drug_id GROUP BY d.drug_name ORDER BY num_treatments DESC NULLS FIRST, avg_drug_amt DESC NULLS FIRST LIMIT 5;","What are the top 5 drugs by number of treatments and average drug amount per treatment? Return the drug name, number of treatments, and average drug amount."
derm_treatment,bigquery,basic_join_group_order_limit,"SELECT di.diag_name, COUNT(DISTINCT t.patient_id) AS num_patients, MAX(o.day100_itch_vas) AS max_itch_score FROM derm_treatment.treatments AS t JOIN derm_treatment.diagnoses AS di ON t.diag_id = di.diag_id JOIN derm_treatment.outcomes AS o ON t.treatment_id = o.treatment_id GROUP BY di.diag_name ORDER BY max_itch_score DESC NULLS FIRST, num_patients DESC NULLS FIRST LIMIT 3;","What are the top 3 diagnoses by maximum itch VAS score at day 100 and number of distinct patients? Return the diagnosis name, number of patients, and maximum itch score."
derm_treatment,bigquery,basic_join_distinct,"SELECT DISTINCT d.doc_id, d.first_name, d.last_name FROM derm_treatment.treatments AS t JOIN derm_treatment.doctors AS d ON t.doc_id = d.doc_id;","Return the distinct list of doctor IDs, first names and last names that have prescribed treatments."
derm_treatment,bigquery,basic_join_distinct,"SELECT DISTINCT p.patient_id, p.first_name, p.last_name FROM derm_treatment.outcomes AS o JOIN derm_treatment.treatments AS t ON o.treatment_id = t.treatment_id JOIN derm_treatment.patients AS p ON t.patient_id = p.patient_id;","Return the distinct list of patient IDs, first names and last names that have outcome assessments."
derm_treatment,bigquery,basic_group_order_limit,"SELECT ins_type, AVG(height_cm) AS avg_height, AVG(weight_kg) AS avg_weight FROM derm_treatment.patients GROUP BY ins_type ORDER BY avg_height DESC NULLS FIRST LIMIT 3;","What are the top 3 insurance types by average patient height in cm? Return the insurance type, average height and average weight."
derm_treatment,bigquery,basic_group_order_limit,"SELECT specialty, COUNT(*) AS num_doctors FROM derm_treatment.doctors GROUP BY specialty ORDER BY num_doctors DESC NULLS FIRST LIMIT 2;",What are the top 2 specialties by number of doctors? Return the specialty and number of doctors.
derm_treatment,bigquery,basic_left_join,"SELECT p.patient_id, p.first_name, p.last_name FROM derm_treatment.patients AS p LEFT JOIN derm_treatment.treatments AS t ON p.patient_id = t.patient_id WHERE t.patient_id IS NULL;","Return the patient IDs, first names and last names of patients who have not received any treatments."
derm_treatment,bigquery,basic_left_join,"SELECT d.drug_id, d.drug_name FROM derm_treatment.drugs AS d LEFT JOIN derm_treatment.treatments AS t ON d.drug_id = t.drug_id WHERE t.drug_id IS NULL;",Return the drug IDs and names of drugs that have not been used in any treatments.
ewallet,bigquery,basic_join_date_group_order_limit,"SELECT m.name AS merchant_name, COUNT(t.txid) AS total_transactions, SUM(t.amount) AS total_amount FROM ewallet.merchants AS m JOIN ewallet.wallet_transactions_daily AS t ON m.mid = t.receiver_id WHERE t.receiver_type = 1 AND t.created_at >= CURRENT_DATE - INTERVAL '150' DAY GROUP BY merchant_name ORDER BY total_amount DESC NULLS FIRST LIMIT 2;","Who are the top 2 merchants (receiver type 1) by total transaction amount in the past 150 days (inclusive of 150 days ago)? Return the merchant name, total number of transactions, and total transaction amount."
ewallet,bigquery,basic_join_date_group_order_limit,"SELECT TIMESTAMP_TRUNC(t.created_at, MONTH) AS MONTH, COUNT(DISTINCT t.sender_id) AS active_users FROM ewallet.wallet_transactions_daily AS t JOIN ewallet.users AS u ON t.sender_id = u.uid WHERE t.sender_type = 0 AND t.status = 'success' AND u.status = 'active' AND t.created_at >= '2023-01-01' AND t.created_at < '2024-01-01' GROUP BY MONTH ORDER BY MONTH NULLS LAST;","How many distinct active users sent money per month in 2023? Return the number of active users per month (as a date), starting from the earliest date. Do not include merchants in the query. Only include successful transactions."
ewallet,bigquery,basic_join_group_order_limit,"SELECT c.code AS coupon_code, COUNT(t.txid) AS redemption_count, SUM(t.amount) AS total_discount FROM ewallet.coupons AS c JOIN ewallet.wallet_transactions_daily AS t ON c.cid = t.coupon_id GROUP BY coupon_code ORDER BY redemption_count DESC NULLS FIRST LIMIT 3;","What are the top 3 most frequently used coupon codes? Return the coupon code, total number of redemptions, and total amount redeemed."
ewallet,bigquery,basic_join_group_order_limit,"SELECT u.country, COUNT(DISTINCT t.sender_id) AS user_count, SUM(t.amount) AS total_amount FROM ewallet.users AS u JOIN ewallet.wallet_transactions_daily AS t ON u.uid = t.sender_id WHERE t.sender_type = 0 GROUP BY u.country ORDER BY total_amount DESC NULLS FIRST LIMIT 5;","Which are the top 5 countries by total transaction amount sent by users, sender_type = 0? Return the country, number of distinct users who sent, and total transaction amount."
ewallet,bigquery,basic_join_distinct,SELECT DISTINCT m.mid AS merchant_id FROM ewallet.merchants AS m JOIN ewallet.wallet_transactions_daily AS t ON m.mid = t.receiver_id WHERE t.receiver_type = 1;,"Return the distinct list of merchant IDs that have received money from a transaction. Consider all transaction types in the results you return, but only include the merchant ids in your final answer."
ewallet,bigquery,basic_join_distinct,SELECT DISTINCT user_id FROM ewallet.notifications WHERE type = 'transaction';,Return the distinct list of user IDs who have received transaction notifications.
ewallet,bigquery,basic_group_order_limit,"SELECT status, COUNT(*) AS COUNT FROM ewallet.wallet_transactions_daily GROUP BY status ORDER BY COUNT DESC NULLS FIRST LIMIT 3;",What are the top 3 most common transaction statuses and their respective counts?
ewallet,bigquery,basic_group_order_limit,"SELECT device_type, COUNT(*) AS COUNT FROM ewallet.user_sessions GROUP BY device_type ORDER BY COUNT DESC NULLS FIRST LIMIT 2;",What are the top 2 most frequently used device types for user sessions and their respective counts?
ewallet,bigquery,basic_left_join,"SELECT u.uid, u.username FROM ewallet.users AS u LEFT JOIN ewallet.notifications AS n ON u.uid = n.user_id WHERE n.id IS NULL;",Return users (user ID and username) who have not received any notifications
ewallet,bigquery,basic_left_join,"SELECT m.mid AS merchant_id, m.name AS merchant_name FROM ewallet.merchants AS m LEFT JOIN ewallet.coupons AS c ON m.mid = c.merchant_id WHERE c.cid IS NULL;",Return merchants (merchant ID and name) who have not issued any coupons.
