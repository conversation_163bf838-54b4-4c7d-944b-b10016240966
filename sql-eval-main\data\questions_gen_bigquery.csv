db_name,db_type,query_category,query,question,instructions
academic,bigquery,group_by,"SELECT author.name FROM academic.author WHERE author.aid IN (SELECT da1.aid FROM academic.domain_author da1 JOIN academic.domain d1 ON da1.did = d1.did JOIN academic.domain_author da2 ON da1.aid = da2.aid JOIN academic.domain d2 ON da2.did = d2.did WHERE d1.name = 'Machine Learning' AND d2.name = 'Data Science');SELECT DISTINCT author.aid FROM academic.author WHERE author.aid IN (SELECT domain_author.aid FROM academic.domain_author WHERE domain_author.did IN (SELECT domain.did FROM academic.domain WHERE domain.name IN ('Machine Learning', 'Data Science')) GROUP BY domain_author.aid HAVING COUNT(DISTINCT domain_author.did) = 2);SELECT author.name, author.aid FROM academic.author WHERE author.aid IN (SELECT da1.aid FROM academic.domain_author da1 JOIN academic.domain d1 ON da1.did = d1.did WHERE d1.name = 'Machine Learning') AND author.aid IN (SELECT da2.aid FROM academic.domain_author da2 JOIN academic.domain d2 ON da2.did = d2.did WHERE d2.name = 'Data Science');","Which authors have written publications in both the domain ""Machine Learning"" and the domain ""Data Science""?",
academic,bigquery,group_by,"SELECT author.name, SUM(publication.citation_num) AS total_citations FROM academic.author JOIN academic.writes ON author.aid = writes.aid JOIN academic.publication ON writes.pid = publication.pid GROUP BY author.name ORDER BY total_citations DESC;SELECT author.aid, SUM(publication.citation_num) AS total_citations FROM academic.author JOIN academic.writes ON author.aid = writes.aid JOIN academic.publication ON writes.pid = publication.pid GROUP BY author.aid ORDER BY total_citations DESC;SELECT author.name, author.aid, SUM(publication.citation_num) AS total_citations FROM academic.author JOIN academic.writes ON author.aid = writes.aid JOIN academic.publication ON writes.pid = publication.pid GROUP BY author.name, author.aid ORDER BY total_citations DESC;SELECT a.aid, COUNT(c.cited) AS total_citations FROM academic.author AS a JOIN academic.writes AS w ON a.aid = w.aid JOIN academic.publication AS p ON w.pid = p.pid JOIN academic.cite AS c ON p.pid = c.cited GROUP BY a.aid ORDER BY total_citations DESC NULLS FIRST;SELECT a.name, COUNT(c.cited) AS total_citations FROM academic.author AS a JOIN academic.writes AS w ON a.aid = w.aid JOIN academic.publication AS p ON w.pid = p.pid JOIN academic.cite AS c ON p.pid = c.cited GROUP BY a.name ORDER BY total_citations DESC NULLS FIRST;SELECT a.aid, a.name, COUNT(c.cited) AS total_citations FROM academic.author AS a JOIN academic.writes AS w ON a.aid = w.aid JOIN academic.publication AS p ON w.pid = p.pid JOIN academic.cite AS c ON p.pid = c.cited GROUP BY a.aid, a.name ORDER BY total_citations DESC NULLS FIRST;",What is the total number of citations received by each author?,
academic,bigquery,group_by,"SELECT publication.year, COUNT(DISTINCT publication.pid) AS total_publications FROM academic.publication GROUP BY publication.year ORDER BY publication.year NULLS LAST;",What is the total number of publications published in each year?,
academic,bigquery,group_by,"SELECT domain.name, AVG(publication.reference_num) AS average_references FROM academic.domain_publication JOIN academic.publication ON domain_publication.pid = publication.pid JOIN academic.domain ON domain.did = domain_publication.did GROUP BY domain.name;SELECT domain.did, AVG(publication.reference_num) AS average_references FROM academic.domain_publication JOIN academic.publication ON domain_publication.pid = publication.pid JOIN academic.domain ON domain.did = domain_publication.did GROUP BY domain.did;SELECT domain.name, domain.did, AVG(publication.reference_num) AS average_references FROM academic.domain_publication JOIN academic.publication ON domain_publication.pid = publication.pid JOIN academic.domain ON domain.did = domain_publication.did GROUP BY domain.name, domain.did;",What is the average number of references cited by publications in each domain name?,
academic,bigquery,group_by,"SELECT publication.year, AVG(publication.citation_num) AS average_citations FROM academic.publication GROUP BY publication.year ORDER BY publication.year NULLS LAST;",What is the average number of citations received by publications in each year?,
academic,bigquery,order_by,SELECT publication.title FROM academic.publication ORDER BY publication.citation_num DESC LIMIT 1;,What is the title of the publication that has received the highest number of citations?,
academic,bigquery,order_by,"SELECT d.name, COUNT(DISTINCT a.aid) AS author_count FROM academic.author AS a JOIN academic.domain_author AS da ON a.aid = da.aid JOIN academic.domain AS d ON da.did = d.did GROUP BY d.name ORDER BY author_count DESC NULLS FIRST LIMIT 5;SELECT d.did, COUNT(DISTINCT a.aid) AS author_count FROM academic.author AS a JOIN academic.domain_author AS da ON a.aid = da.aid JOIN academic.domain AS d ON da.did = d.did GROUP BY d.did ORDER BY author_count DESC NULLS FIRST LIMIT 5;SELECT d.name, d.did, COUNT(DISTINCT a.aid) AS author_count FROM academic.author AS a JOIN academic.domain_author AS da ON a.aid = da.aid JOIN academic.domain AS d ON da.did = d.did GROUP BY d.name, d.did ORDER BY author_count DESC NULLS FIRST LIMIT 5;",What are the top 5 domains with the highest number of authors associated with them?,
academic,bigquery,order_by,SELECT publication.title FROM academic.publication ORDER BY publication.reference_num DESC NULLS FIRST LIMIT 3;,"What are the top 3 titles of the publications that have the highest number of references cited, ordered by the number of references cited in descending order?",
academic,bigquery,order_by,"SELECT publication.title, publication.citation_num FROM academic.publication ORDER BY publication.citation_num DESC NULLS FIRST LIMIT 3;SELECT publication.pid, publication.citation_num FROM academic.publication ORDER BY publication.citation_num DESC NULLS FIRST LIMIT 3;SELECT publication.title, publication.pid, publication.citation_num FROM academic.publication ORDER BY publication.citation_num DESC NULLS FIRST LIMIT 3;",What are the top 3 publications with the highest number of citations?,
academic,bigquery,order_by,SELECT DISTINCT publication.title FROM academic.publication ORDER BY publication.title ASC NULLS LAST;,What are the titles of all publications ordered alphabetically?,
academic,bigquery,ratio,"SELECT CAST(COUNT(DISTINCT publication.pid) AS FLOAT64) / NULLIF(COUNT(DISTINCT author.aid), 0) AS publication_to_author_ratio FROM academic.publication, academic.author;",What is the ratio of publications to authors in the database?,
academic,bigquery,ratio,"SELECT CAST(COUNT(DISTINCT CASE WHEN NOT cid IS NULL THEN pid END) AS FLOAT64) / NULLIF(COUNT(DISTINCT CASE WHEN NOT jid IS NULL THEN pid END), 0) AS ratio FROM academic.publication;",What is the ratio of publications presented in conferences to publications published in journals?,
academic,bigquery,ratio,"SELECT domain_publication.did, CAST(COUNT(DISTINCT domain_publication.pid) AS FLOAT64) / NULLIF(COUNT(DISTINCT domain_keyword.kid), 0) AS publication_to_keyword_ratio FROM academic.domain_publication LEFT JOIN academic.domain_keyword ON domain_publication.did = domain_keyword.did GROUP BY domain_publication.did ORDER BY publication_to_keyword_ratio DESC;SELECT domain_publication.did, CAST(COUNT(DISTINCT domain_publication.pid) AS FLOAT64) / NULLIF(COUNT(DISTINCT domain_keyword.kid), 0) AS publication_to_keyword_ratio FROM academic.domain_keyword LEFT JOIN academic.domain_publication ON domain_publication.did = domain_keyword.did GROUP BY domain_publication.did ORDER BY publication_to_keyword_ratio DESC;SELECT d.did, COALESCE(CAST(COUNT(DISTINCT dp.pid) AS FLOAT64) / NULLIF(COUNT(DISTINCT dk.kid), 0), 0) AS publication_to_keyword_ratio FROM academic.domain AS d LEFT JOIN academic.domain_publication AS dp ON d.did = dp.did LEFT JOIN academic.domain_keyword AS dk ON d.did = dk.did GROUP BY d.did ORDER BY publication_to_keyword_ratio DESC;",What is the ratio of the total number of publications to the total number of keywords within each domain ID? Show all domain IDs.,
academic,bigquery,ratio,"SELECT publication.year, COUNT(DISTINCT publication.pid) AS num_publications, COUNT(DISTINCT publication.jid) AS num_journals, CAST(COUNT(DISTINCT publication.pid) AS FLOAT64) / NULLIF(COUNT(DISTINCT publication.jid), 0) AS ratio FROM academic.publication GROUP BY publication.year ORDER BY publication.year NULLS LAST;",How does the ratio of publications to journals change over the years? Return the annual numbers of publications and journals as well.,
academic,bigquery,ratio,"SELECT organization.continent, CAST(COUNT(DISTINCT author.aid) AS FLOAT64) / NULLIF(COUNT(DISTINCT organization.oid), 0) AS ratio FROM academic.organization LEFT JOIN academic.author ON author.oid = organization.oid GROUP BY organization.continent ORDER BY ratio DESC;WITH author_counts AS (SELECT o.continent, COUNT(DISTINCT a.aid) AS author_count FROM academic.author AS a JOIN academic.organization AS o ON a.oid = o.oid GROUP BY o.continent), organization_counts AS (SELECT o.continent, COUNT(DISTINCT o.oid) AS organization_count FROM academic.organization AS o GROUP BY o.continent) SELECT ac.continent, CAST(ac.author_count AS FLOAT64) / NULLIF(oc.organization_count, 0) AS author_to_organization_ratio FROM author_counts AS ac JOIN organization_counts AS oc ON ac.continent = oc.continent ORDER BY author_to_organization_ratio DESC;",How does the ratio of authors to organizations differ by continent?,
academic,bigquery,table_join,"SELECT author.name, COUNT(publication.pid) AS publication_count FROM academic.writes JOIN academic.author ON writes.aid = author.aid JOIN academic.publication ON writes.pid = publication.pid WHERE publication.year = 2021 GROUP BY author.name ORDER BY publication_count DESC LIMIT 1;SELECT author.aid, COUNT(publication.pid) AS publication_count FROM academic.writes JOIN academic.author ON writes.aid = author.aid JOIN academic.publication ON writes.pid = publication.pid WHERE publication.year = 2021 GROUP BY author.aid ORDER BY publication_count DESC LIMIT 1;SELECT author.name, author.aid, COUNT(publication.pid) AS publication_count FROM academic.writes JOIN academic.author ON writes.aid = author.aid JOIN academic.publication ON writes.pid = publication.pid WHERE publication.year = 2021 GROUP BY author.name, author.aid ORDER BY publication_count DESC LIMIT 1;",Which author had the most publications in the year 2021 and how many publications did he/she have that year?,
academic,bigquery,table_join,"SELECT conference.name, COUNT(publication.pid) AS total_publications FROM academic.publication JOIN academic.conference ON publication.cid = conference.cid GROUP BY conference.name ORDER BY total_publications DESC NULLS FIRST;SELECT conference.cid, COUNT(publication.pid) AS total_publications FROM academic.publication JOIN academic.conference ON publication.cid = conference.cid GROUP BY conference.cid ORDER BY total_publications DESC NULLS FIRST;SELECT conference.name, conference.cid, COUNT(publication.pid) AS total_publications FROM academic.publication JOIN academic.conference ON publication.cid = conference.cid GROUP BY conference.name, conference.cid ORDER BY total_publications DESC NULLS FIRST;",What is the total number of publications presented in each conference?,
academic,bigquery,table_join,"SELECT journal.name, COUNT(publication.pid) AS total_publications FROM academic.publication JOIN academic.journal ON publication.jid = journal.jid GROUP BY journal.name ORDER BY total_publications DESC;SELECT journal.jid, COUNT(publication.pid) AS total_publications FROM academic.publication JOIN academic.journal ON publication.jid = journal.jid GROUP BY journal.jid ORDER BY total_publications DESC;SELECT journal.name, journal.jid, COUNT(publication.pid) AS total_publications FROM academic.publication JOIN academic.journal ON publication.jid = journal.jid GROUP BY journal.name, journal.jid ORDER BY total_publications DESC;SELECT journal.name, COUNT(publication.pid) AS total_publications FROM academic.journal LEFT JOIN academic.publication ON journal.jid = publication.jid GROUP BY journal.name ORDER BY total_publications DESC;SELECT journal.jid, COUNT(publication.pid) AS total_publications FROM academic.journal LEFT JOIN academic.publication ON journal.jid = publication.jid GROUP BY journal.jid ORDER BY total_publications DESC;SELECT journal.name, journal.jid, COUNT(publication.pid) AS total_publications FROM academic.journal LEFT JOIN academic.publication ON journal.jid = publication.jid GROUP BY journal.name, journal.jid ORDER BY total_publications DESC;","What is the total number of publications in each journal, ordered by the number of publications in descending order?",
academic,bigquery,table_join,"SELECT conference.name, COUNT(publication.pid) AS num_publications FROM academic.publication JOIN academic.conference ON publication.cid = conference.cid GROUP BY conference.name, conference.cid ORDER BY num_publications DESC;","How many publications were presented at each conference, ordered by the number of publications in descending order? Give the names of the conferences and their corresponding number of publications.",
academic,bigquery,table_join,SELECT COUNT(DISTINCT publication.pid) FROM academic.publication JOIN academic.journal ON publication.jid = journal.jid WHERE LOWER(journal.name) LIKE LOWER('J%');,"How many publications were published in journals whose names start with the letter ""J""?",
academic,bigquery,instruct,"SELECT DISTINCT organization.name FROM academic.organization JOIN academic.author ON organization.oid = author.oid JOIN academic.writes ON author.aid = writes.aid JOIN academic.domain_publication ON writes.pid = domain_publication.pid JOIN academic.domain ON domain_publication.did = domain.did WHERE domain.name = 'Machine Learning';SELECT DISTINCT organization.oid FROM academic.organization JOIN academic.author ON organization.oid = author.oid JOIN academic.writes ON author.aid = writes.aid JOIN academic.domain_publication ON writes.pid = domain_publication.pid JOIN academic.domain ON domain_publication.did = domain.did WHERE domain.name = 'Machine Learning';SELECT DISTINCT organization.name, organization.oid FROM academic.organization JOIN academic.author ON organization.oid = author.oid JOIN academic.writes ON author.aid = writes.aid JOIN academic.domain_publication ON writes.pid = domain_publication.pid JOIN academic.domain ON domain_publication.did = domain.did WHERE domain.name = 'Machine Learning';","Which organizations have authors who have written publications in the domain ""Machine Learning""?",Always filter names using an exact match
academic,bigquery,instruct,"SELECT DISTINCT a2.name FROM academic.author AS a1 JOIN academic.domain_author AS da1 ON a1.aid = da1.aid JOIN academic.domain_author AS da2 ON da1.did = da2.did JOIN academic.author AS a2 ON da2.aid = a2.aid WHERE LOWER(LOWER(a1.name)) LIKE LOWER('%martin%');SELECT DISTINCT a2.aid FROM academic.author AS a1 JOIN academic.domain_author AS da1 ON a1.aid = da1.aid JOIN academic.domain_author AS da2 ON da1.did = da2.did JOIN academic.author AS a2 ON da2.aid = a2.aid WHERE LOWER(LOWER(a1.name)) LIKE LOWER('%martin%');SELECT DISTINCT a2.name, a2.aid FROM academic.author AS a1 JOIN academic.domain_author AS da1 ON a1.aid = da1.aid JOIN academic.domain_author AS da2 ON da1.did = da2.did JOIN academic.author AS a2 ON da2.aid = a2.aid WHERE LOWER(LOWER(a1.name)) LIKE LOWER('%martin%');",Which authors belong to the same domain as Martin?,Always filter names using LIKE with percent sign wildcards
academic,bigquery,instruct,"SELECT DISTINCT name FROM academic.author WHERE oid IS NULL;SELECT DISTINCT aid FROM academic.author WHERE oid IS NULL;SELECT DISTINCT name, aid FROM academic.author WHERE oid IS NULL;",Which authors are not part of any organization?,Always filter names using LIKE
academic,bigquery,instruct,"SELECT DISTINCT publication.title FROM academic.domain JOIN academic.domain_author ON domain.did = domain_author.did JOIN academic.writes ON domain_author.aid = writes.aid JOIN academic.publication ON writes.pid = publication.pid JOIN academic.domain_conference ON domain.did = domain_conference.did WHERE LOWER(domain.name) LIKE '%sociology%' AND publication.year = 2020 AND publication.cid = domain_conference.cid;SELECT DISTINCT publication.pid FROM academic.domain JOIN academic.domain_author ON domain.did = domain_author.did JOIN academic.writes ON domain_author.aid = writes.aid JOIN academic.publication ON writes.pid = publication.pid JOIN academic.domain_conference ON domain.did = domain_conference.did WHERE LOWER(domain.name) LIKE '%sociology%' AND publication.year = 2020 AND publication.cid = domain_conference.cid;SELECT DISTINCT publication.pid, publication.title FROM academic.domain JOIN academic.domain_author ON domain.did = domain_author.did JOIN academic.writes ON domain_author.aid = writes.aid JOIN academic.publication ON writes.pid = publication.pid JOIN academic.domain_conference ON domain.did = domain_conference.did WHERE LOWER(domain.name) LIKE '%sociology%' AND publication.year = 2020 AND publication.cid = domain_conference.cid;",What are the publications written by authors from the 'Sociology' domain and presented at conferences in the year 2020?,"To get publications written by authors from a given domain, you would need to join domain, domain_author, author to link the domain to the author first, and then join with write to link with the publication id. Finally, to see which ones were presented at conferences, you must join the domain table with the domain_conference table. You must also filter names using LIKE."
academic,bigquery,instruct,SELECT DISTINCT author.name FROM academic.author JOIN academic.writes ON author.aid = writes.aid JOIN academic.publication ON writes.pid = publication.pid JOIN academic.domain_publication ON publication.pid = domain_publication.pid JOIN academic.domain ON domain_publication.did = domain.did WHERE LOWER(domain.name) LIKE LOWER('%computer%science%');,"What are the names of the authors who have written publications in the domain ""Computer Science""?","To get publications written by authors from a given domain, you would need to join domain, domain_author, author to link the domain to the author first, and then join with write to link with the publication id. You must also filter names using LIKE."
advising,bigquery,date_functions,"SELECT TIMESTAMP_TRUNC(s.admit_term, MONTH) AS month, COUNT(*) AS total_students FROM advising.student AS s GROUP BY MONTH ORDER BY total_students DESC NULLS FIRST LIMIT 1;SELECT FORMAT_DATE('%Y-%m', DATE_TRUNC(s.admit_term, MONTH)) AS month, COUNT(*) AS total_students FROM advising.student AS s GROUP BY month ORDER BY total_students DESC NULLS FIRST LIMIT 1;",What month were most students admitted? Return the no. of students and the month as a date,
advising,bigquery,date_functions,SELECT AVG(predicted_graduation_semester - admit_term) AS average_predicted_time_to_graduation FROM advising.student;,What's the average predicted time to graduation since admission in no. of days?,
advising,bigquery,date_functions,"SELECT COUNT(*) AS num_students_graduated FROM advising.student WHERE predicted_graduation_semester >= TIMESTAMP_TRUNC(CURRENT_DATE, YEAR) - INTERVAL '10' YEAR;",How many students were predicted to graduate in the last 10 years?,
advising,bigquery,date_functions,"SELECT DATE_DIFF(CURRENT_DATE, MAX(admit_term), DAY) AS duration_since_last_admitted_student FROM advising.student;",How long has it been in days since the last admitted student? Give the answer as an integer.,
advising,bigquery,date_functions,SELECT DISTINCT co.course_id FROM advising.course_offering AS co JOIN advising.offering_instructor AS oi ON co.offering_id = oi.offering_id WHERE (co.semester = 1 OR co.semester = 2) AND co.end_time < '13:00:00' AND co.thursday IS NOT NULL;,Return the course id's that are offered in either semesters 1 or 2 and ends before 1pm and had an instructor on thursday,
advising,bigquery,group_by,"SELECT course_tags_count.course_id, SUM(course_tags_count.hilarious) AS total_hilarious FROM advising.course_tags_count GROUP BY course_tags_count.course_id;",What is the total number of students who found the instructor to be hilarious per course id?,
advising,bigquery,group_by,"SELECT i.name, AVG(c.clarity_score) FROM advising.course AS c JOIN advising.course_offering AS co ON c.course_id = co.course_id JOIN advising.offering_instructor AS oi ON co.offering_id = oi.offering_id JOIN advising.instructor AS i ON oi.instructor_id = i.instructor_id GROUP BY i.name;SELECT i.instructor_id, AVG(c.clarity_score) FROM advising.course AS c JOIN advising.course_offering AS co ON c.course_id = co.course_id JOIN advising.offering_instructor AS oi ON co.offering_id = oi.offering_id JOIN advising.instructor AS i ON oi.instructor_id = i.instructor_id GROUP BY i.instructor_id;SELECT i.name, i.instructor_id, AVG(c.clarity_score) FROM advising.course AS c JOIN advising.course_offering AS co ON c.course_id = co.course_id JOIN advising.offering_instructor AS oi ON co.offering_id = oi.offering_id JOIN advising.instructor AS i ON oi.instructor_id = i.instructor_id GROUP BY i.name, i.instructor_id;",What is the average clarity score for each instructor who taught a course?,
advising,bigquery,group_by,"SELECT course_offering.has_final_exam, COUNT(offering_id) AS num_courses FROM advising.course_offering GROUP BY course_offering.has_final_exam;SELECT COUNT(CASE WHEN co.has_final_exam THEN 1 END) AS num_with_final_exam, COUNT(CASE WHEN NOT co.has_final_exam THEN 1 END) AS num_without_final_exam FROM advising.course_offering AS co;",How many course offerings have a final exam and how many do not?,
advising,bigquery,group_by,"SELECT course.department, COUNT(DISTINCT course.course_id) AS num_courses FROM advising.course GROUP BY course.department ORDER BY num_courses DESC;",How many courses does each department offer?,
advising,bigquery,group_by,"SELECT course_offering.semester, COUNT(DISTINCT course_offering.course_id) AS num_courses FROM advising.course_offering GROUP BY course_offering.semester ORDER BY course_offering.semester NULLS LAST;SELECT semester.semester_id, COUNT(DISTINCT course_offering.course_id) AS num_courses FROM advising.semester LEFT JOIN advising.course_offering ON semester.semester_id = course_offering.semester GROUP BY semester.semester_id;",How many courses are offered for each semester id?,
advising,bigquery,order_by,"SELECT course.name, course.num_enrolled FROM advising.course ORDER BY course.num_enrolled DESC LIMIT 1;SELECT course.course_id, course.num_enrolled FROM advising.course ORDER BY course.num_enrolled DESC LIMIT 1;SELECT course.number, course.num_enrolled FROM advising.course ORDER BY course.num_enrolled DESC LIMIT 1;SELECT course.name, course.course_id, course.num_enrolled FROM advising.course ORDER BY course.num_enrolled DESC LIMIT 1;SELECT course.name, course.number, course.num_enrolled FROM advising.course ORDER BY course.num_enrolled DESC LIMIT 1;SELECT course.course_id, course.number, course.num_enrolled FROM advising.course ORDER BY course.num_enrolled DESC LIMIT 1;SELECT course.name, course.course_id, course.number, course.num_enrolled FROM advising.course ORDER BY course.num_enrolled DESC LIMIT 1;","Which course has the highest number of enrolled students, and what is the enrollment number?",
advising,bigquery,order_by,"SELECT course_tags_count.course_id, course_tags_count.participation FROM advising.course_tags_count ORDER BY course_tags_count.participation DESC;","What is the total number of students who participated actively for each course id, ordered from highest to lowest participants?",
advising,bigquery,order_by,"SELECT course.course_id, SUM(course.num_enrolled) AS total_students FROM advising.course GROUP BY course.course_id ORDER BY total_students DESC;SELECT course.name, SUM(course.num_enrolled) AS total_students FROM advising.course GROUP BY course.name ORDER BY total_students DESC;SELECT course.number, SUM(course.num_enrolled) AS total_students FROM advising.course GROUP BY course.number ORDER BY total_students DESC;SELECT course.course_id, course.name, SUM(course.num_enrolled) AS total_students FROM advising.course GROUP BY course.course_id, course.name ORDER BY total_students DESC;SELECT course.course_id, course.number, SUM(course.num_enrolled) AS total_students FROM advising.course GROUP BY course.course_id, course.number ORDER BY total_students DESC;SELECT course.name, course.number, SUM(course.num_enrolled) AS total_students FROM advising.course GROUP BY course.name, course.number ORDER BY total_students DESC;SELECT course.course_id, course.name, course.number, SUM(course.num_enrolled) AS total_students FROM advising.course GROUP BY course.course_id, course.name, course.number ORDER BY total_students DESC;","What is the total number of students enrolled in each course, ordered from highest to lowest?",
advising,bigquery,order_by,"SELECT student.student_id, student.total_credit FROM advising.student ORDER BY student.total_credit DESC;","What is the total number of credits earned by each student, ordered from highest to lowest? Give the student id and the total number of credits.",
advising,bigquery,order_by,"SELECT instructor.name, COUNT(offering_instructor.offering_id) AS num_courses FROM advising.offering_instructor JOIN advising.instructor ON offering_instructor.instructor_id = instructor.instructor_id GROUP BY instructor.name ORDER BY num_courses DESC NULLS FIRST LIMIT 1;","What is the name of the instructor who has taught the most courses, and how many courses have they taught?",
advising,bigquery,ratio,SELECT CAST(SUM(CASE WHEN c.has_exams THEN c.num_enrolled ELSE 0 END) AS FLOAT64) / SUM(CASE WHEN NOT c.has_exams THEN c.num_enrolled ELSE 0 END) AS ratio FROM advising.course AS c;,What is the ratio of the total number of students enrolled in courses with exams to the total number of students enrolled in courses without exams?,
advising,bigquery,ratio,"SELECT course_tags_count.course_id, CAST(course_tags_count.clear_grading AS FLOAT64) / NULLIF(course_tags_count.good_feedback, 0) AS ratio FROM advising.course_tags_count ORDER BY course_tags_count.course_id NULLS LAST;",What is the ratio of the number of students who found the grading criteria clear and easy to understand to the number of students who received good feedback from the instructor for each course id?,
advising,bigquery,ratio,"SELECT course_offering.semester, CAST(SUM(CASE WHEN course.has_projects THEN 1 ELSE 0 END) AS FLOAT64) / NULLIF(SUM(CASE WHEN course.has_exams THEN 1 ELSE 0 END), 0) AS ratio FROM advising.course JOIN advising.course_offering ON course.course_id = course_offering.course_id GROUP BY course_offering.semester ORDER BY course_offering.semester NULLS LAST;",What is the ratio of the number of courses with projects to the number of courses with exams in each semester id?,
advising,bigquery,ratio,"SELECT course.course_id, CAST(course.helpfulness_score AS FLOAT64) / NULLIF(course.clarity_score, 0) AS ratio FROM advising.course;",What is the ratio of helpfulness scores to clarity scores for each course ID?,
advising,bigquery,ratio,"SELECT course.course_id, CAST(course.num_enrolled AS FLOAT64) / NULLIF(course.num_reviews, 0) AS student_review_ratio FROM advising.course ORDER BY student_review_ratio NULLS LAST;SELECT course.name, CAST(course.num_enrolled AS FLOAT64) / NULLIF(course.num_reviews, 0) AS student_review_ratio FROM advising.course ORDER BY student_review_ratio NULLS LAST;SELECT course.number, CAST(course.num_enrolled AS FLOAT64) / NULLIF(course.num_reviews, 0) AS student_review_ratio FROM advising.course ORDER BY student_review_ratio NULLS LAST;SELECT course.course_id, course.name, CAST(course.num_enrolled AS FLOAT64) / NULLIF(course.num_reviews, 0) AS student_review_ratio FROM advising.course ORDER BY student_review_ratio NULLS LAST;SELECT course.course_id, course.number, CAST(course.num_enrolled AS FLOAT64) / NULLIF(course.num_reviews, 0) AS student_review_ratio FROM advising.course ORDER BY student_review_ratio NULLS LAST;SELECT course.name, course.number, CAST(course.num_enrolled AS FLOAT64) / NULLIF(course.num_reviews, 0) AS student_review_ratio FROM advising.course ORDER BY student_review_ratio NULLS LAST;SELECT course.course_id, course.name, course.number, CAST(course.num_enrolled AS FLOAT64) / NULLIF(course.num_reviews, 0) AS student_review_ratio FROM advising.course ORDER BY student_review_ratio NULLS LAST;",How does the ratio of enrolled students to the number of reviews vary across different courses?,
advising,bigquery,table_join,"SELECT DISTINCT course.name AS course_name FROM advising.student JOIN advising.student_record ON student.student_id = student_record.student_id JOIN advising.program ON student.program_id = program.program_id JOIN advising.course ON student_record.course_id = course.course_id WHERE LOWER(program.name) LIKE LOWER('%Computer Science%');SELECT DISTINCT course.course_id AS course_name FROM advising.student JOIN advising.student_record ON student.student_id = student_record.student_id JOIN advising.program ON student.program_id = program.program_id JOIN advising.course ON student_record.course_id = course.course_id WHERE LOWER(program.name) LIKE LOWER('%Computer Science%');SELECT DISTINCT course.number AS course_name FROM advising.student JOIN advising.student_record ON student.student_id = student_record.student_id JOIN advising.program ON student.program_id = program.program_id JOIN advising.course ON student_record.course_id = course.course_id WHERE LOWER(program.name) LIKE LOWER('%Computer Science%');SELECT DISTINCT course.name, course.course_id AS course_name FROM advising.student JOIN advising.student_record ON student.student_id = student_record.student_id JOIN advising.program ON student.program_id = program.program_id JOIN advising.course ON student_record.course_id = course.course_id WHERE LOWER(program.name) LIKE LOWER('%Computer Science%');SELECT DISTINCT course.name, course.number AS course_name FROM advising.student JOIN advising.student_record ON student.student_id = student_record.student_id JOIN advising.program ON student.program_id = program.program_id JOIN advising.course ON student_record.course_id = course.course_id WHERE LOWER(program.name) LIKE LOWER('%Computer Science%');SELECT DISTINCT course.course_id, course.number AS course_name FROM advising.student JOIN advising.student_record ON student.student_id = student_record.student_id JOIN advising.program ON student.program_id = program.program_id JOIN advising.course ON student_record.course_id = course.course_id WHERE LOWER(program.name) LIKE LOWER('%Computer Science%');SELECT DISTINCT course.name, course.course_id, course.number AS course_name FROM advising.student JOIN advising.student_record ON student.student_id = student_record.student_id JOIN advising.program ON student.program_id = program.program_id JOIN advising.course ON student_record.course_id = course.course_id WHERE LOWER(program.name) LIKE LOWER('%Computer Science%');",Which courses have been taken by students in the Computer Science program?,
advising,bigquery,table_join,"SELECT DISTINCT course.name FROM advising.course_offering JOIN advising.course ON course_offering.course_id = course.course_id WHERE course_offering.has_final_project = TRUE AND course_offering.has_final_exam = TRUE;SELECT DISTINCT course.course_id FROM advising.course_offering JOIN advising.course ON course_offering.course_id = course.course_id WHERE course_offering.has_final_project = TRUE AND course_offering.has_final_exam = TRUE;SELECT DISTINCT course.number FROM advising.course_offering JOIN advising.course ON course_offering.course_id = course.course_id WHERE course_offering.has_final_project = TRUE AND course_offering.has_final_exam = TRUE;SELECT DISTINCT course.name, course.course_id FROM advising.course_offering JOIN advising.course ON course_offering.course_id = course.course_id WHERE course_offering.has_final_project = TRUE AND course_offering.has_final_exam = TRUE;SELECT DISTINCT course.name, course.number FROM advising.course_offering JOIN advising.course ON course_offering.course_id = course.course_id WHERE course_offering.has_final_project = TRUE AND course_offering.has_final_exam = TRUE;SELECT DISTINCT course.course_id, course.number FROM advising.course_offering JOIN advising.course ON course_offering.course_id = course.course_id WHERE course_offering.has_final_project = TRUE AND course_offering.has_final_exam = TRUE;SELECT DISTINCT course.name, course.course_id, course.number FROM advising.course_offering JOIN advising.course ON course_offering.course_id = course.course_id WHERE course_offering.has_final_project = TRUE AND course_offering.has_final_exam = TRUE;",Which courses have a final project and a final exam?,
advising,bigquery,table_join,SELECT COUNT(DISTINCT student_record.student_id) AS total_students FROM advising.student_record JOIN advising.course_offering ON student_record.course_id = course_offering.course_id WHERE course_offering.has_final_project = TRUE OR course_offering.has_final_exam = TRUE;,What is the total number of students who have taken a course with a final project or exam?,
advising,bigquery,table_join,"SELECT program.name, SUM(student.total_credit) AS total_credits FROM advising.student JOIN advising.program ON student.program_id = program.program_id GROUP BY program.name;SELECT program.program_id, SUM(student.total_credit) AS total_credits FROM advising.student JOIN advising.program ON student.program_id = program.program_id GROUP BY program.program_id;SELECT program.name, program.program_id, SUM(student.total_credit) AS total_credits FROM advising.student JOIN advising.program ON student.program_id = program.program_id GROUP BY program.name, program.program_id;",What is the total number of credits earned by students in each program?,
advising,bigquery,table_join,"SELECT program.name, COUNT(student.student_id) AS number_of_students FROM advising.student JOIN advising.program ON student.program_id = program.program_id WHERE NOT student.declare_major IS NULL GROUP BY program.name ORDER BY number_of_students DESC NULLS FIRST;SELECT program.program_id, COUNT(student.student_id) AS number_of_students FROM advising.student JOIN advising.program ON student.program_id = program.program_id WHERE NOT student.declare_major IS NULL GROUP BY program.program_id ORDER BY number_of_students DESC NULLS FIRST;SELECT program.name, program.program_id, COUNT(student.student_id) AS number_of_students FROM advising.student JOIN advising.program ON student.program_id = program.program_id WHERE NOT student.declare_major IS NULL GROUP BY program.name, program.program_id ORDER BY number_of_students DESC NULLS FIRST;",How many students have declared a major in each program?,
advising,bigquery,instruct,"SELECT student.firstname, student.lastname FROM advising.student WHERE NOT student.minor IS NULL ORDER BY student.lastname NULLS LAST;",Which students have declared a minor program? List their firstname and lastname. Order the results by the students' last names.,"student.declare_major is null for students who have not declared their major.
student.minor is null for students who have not declared a minor program."
advising,bigquery,instruct,SELECT AVG(student.total_gpa) FROM advising.student JOIN advising.program ON student.program_id = program.program_id WHERE LOWER(program.name) = 'mathematics';,What is the average GPA of students in the program mathematics?,Match strings case-insensitively
advising,bigquery,instruct,SELECT course.name FROM advising.course WHERE LOWER(course.department) LIKE LOWER('%Computer Science%') ORDER BY course.name ASC NULLS LAST;,What are the names of all the courses offered by the department of Computer Science?,"Filter strings using LIKE.
Use the student_record table for all information relating to students' choices and their course."
advising,bigquery,instruct,"SELECT course.name, course.easiness_score FROM advising.course WHERE course.department = 'Computer Science';SELECT course.course_id, course.easiness_score FROM advising.course WHERE course.department = 'Computer Science';SELECT course.number, course.easiness_score FROM advising.course WHERE course.department = 'Computer Science';SELECT course.name, course.course_id, course.easiness_score FROM advising.course WHERE course.department = 'Computer Science';SELECT course.name, course.number, course.easiness_score FROM advising.course WHERE course.department = 'Computer Science';SELECT course.course_id, course.number, course.easiness_score FROM advising.course WHERE course.department = 'Computer Science';SELECT course.name, course.course_id, course.number, course.easiness_score FROM advising.course WHERE course.department = 'Computer Science';","What are the easiness scores for courses in the ""Computer Science"" department? Show both courses and scores.",Always filter names using exact string matching
advising,bigquery,instruct,"SELECT DISTINCT student_id FROM advising.student_record WHERE student_record.how = 'in-person' AND student_record.grade IN ('A', 'C');",Return the student IDs who have taken an in-person course and have gotten a grade of A or C.,"Always filter strings with an exact match.
When asked for specific students or courses, do not return duplicates."
atis,bigquery,date_functions,"SELECT flight.flight_number, CAST((arrival_time - departure_time) / 60 AS INT64) AS duration_minutes FROM atis.flight ORDER BY duration_minutes NULLS LAST LIMIT 1;SELECT flight.flight_id, CAST((arrival_time - departure_time) / 60 AS INT64) AS duration_minutes FROM atis.flight ORDER BY duration_minutes NULLS LAST LIMIT 1;SELECT flight.flight_number, flight.flight_id, CAST((arrival_time - departure_time) / 60 AS INT64) AS duration_minutes FROM atis.flight ORDER BY duration_minutes NULLS LAST LIMIT 1;",Which flight has the shortest duration between departure and arrival times? Convert to integer minutes.,
atis,bigquery,date_functions,SELECT AVG(TIMESTAMP_SECONDS(arrival_time) - TIMESTAMP_SECONDS(departure_time) - INTERVAL '34' MINUTE) AS average_duration FROM atis.flight;SELECT AVG(arrival_time - departure_time) / 60 - 34 AS average_duration FROM atis.flight;,"What's the average duration between departure and arrival times minus 34 minutes? Convert from UNIX to regular datetime, and return the answer in minutes",
atis,bigquery,date_functions,"SELECT month.month_name, COUNT(*) AS departure_count FROM atis.flight JOIN atis.month ON EXTRACT(MONTH FROM TIMESTAMP_SECONDS(flight.departure_time)) = month.month_number GROUP BY month.month_name, month.month_number ORDER BY month.month_number NULLS LAST;SELECT TIMESTAMP_TRUNC(TIMESTAMP_SECONDS(flight.departure_time), MONTH) AS month, COUNT(*) AS num_departures FROM atis.flight GROUP BY MONTH ORDER BY MONTH NULLS LAST;SELECT FORMAT_TIMESTAMP('%Y-%m', TIMESTAMP_TRUNC(TIMESTAMP_SECONDS(flight.departure_time), MONTH)) AS month, COUNT(*) AS num_departures FROM atis.flight GROUP BY month ORDER BY month NULLS LAST;SELECT EXTRACT(MONTH FROM TIMESTAMP_SECONDS(flight.departure_time)) AS month, COUNT(*) AS num_departures FROM atis.flight GROUP BY month ORDER BY month NULLS LAST;",Count the number of flight departures for each month?,
atis,bigquery,date_functions,"SELECT FORMAT_TIMESTAMP('%H:%M', TIMESTAMP_SECONDS(MIN(departure_time))) AS earliest_departure_time FROM atis.flight;",What's the earliest flight departure time in the day in HH:MM?,
atis,bigquery,date_functions,"SELECT DATE_DIFF(CURRENT_DATE(), DATE(TIMESTAMP_SECONDS(MIN(departure_time))), DAY) AS difference_in_days FROM atis.flight;SELECT DATE_DIFF(CURRENT_DATE(), DATE(TIMESTAMP_SECONDS(MIN(f.departure_time))), DAY) AS days_difference FROM atis.flight AS f;",What's the absolute difference in time in days between today and the earliest flight departure? Give the answer as an integer.,
atis,bigquery,group_by,"SELECT fare.fare_airline, SUM(fare.round_trip_cost) AS total_round_trip_cost FROM atis.fare GROUP BY fare.fare_airline ORDER BY total_round_trip_cost DESC NULLS FIRST;",What is the total cost of round-trip fares for each airline code?,
atis,bigquery,group_by,"SELECT fare.fare_airline, AVG(fare.round_trip_cost) AS average_cost FROM atis.fare WHERE fare.from_airport = 'LAX' AND fare.to_airport = 'ORD' GROUP BY fare.fare_airline ORDER BY average_cost DESC;SELECT airline.airline_name, AVG(fare.round_trip_cost) AS avg_round_trip_cost FROM atis.fare JOIN atis.airline ON fare.fare_airline = airline.airline_code WHERE fare.from_airport = 'LAX' AND fare.to_airport = 'ORD' GROUP BY airline.airline_name ORDER BY avg_round_trip_cost DESC NULLS FIRST;","What is the average cost of round-trip fares from Los Angeles (LAX) to Chicago (ORD) for each airline, sorted in descending order by average cost?",
atis,bigquery,group_by,"SELECT f.from_airport, f.to_airport, AVG(f.one_direction_cost) AS average_cost FROM atis.fare AS f GROUP BY f.from_airport, f.to_airport ORDER BY f.from_airport NULLS LAST, f.to_airport NULLS LAST;",What is the average cost of a one-way trip for each airport pair in the fare table?,
atis,bigquery,group_by,"SELECT food_service.compartment, COUNT(food_service.meal_number) AS number_of_meals FROM atis.food_service GROUP BY food_service.compartment ORDER BY number_of_meals DESC;","How many meals are served in each compartment, sorted by the number of meals in descending order?",
atis,bigquery,group_by,"SELECT airport.airport_code, COUNT(flight.from_airport) AS num_departures FROM atis.airport LEFT JOIN atis.flight ON airport.airport_code = flight.from_airport AND flight.connections = 0 GROUP BY airport.airport_code;SELECT airport.airport_code, COUNT(flight.from_airport) AS num_departures FROM atis.airport JOIN atis.flight ON airport.airport_code = flight.from_airport WHERE flight.connections = 0 GROUP BY airport.airport_code;","How many flights depart from each airport code, excluding departures with connections?",
atis,bigquery,order_by,"SELECT flight.flight_id, (flight.arrival_time - flight.departure_time) AS duration FROM atis.flight WHERE to_airport = 'ORD' ORDER BY duration ASC NULLS LAST;","Which flight ids to Chicago (ORD) have the longest duration from departure to arrival, sorted in ascending order?",
atis,bigquery,order_by,"SELECT airport.airport_name, airport.minimum_connect_time FROM atis.airport ORDER BY airport.minimum_connect_time ASC NULLS LAST;SELECT airport.airport_code, airport.minimum_connect_time FROM atis.airport ORDER BY airport.minimum_connect_time ASC NULLS LAST;SELECT airport.airport_name, airport.airport_code, airport.minimum_connect_time FROM atis.airport ORDER BY airport.minimum_connect_time ASC NULLS LAST;","Which airports have the shortest minimum connect time, sorted in ascending order? Show the minimum connect time.",
atis,bigquery,order_by,SELECT aircraft.aircraft_code FROM atis.aircraft ORDER BY pay_load DESC LIMIT 1;,Which aircraft code can carry the highest weight of cargo that any aircraft can carry?,
atis,bigquery,order_by,"SELECT airline.airline_name, COUNT(flight.flight_id) AS number_of_flights FROM atis.flight JOIN atis.airline ON flight.airline_code = airline.airline_code GROUP BY airline.airline_name ORDER BY number_of_flights DESC LIMIT 2;SELECT airline.airline_code, COUNT(flight.flight_id) AS number_of_flights FROM atis.flight JOIN atis.airline ON flight.airline_code = airline.airline_code GROUP BY airline.airline_code ORDER BY number_of_flights DESC LIMIT 2;SELECT airline.airline_name, airline.airline_code, COUNT(flight.flight_id) AS number_of_flights FROM atis.flight JOIN atis.airline ON flight.airline_code = airline.airline_code GROUP BY airline.airline_name, airline.airline_code ORDER BY number_of_flights DESC LIMIT 2;",What are the top 2 airlines with the most flights?,
atis,bigquery,order_by,SELECT aircraft.aircraft_code FROM atis.aircraft WHERE aircraft.cruising_speed > 200 ORDER BY aircraft.aircraft_code ASC NULLS LAST;,What are the aircraft codes for all aircraft with a cruising speed of over 200 mph? sort the aircraft codes in ascending order.,
atis,bigquery,ratio,"SELECT CAST(aircraft.range_miles AS FLOAT64) / NULLIF(aircraft.pay_load, 0) AS range_to_payload_ratio FROM atis.aircraft;",Calculate the ratio of the maximum range to the maximum payload for each aircraft,
atis,bigquery,ratio,"SELECT fare.fare_id, CAST(fare.one_direction_cost AS FLOAT64) / NULLIF(fare.round_trip_cost, 0) AS cost_ratio FROM atis.fare ORDER BY cost_ratio NULLS LAST;",What is the ratio of one-way trip costs to round-trip costs for each fare?,
atis,bigquery,ratio,"SELECT aircraft.aircraft_code, CAST(aircraft.capacity AS FLOAT64) / NULLIF(aircraft.range_miles, 0) AS capacity_range_ratio FROM atis.aircraft;",What is the ratio of aircraft capacity to its range in miles for each aircraft code?,
atis,bigquery,ratio,"SELECT flight.airline_code, CAST(SUM(CASE WHEN flight.stops > 0 THEN 1 ELSE 0 END) AS FLOAT64) / NULLIF(COUNT(*), 0) AS ratio FROM atis.flight GROUP BY flight.airline_code;",What is the proportion of flights with stops out of all flights for each airline code?,
atis,bigquery,ratio,"SELECT aircraft.manufacturer, AVG(CAST(aircraft.cruising_speed AS FLOAT64) / NULLIF(aircraft.pay_load, 0)) AS speed_payload_ratio FROM atis.aircraft GROUP BY aircraft.manufacturer ORDER BY speed_payload_ratio DESC;",How does the average ratio of the cruising speed to the payload of an aircraft vary across different aircraft manufacturers?,
atis,bigquery,table_join,"SELECT flight.flight_id, food_service.meal_description FROM atis.flight JOIN atis.food_service ON flight.meal_code = food_service.meal_code WHERE LOWER(food_service.compartment) LIKE '%first class%';",Which flights serve meals in first class? Give me the flight id and meal description.,
atis,bigquery,table_join,"SELECT DISTINCT airline.airline_name FROM atis.flight_stop JOIN atis.airport ON flight_stop.stop_airport = airport.airport_code JOIN atis.flight ON flight_stop.flight_id = flight.flight_id JOIN atis.airline ON flight.airline_code = airline.airline_code WHERE LOWER(airport.airport_location) LIKE LOWER('%Dallas%');SELECT DISTINCT airline.airline_code FROM atis.flight_stop JOIN atis.airport ON flight_stop.stop_airport = airport.airport_code JOIN atis.flight ON flight_stop.flight_id = flight.flight_id JOIN atis.airline ON flight.airline_code = airline.airline_code WHERE LOWER(airport.airport_location) LIKE LOWER('%Dallas%');SELECT DISTINCT airline.airline_name, airline.airline_code FROM atis.flight_stop JOIN atis.airport ON flight_stop.stop_airport = airport.airport_code JOIN atis.flight ON flight_stop.flight_id = flight.flight_id JOIN atis.airline ON flight.airline_code = airline.airline_code WHERE LOWER(airport.airport_location) LIKE LOWER('%Dallas%');",Which airlines offer flights with a stopover in Dallas?,
atis,bigquery,table_join,"SELECT DISTINCT airline.airline_name FROM atis.flight JOIN atis.airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'LAX' AND flight.to_airport = 'ORD';SELECT DISTINCT airline.airline_code FROM atis.flight JOIN atis.airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'LAX' AND flight.to_airport = 'ORD';SELECT DISTINCT airline.airline_name, airline.airline_code FROM atis.flight JOIN atis.airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'LAX' AND flight.to_airport = 'ORD';",Which airlines offer flights from LAX to ORD?,
atis,bigquery,table_join,"SELECT airline.airline_name, flight.stops FROM atis.flight JOIN atis.airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'ORD' AND flight.to_airport = 'JFK' ORDER BY flight.stops NULLS LAST;SELECT airline.airline_code, flight.stops FROM atis.flight JOIN atis.airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'ORD' AND flight.to_airport = 'JFK' ORDER BY flight.stops NULLS LAST;SELECT airline.airline_name, airline.airline_code, flight.stops FROM atis.flight JOIN atis.airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'ORD' AND flight.to_airport = 'JFK' ORDER BY flight.stops NULLS LAST;","Which airlines offer flights from Chicago (ORD) to New York (JFK), and how many stops do they have, sorted by number of stops in ascending order?",
atis,bigquery,table_join,"SELECT DISTINCT airline.airline_name FROM atis.airline WHERE NOT airline.airline_code IN (SELECT flight.airline_code FROM atis.flight WHERE flight.from_airport = 'JFK' OR flight.to_airport = 'JFK' OR flight.stops > 0);SELECT DISTINCT airline.airline_code FROM atis.airline WHERE NOT airline.airline_code IN (SELECT flight.airline_code FROM atis.flight WHERE flight.from_airport = 'JFK' OR flight.to_airport = 'JFK' OR flight.stops > 0);SELECT DISTINCT airline.airline_name, airline.airline_code FROM atis.airline WHERE NOT airline.airline_code IN (SELECT flight.airline_code FROM atis.flight WHERE flight.from_airport = 'JFK' OR flight.to_airport = 'JFK' OR flight.stops > 0);","Which airlines do not have any flights that either depart from/arrive at JFK, or have one or more stops?",
atis,bigquery,instruct,SELECT state_code FROM atis.airport WHERE LOWER(airport_name) LIKE LOWER('%Orlando International Airport%');,Which state code is Orlando International Airport in?,"Filter airport, city, country names using LIKE.
Filter state code (eg NY), airport codes (eg JFK) using case-insensitive matches.
If multiple flight days are requested, use LIKE and wildcards for each of the days separately, since they are not necessarily ordered."
atis,bigquery,instruct,"SELECT flight.flight_number FROM atis.flight WHERE LOWER(flight.flight_days) LIKE '%mon%' AND LOWER(flight.flight_days) LIKE '%wed%';SELECT flight.flight_id FROM atis.flight WHERE LOWER(flight.flight_days) LIKE '%mon%' AND LOWER(flight.flight_days) LIKE '%wed%';SELECT flight.flight_number, flight.flight_id FROM atis.flight WHERE LOWER(flight.flight_days) LIKE '%mon%' AND LOWER(flight.flight_days) LIKE '%wed%';",Which flights operate on Mondays and Wednesdays? Give me the relevant flight numbers,"Filter airport, city, country names using LIKE.
Filter state code (eg NY), airport codes (eg JFK) using case-insensitive matches.
If multiple flight days are requested, use LIKE for each of the days separately, since they are not necessarily ordered."
atis,bigquery,instruct,SELECT SUM(fare.round_trip_cost) AS total_round_trip_cost FROM atis.fare WHERE fare.from_airport = 'JFK' AND fare.to_airport = 'LAX';,What is the total cost of all round-trip fares from New York (JFK) to Los Angeles?,"Filter airport, city, country names using LIKE.
Filter state code (eg NY), airport codes (eg JFK) using case-insensitive matches.
fare.round_trip_required is not needed when getting the round trip cost."
atis,bigquery,instruct,SELECT minimum_connect_time FROM atis.airport WHERE airport_code = 'JFK';,What is the minimum amount of time required for a connecting flight at JFK Airport?,"Filter airport, city, country names using LIKE.
Filter state code (eg NY) and airport codes (eg JFK) using upper-case matches."
atis,bigquery,instruct,SELECT COUNT(DISTINCT flight_fare.flight_id) FROM atis.flight_fare JOIN atis.fare ON flight_fare.fare_id = fare.fare_id WHERE fare.round_trip_required = 'Yes';,How many flights require a round-trip to purchase the fare?,"Filter airport, city, country names using LIKE.
Filter state code (eg NY) and airport codes (eg JFK) using upper-case matches."
geography,bigquery,group_by,"SELECT city.country_name, SUM(city.population) AS total_population FROM geography.city GROUP BY city.country_name ORDER BY total_population DESC;",What is the total population in cities by country?,
geography,bigquery,group_by,"SELECT river.country_name, AVG(river.length) AS average_length FROM geography.river GROUP BY river.country_name ORDER BY average_length DESC;",What is the average length of rivers in each country?,
geography,bigquery,group_by,"SELECT river.country_name, COUNT(DISTINCT river.river_name) AS number_of_rivers FROM geography.river GROUP BY river.country_name ORDER BY number_of_rivers DESC NULLS FIRST;",How many rivers flow through each country?,
geography,bigquery,group_by,"SELECT mountain.country_name, COUNT(mountain.mountain_name) AS number_of_mountains FROM geography.mountain GROUP BY mountain.country_name ORDER BY number_of_mountains DESC NULLS FIRST;",How many mountains are there in each country?,
geography,bigquery,group_by,"SELECT lake.state_name, COUNT(lake.lake_name) AS lake_count FROM geography.lake GROUP BY lake.state_name ORDER BY lake_count DESC NULLS FIRST;",How many lakes are there in each state?,
geography,bigquery,order_by,"SELECT state.state_name, state.density FROM geography.state ORDER BY state.density DESC;","Which states have the highest population density in people per square kilometer, ordered from highest to lowest?",
geography,bigquery,order_by,"SELECT lake.lake_name, lake.area FROM geography.lake ORDER BY lake.area DESC;","Which lakes have the largest areas in square kilometers, ordered from largest to smallest?",
geography,bigquery,order_by,"SELECT city.city_name, city.population FROM geography.city ORDER BY city.population DESC LIMIT 5;",What are the top 5 cities with the highest population? Give both city names and the population.,
geography,bigquery,order_by,"SELECT river.river_name, river.length FROM geography.river ORDER BY river.length DESC;","What are the longest rivers in meters, ordered from longest to shortest?",
geography,bigquery,order_by,"SELECT mountain.mountain_name, mountain.mountain_altitude FROM geography.mountain ORDER BY mountain.mountain_altitude DESC;","What are the highest mountains in meters, ordered from highest to lowest altitude?",
geography,bigquery,ratio,SELECT CAST(SUM(CASE WHEN LOWER(state.country_name) = 'united states' THEN state.population ELSE 0 END) AS FLOAT64) / CAST(SUM(CASE WHEN LOWER(state.state_name) = 'california' THEN state.population ELSE 0 END) AS FLOAT64) AS population_ratio FROM geography.state;,What is the ratio of the population of the United States to the population of California?,
geography,bigquery,ratio,"SELECT CAST((SELECT length FROM geography.river WHERE LOWER(river_name) LIKE '%mississippi%') AS FLOAT64) / NULLIF((SELECT length FROM geography.river WHERE LOWER(river_name) LIKE '%rhine%'), 0) AS ratio;",What is the ratio of the length of the Mississippi River to the length of the Rhine River?,
geography,bigquery,ratio,"SELECT (CAST(everest.mountain_altitude AS FLOAT64) / NULLIF(dhaulagiri.mountain_altitude, 0)) AS altitude_ratio FROM (SELECT mountain_altitude FROM geography.mountain WHERE mountain_name = 'Mount Everest') AS everest, (SELECT mountain_altitude FROM geography.mountain WHERE mountain_name = 'Dhaulagiri') AS dhaulagiri;",What is the ratio of the altitude of 'Mount Everest' to the altitude of 'Dhaulagiri'? Match strings exactly,
geography,bigquery,ratio,"SELECT city.city_name, CAST(city.population AS FLOAT64) / NULLIF(state.population, 0) AS population_ratio FROM geography.city JOIN geography.state ON city.state_name = state.state_name ORDER BY population_ratio DESC;","How does the population of each city vary in relation to the population of its corresponding state? Return the city name, and the proportion of each city's population relative to the state.",
geography,bigquery,ratio,"SELECT state_name, population / NULLIF(area, 0) AS population_density FROM geography.state;",Get the ratio of population per area for each state,
geography,bigquery,table_join,SELECT DISTINCT lake.country_name FROM geography.lake JOIN geography.river ON lake.country_name = river.country_name;,Which countries have both lakes and rivers?,
geography,bigquery,table_join,SELECT border_info.border FROM geography.border_info JOIN geography.lake ON border_info.state_name = lake.state_name WHERE LOWER(lake.lake_name) LIKE LOWER('%Ontario%');,Which states border the state where lake ontario is?,
geography,bigquery,table_join,SELECT lake.lake_name FROM geography.lake JOIN geography.state ON lake.state_name = state.state_name WHERE state.area > 1000 AND LOWER(lake.lake_name) LIKE LOWER('Lake%') ORDER BY lake.lake_name NULLS LAST;,"Which lakes have a name that starts with ""Lake""? They should be located in states with an area greater than 1000 square kilometers.",
geography,bigquery,table_join,"SELECT highlow.state_name, highlow.highest_point, state.density FROM geography.highlow JOIN geography.state ON highlow.state_name = state.state_name;",What is the highest point in each state and what is the population density of that state?,
geography,bigquery,table_join,"SELECT l.country_name, AVG(r.length) AS average_length FROM geography.river AS r JOIN geography.lake AS l ON r.country_name = l.country_name GROUP BY 1;",What is the average length of rivers per country in countries with a lake?,
geography,bigquery,instruct,SELECT state_name FROM geography.state WHERE population < 100000;,Which states have fewer than a hundred thousand people?,Always filter names using LIKE
geography,bigquery,instruct,"SELECT river_name FROM geography.river WHERE traverse LIKE '%,%,%';",Which rivers traverse at least 3 cities/landmarks?,Always filter names using LIKE
geography,bigquery,instruct,"SELECT lake_name, area FROM geography.lake WHERE LOWER(state_name) LIKE LOWER('%Michigan%');",What are the names and areas of the lakes in Michigan?,Always filter names using LIKE
geography,bigquery,instruct,"SELECT mountain_name, mountain_altitude FROM geography.mountain WHERE LOWER(country_name) LIKE LOWER('%Nepal%');",What are the names and altitudes of the mountains in Nepal?,Always filter names using LIKE
geography,bigquery,instruct,"SELECT city_name, population FROM geography.city WHERE LOWER(country_name) LIKE LOWER('%United States%');",Get the cities in the United States and their population,Always filter names using LIKE
restaurants,bigquery,group_by,"SELECT restaurant.food_type, COUNT(DISTINCT restaurant.id) AS total_number_of_restaurants FROM restaurants.restaurant GROUP BY restaurant.food_type;",What is the total number of restaurants serving each type of food?,
restaurants,bigquery,group_by,"SELECT location.city_name, COUNT(DISTINCT location.restaurant_id) AS total_count FROM restaurants.location GROUP BY location.city_name;",What is the total count of restaurants in each city?,
restaurants,bigquery,group_by,"SELECT restaurant.food_type, AVG(restaurant.rating) AS average_rating FROM restaurants.restaurant GROUP BY restaurant.food_type ORDER BY average_rating DESC;",What is the average rating of restaurants serving each type of food?,
restaurants,bigquery,group_by,"SELECT restaurant.city_name, COUNT(*) AS number_of_restaurants FROM restaurants.restaurant WHERE LOWER(restaurant.food_type) LIKE LOWER('%Italian%') GROUP BY restaurant.city_name ORDER BY number_of_restaurants DESC;",How many restaurants serve Italian food in each city?,
restaurants,bigquery,group_by,"SELECT city_name, COUNT(id) AS number_of_restaurants FROM restaurants.restaurant GROUP BY city_name ORDER BY number_of_restaurants DESC;",How many restaurants are there in each city? Order the results by the number of restaurants in descending order.,
restaurants,bigquery,order_by,SELECT street_name FROM restaurants.location GROUP BY street_name ORDER BY COUNT(restaurant_id) DESC NULLS FIRST LIMIT 1;,Which street has the most number of restaurants?,
restaurants,bigquery,order_by,SELECT name FROM restaurants.restaurant WHERE LOWER(food_type) LIKE LOWER('%Italian%') OR LOWER(city_name) LIKE LOWER('%New York%') ORDER BY name NULLS LAST;,Which restaurants serve Italian cuisine or are located in New York? Order the results by the restaurant name.,
restaurants,bigquery,order_by,"SELECT geographic.region, AVG(restaurant.rating) AS average_rating FROM restaurants.restaurant JOIN restaurants.geographic ON restaurant.city_name = geographic.city_name GROUP BY geographic.region ORDER BY geographic.region NULLS LAST;",What is the average rating of restaurants in each region? Order the results by the region name.,
restaurants,bigquery,order_by,SELECT restaurant.name FROM restaurants.restaurant ORDER BY restaurant.rating DESC LIMIT 3;,What are the names of the top 3 restaurants with the highest ratings?,
restaurants,bigquery,order_by,"SELECT name, rating FROM restaurants.restaurant ORDER BY rating DESC NULLS FIRST;SELECT id, rating FROM restaurants.restaurant ORDER BY rating DESC NULLS FIRST;SELECT name, id, rating FROM restaurants.restaurant ORDER BY rating DESC NULLS FIRST;",List the restaurants starting from the best ratings to the lowest,
restaurants,bigquery,ratio,"SELECT CAST(COUNT(*) AS FLOAT64) / NULLIF((SELECT COUNT(*) FROM restaurants.restaurant), 0) AS rating_ratio FROM restaurants.restaurant WHERE rating > 4.5;",What is the ratio of restaurants with rating > 4.5 to the total number of restaurants in the database.,
restaurants,bigquery,ratio,"SELECT CAST(SUM(CASE WHEN restaurant.rating > 4.0 THEN 1 ELSE 0 END) AS FLOAT64) / NULLIF(SUM(CASE WHEN restaurant.rating < 4.0 THEN 1 ELSE 0 END), 0) AS ratio FROM restaurants.restaurant;",What is the ratio of restaurants with a rating above 4.0 to restaurants with a rating below 4.0 overall?,
restaurants,bigquery,ratio,"SELECT CAST(COUNT(CASE WHEN rating > 4 THEN 1 END) AS FLOAT64) / NULLIF(COUNT(CASE WHEN rating < 4 THEN 1 END), 0) AS ratio FROM restaurants.restaurant WHERE LOWER(city_name) LIKE LOWER('New York');",What is the ratio of restaurants with a rating above 4 to restaurants with a rating below 4 in New York?,
restaurants,bigquery,ratio,"SELECT CAST(SUM(CASE WHEN LOWER(restaurant.food_type) LIKE '%vegan%' THEN 1 ELSE 0 END) AS FLOAT64) / NULLIF(SUM(CASE WHEN NOT LOWER(restaurant.food_type) LIKE '%vegan%' THEN 1 ELSE 0 END), 0) AS ratio FROM restaurants.restaurant WHERE LOWER(LOWER(restaurant.city_name)) LIKE LOWER('%san francisco%');",What is the ratio of restaurants serving vegan food to restaurants serving non-vegan food in San Francisco? Match food_type case insensitively,
restaurants,bigquery,ratio,"SELECT CAST(COUNT(CASE WHEN LOWER(food_type) LIKE LOWER('%Italian%') THEN 1 END) AS FLOAT64) / NULLIF(COUNT(food_type), 0) AS ratio FROM restaurants.restaurant WHERE LOWER(city_name) LIKE LOWER('%Los Angeles%');",What is the ratio of Italian restaurants out of all restaurants in Los Angeles?,
restaurants,bigquery,table_join,"SELECT r.city_name, r.name, COUNT(r.id) AS restaurant_count FROM restaurants.restaurant AS r GROUP BY r.city_name, r.name HAVING COUNT(r.id) > 1;","What cities have more than one restaurants with the same name, and how many of them are there? Return the city name, restaurant name, and restaurant count",
restaurants,bigquery,table_join,"SELECT r.city_name, AVG(r.rating) AS average_rating FROM restaurants.restaurant r JOIN restaurants.location l ON r.id = l.restaurant_id WHERE LOWER(r.food_type) LIKE '%mexican%' GROUP BY r.city_name;",What is the average rating of restaurants that serve Mexican food in each city?,
restaurants,bigquery,table_join,"SELECT geographic.region, AVG(restaurant.rating) AS average_rating FROM restaurants.geographic JOIN restaurants.restaurant ON geographic.city_name = restaurant.city_name GROUP BY 1;",What is the average rating of restaurants in each region?,
restaurants,bigquery,table_join,"SELECT geographic.region, COUNT(restaurant.id) AS number_of_restaurants FROM restaurants.restaurant JOIN restaurants.geographic ON restaurant.city_name = geographic.city_name WHERE LOWER(restaurant.food_type) LIKE '%italian%' GROUP BY geographic.region ORDER BY number_of_restaurants DESC;",How many restaurants serve Italian food in each region?,
restaurants,bigquery,table_join,"SELECT geographic.region, COUNT(DISTINCT restaurant.id) AS number_of_restaurants FROM restaurants.geographic JOIN restaurants.restaurant ON geographic.city_name = restaurant.city_name GROUP BY geographic.region ORDER BY number_of_restaurants DESC NULLS FIRST;SELECT geographic.region, COUNT(DISTINCT restaurant.id) AS number_of_restaurants FROM restaurants.geographic LEFT JOIN restaurants.restaurant ON geographic.city_name = restaurant.city_name GROUP BY geographic.region ORDER BY number_of_restaurants DESC NULLS FIRST;",How many restaurants are there in each region?,
restaurants,bigquery,instruct,SELECT DISTINCT restaurant.city_name FROM restaurants.restaurant WHERE rating = (SELECT MAX(rating) FROM restaurants.restaurant);,Which city has the highest-rated restaurant?,Match all strings case-insensitively using wildcard operators
restaurants,bigquery,instruct,"SELECT restaurant.name, restaurant.rating FROM restaurants.restaurant WHERE restaurant.rating > 4 AND LOWER(restaurant.city_name) LIKE LOWER('%New York%');",What's the name and rating of all the restaurants that have a rating greater than 4 and are located in the city of New York?,Match all strings case-insensitively using wildcard operators
restaurants,bigquery,instruct,"SELECT restaurant.name, restaurant.food_type FROM restaurants.restaurant JOIN restaurants.location ON restaurant.id = location.restaurant_id WHERE location.street_name LIKE '%Market St%' AND location.city_name LIKE '%San Francisco%';",What's the name and food type of all the restaurants located on Market St in San Francisco?,Match all strings case-insensitively using wildcard operators
restaurants,bigquery,instruct,SELECT restaurant.name FROM restaurants.restaurant WHERE LOWER(LOWER(restaurant.food_type)) LIKE LOWER('%italian%');,What are the names of the restaurants that serve Italian food?,Match all strings case-insensitively using wildcard operators
restaurants,bigquery,instruct,SELECT DISTINCT restaurant.name FROM restaurants.restaurant WHERE LOWER(restaurant.city_name) LIKE LOWER('%Los Angeles%') AND restaurant.rating > 4 ORDER BY restaurant.name NULLS LAST;,What are the names of the restaurants in Los Angeles that have a rating higher than 4?,Match all strings case-insensitively using wildcard operators
scholar,bigquery,group_by,"SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM scholar.paper GROUP BY paper.year ORDER BY paper.year NULLS LAST;",What is the total number of papers published per year?,
scholar,bigquery,group_by,"SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM scholar.paper GROUP BY paper.year ORDER BY paper.year NULLS LAST;",What is the total number of papers published in each year?,
scholar,bigquery,group_by,"SELECT paperdataset.datasetid, COUNT(DISTINCT paperdataset.paperid) AS total_papers FROM scholar.paperdataset GROUP BY paperdataset.datasetid;SELECT dataset.datasetname, COUNT(paperdataset.paperid) AS total_papers FROM scholar.paperdataset JOIN scholar.dataset ON paperdataset.datasetid = dataset.datasetid GROUP BY dataset.datasetname;",What is the total number of papers associated with each dataset?,
scholar,bigquery,group_by,"SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS keyphrase_count FROM scholar.paperkeyphrase GROUP BY paperkeyphrase.paperid ORDER BY keyphrase_count DESC;SELECT p.title, COUNT(pk.keyphraseid) AS num_keyphrases FROM scholar.paper AS p JOIN scholar.paperkeyphrase AS pk ON p.paperid = pk.paperid GROUP BY p.title ORDER BY num_keyphrases DESC;",How many keyphrases are associated with each paper?,
scholar,bigquery,group_by,SELECT COUNT(*) AS number_of_authors FROM (SELECT writes.authorid FROM scholar.writes GROUP BY writes.authorid HAVING COUNT(writes.paperid) > 2) AS subquery;,How many authors have published more than 2 papers?,
scholar,bigquery,order_by,"SELECT paper.paperid, COUNT(DISTINCT writes.authorid) AS num_authors FROM scholar.paper JOIN scholar.writes ON paper.paperid = writes.paperid GROUP BY paper.paperid ORDER BY num_authors DESC NULLS FIRST;SELECT paper.title, COUNT(DISTINCT writes.authorid) AS num_authors FROM scholar.paper JOIN scholar.writes ON paper.paperid = writes.paperid GROUP BY paper.title ORDER BY num_authors DESC NULLS FIRST;SELECT paper.paperid, paper.title, COUNT(DISTINCT writes.authorid) AS num_authors FROM scholar.paper JOIN scholar.writes ON paper.paperid = writes.paperid GROUP BY paper.paperid, paper.title ORDER BY num_authors DESC NULLS FIRST;","Which papers have the highest number of authors, ordered by the number of authors in descending order?",
scholar,bigquery,order_by,"SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS total_keyphrases FROM scholar.paperkeyphrase GROUP BY paperkeyphrase.paperid ORDER BY paperkeyphrase.paperid ASC NULLS LAST;","What is the total number of keyphrases associated with each paper, ordered by the paper ID in ascending order?",
scholar,bigquery,order_by,SELECT paper.title FROM scholar.paper WHERE paper.year = 2020 ORDER BY paper.title ASC NULLS LAST;,"What are the titles of the papers published in the year 2020, ordered alphabetically?",
scholar,bigquery,order_by,SELECT journal.journalname FROM scholar.journal ORDER BY LENGTH(journal.journalname) ASC NULLS LAST;,"What are the names of the journals in the database, ordered by the length of the journal name from shortest to longest?",
scholar,bigquery,order_by,"SELECT cite.citingpaperid, COUNT(*) AS citation_count FROM scholar.cite GROUP BY cite.citingpaperid ORDER BY citation_count DESC;SELECT p.paperid, p.numciting FROM scholar.paper AS p WHERE p.numciting > 0 ORDER BY p.numciting DESC NULLS FIRST;SELECT p.title, COUNT(c.citedpaperid) AS num_cited_papers FROM scholar.paper AS p JOIN scholar.cite AS c ON p.paperid = c.citingpaperid GROUP BY p.title ORDER BY num_cited_papers DESC NULLS FIRST;","For each paper that cites other papers, how many other papers does it cite? Sort by the number of papers cited in descending order",
scholar,bigquery,ratio,"SELECT CAST(COUNT(DISTINCT CASE WHEN keyphrase_count > 1 THEN subquery.paperid END) AS FLOAT64) / NULLIF(COUNT(DISTINCT CASE WHEN keyphrase_count = 1 THEN subquery.paperid END), 0) AS ratio FROM (SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS keyphrase_count FROM scholar.paperkeyphrase GROUP BY paperkeyphrase.paperid) AS subquery;",What is the ratio of papers that have more than 1 keyphrases to papers that have 1 keyphrase?,
scholar,bigquery,ratio,"SELECT CAST(COUNT(CASE WHEN paper.numcitedby > 1 THEN 1 END) AS FLOAT64) / NULLIF(COUNT(CASE WHEN paper.numcitedby < 2 THEN 1 END), 0) AS ratio FROM scholar.paper;",What is the ratio of papers that have been cited by 2 or more papers to papers that have been cited by less than 2 papers?,
scholar,bigquery,ratio,"SELECT CAST(COUNT(CASE WHEN paper.year = 2020 THEN 1 END) AS FLOAT64) / NULLIF(COUNT(paper.paperid), 0) AS ratio FROM scholar.paper;",What is the ratio of papers published in the year 2020 to the total number of papers in the database?,
scholar,bigquery,ratio,"SELECT CAST(COUNT(DISTINCT CASE WHEN paper_count >= 3 THEN subquery.authorid END) AS FLOAT64) / NULLIF(COUNT(DISTINCT CASE WHEN paper_count < 3 THEN subquery.authorid END), 0) AS ratio FROM (SELECT writes.authorid, COUNT(writes.paperid) AS paper_count FROM scholar.writes GROUP BY writes.authorid) AS subquery;",What is the ratio of authors who have written 3 or more papers to authors who have written less than 3 papers?,
scholar,bigquery,ratio,"SELECT CAST(COUNT(CASE WHEN dataset_count > 1 THEN 1 END) AS FLOAT64) / NULLIF(COUNT(CASE WHEN dataset_count = 1 THEN 1 END), 0) AS ratio FROM (SELECT paperdataset.paperid, COUNT(paperdataset.datasetid) AS dataset_count FROM scholar.paperdataset GROUP BY paperdataset.paperid) AS subquery;",What is the proportion of papers that belong to more than 1 dataset to papers that belong to 1 dataset?,
scholar,bigquery,table_join,"SELECT paper.title FROM scholar.paper JOIN scholar.paperkeyphrase ON paper.paperid = paperkeyphrase.paperid JOIN scholar.keyphrase ON paperkeyphrase.keyphraseid = keyphrase.keyphraseid WHERE LOWER(keyphrase.keyphrasename) LIKE LOWER('%Machine Learning%');SELECT paper.paperid FROM scholar.paper JOIN scholar.paperkeyphrase ON paper.paperid = paperkeyphrase.paperid JOIN scholar.keyphrase ON paperkeyphrase.keyphraseid = keyphrase.keyphraseid WHERE LOWER(keyphrase.keyphrasename) LIKE LOWER('%Machine Learning%');SELECT paper.title, paper.paperid FROM scholar.paper JOIN scholar.paperkeyphrase ON paper.paperid = paperkeyphrase.paperid JOIN scholar.keyphrase ON paperkeyphrase.keyphraseid = keyphrase.keyphraseid WHERE LOWER(keyphrase.keyphrasename) LIKE LOWER('%Machine Learning%');","Which papers are associated with the keyphrase ""Machine Learning""?",
scholar,bigquery,table_join,"SELECT author.authorname, COUNT(DISTINCT writes.paperid) AS number_of_papers FROM scholar.author JOIN scholar.writes ON author.authorid = writes.authorid GROUP BY author.authorname ORDER BY number_of_papers DESC;SELECT author.authorid, COUNT(DISTINCT writes.paperid) AS number_of_papers FROM scholar.author JOIN scholar.writes ON author.authorid = writes.authorid GROUP BY author.authorid ORDER BY number_of_papers DESC;SELECT author.authorname, author.authorid, COUNT(DISTINCT writes.paperid) AS number_of_papers FROM scholar.author JOIN scholar.writes ON author.authorid = writes.authorid GROUP BY author.authorname, author.authorid ORDER BY number_of_papers DESC;","Which authors have published the most papers, ordered by the number of papers they have published in descending order?",
scholar,bigquery,table_join,SELECT COUNT(DISTINCT paperkeyphrase.keyphraseid) AS total_keyphrases FROM scholar.paper JOIN scholar.journal ON paper.journalid = journal.journalid JOIN scholar.paperkeyphrase ON paper.paperid = paperkeyphrase.paperid WHERE LOWER(journal.journalname) LIKE LOWER('%IEEE Transactions%');,"What is the total number of unique keyphrases associated with papers published in the journal with ""IEEE Transactions"" in its name?",
scholar,bigquery,table_join,"SELECT journal.journalname, COUNT(DISTINCT paper.paperid) AS total_papers FROM scholar.paper JOIN scholar.journal ON paper.journalid = journal.journalid GROUP BY journal.journalname ORDER BY journal.journalname NULLS LAST;","What is the total number of papers published in each journal, ordered by the journal name?",
scholar,bigquery,table_join,"SELECT paperdataset.paperid, COUNT(cite.citedpaperid) AS citation_count FROM scholar.paperdataset JOIN scholar.cite ON paperdataset.paperid = cite.citedpaperid WHERE paperdataset.datasetid = (SELECT datasetid FROM scholar.dataset WHERE LOWER(datasetname) LIKE LOWER('%COVID-19 Research%')) GROUP BY paperdataset.paperid ORDER BY citation_count DESC NULLS FIRST;SELECT p.title, COUNT(c.citingpaperid) AS num_citing_papers FROM scholar.paper AS p JOIN scholar.paperdataset AS pd ON p.paperid = pd.paperid JOIN scholar.cite AS c ON p.paperid = c.citedpaperid JOIN scholar.dataset AS d ON pd.datasetid = d.datasetid WHERE d.datasetname = 'COVID-19 Research' GROUP BY p.title ORDER BY num_citing_papers DESC;","How many papers cite each paper in the dataset named ""COVID-19 Research""?",
scholar,bigquery,instruct,"SELECT venue.venuename, COUNT(DISTINCT paper.paperid) FROM scholar.paper JOIN scholar.venue ON paper.venueid = venue.venueid WHERE paper.venueid = (SELECT venueid FROM scholar.paper WHERE paperid = 2) GROUP BY venue.venuename;","What is the name of the venue where the paper with paper ID 2 was published, and how many papers were published in total in that venue?",Always filter strings using LIKE
scholar,bigquery,instruct,SELECT author.authorname FROM scholar.author JOIN scholar.writes ON author.authorid = writes.authorid JOIN scholar.paper ON writes.paperid = paper.paperid WHERE paper.title = 'The Effects of Climate Change on Agriculture';,"What are the names of the authors who wrote the paper with the title ""The Effects of Climate Change on Agriculture""?",Always filter strings with an exact match
scholar,bigquery,instruct,SELECT COUNT(paper.paperid) FROM scholar.paper JOIN scholar.journal ON paper.journalid = journal.journalid WHERE paper.year = 2020 AND LOWER(journal.journalname) LIKE LOWER('%nature%');,"How many papers were published in the journal ""nature"" in the year 2020?",Filter strings with case-insensitive matching
scholar,bigquery,instruct,SELECT COUNT(DISTINCT paper.paperid) FROM scholar.paper JOIN scholar.journal ON paper.journalid = journal.journalid JOIN scholar.paperkeyphrase ON paper.paperid = paperkeyphrase.paperid JOIN scholar.keyphrase ON paperkeyphrase.keyphraseid = keyphrase.keyphraseid WHERE LOWER(keyphrase.keyphrasename) LIKE LOWER('%machine learning%') AND journal.journalname = 'IEEE Transactions on Pattern Analysis and Machine Intelligence';,"How many papers are associated with the keyphrase ""machine learning"" and were published in the journal named ""IEEE Transactions on Pattern Analysis and Machine Intelligence""?","Filter paper names, journal names, using exact matches. Filter keyphrases with case-insensitive matching."
scholar,bigquery,instruct,SELECT COUNT(DISTINCT writes.authorid) AS number_of_authors FROM scholar.writes JOIN scholar.paper ON writes.paperid = paper.paperid JOIN scholar.journal ON paper.journalid = journal.journalid WHERE LOWER(journal.journalname) LIKE LOWER('%Science%') AND paper.year = 2020;,"How many authors wrote papers that were published in the journal ""Science"" in the year 2020?",Filter paper names using exact matches. Filter keyphrases and journal names with case-insensitive matching.
yelp,bigquery,date_functions,"SELECT COUNT(*) AS review_count FROM yelp.review AS r JOIN yelp.business AS b ON r.business_id = b.business_id WHERE b.state = 'CA' AND PARSE_DATE('%B', r.month) >= DATE_SUB(CURRENT_DATE(), INTERVAL 10 MONTH);",How many reviews were written for businesses located in California in the last 10 months?,
yelp,bigquery,date_functions,"SELECT SUM(COUNT) AS total_checkins FROM yelp.checkin WHERE DAY IN ('Thursday', 'Friday');",What is the total number of check-ins on the 2 days before Saturday?,
yelp,bigquery,date_functions,"SELECT COUNT(*) AS review_count FROM yelp.review WHERE DATE(CAST(review.year AS STRING) || '-' || CASE review.month WHEN 'January' THEN '01' WHEN 'February' THEN '02' WHEN 'March' THEN '03' WHEN 'April' THEN '04' WHEN 'May' THEN '05' WHEN 'June' THEN '06' WHEN 'July' THEN '07' WHEN 'August' THEN '08' WHEN 'September' THEN '09' WHEN 'October' THEN '10' WHEN 'November' THEN '11' WHEN 'December' THEN '12' END || '-01') = (SELECT DATE_SUB(DATE(CAST(r.year AS STRING) || '-' || CASE r.month WHEN 'January' THEN '01' WHEN 'February' THEN '02' WHEN 'March' THEN '03' WHEN 'April' THEN '04' WHEN 'May' THEN '05' WHEN 'June' THEN '06' WHEN 'July' THEN '07' WHEN 'August' THEN '08' WHEN 'September' THEN '09' WHEN 'October' THEN '10' WHEN 'November' THEN '11' WHEN 'December' THEN '12' END || '-01'), INTERVAL 2 MONTH) FROM yelp.review AS r WHERE r.rid = 3);",How many reviews were there 2 months before the review with id 3?,
yelp,bigquery,date_functions,SELECT text AS message FROM yelp.tip WHERE LOWER(MONTH) LIKE LOWER('%May%') AND YEAR = 2021 LIMIT 1;,What was the message that came with the tip made exactly 2 months after March 2021?,
yelp,bigquery,date_functions,SELECT COUNT(DISTINCT MONTH) AS num_months FROM yelp.review WHERE YEAR = 2021 AND CASE MONTH WHEN 'January' THEN 1 WHEN 'February' THEN 2 WHEN 'March' THEN 3 WHEN 'April' THEN 4 WHEN 'May' THEN 5 WHEN 'June' THEN 6 WHEN 'July' THEN 7 WHEN 'August' THEN 8 WHEN 'September' THEN 9 WHEN 'October' THEN 10 WHEN 'November' THEN 11 WHEN 'December' THEN 12 END BETWEEN 6 AND 12;,How many months between June 2021 and December 2021 had reviews?,
yelp,bigquery,group_by,"SELECT neighbourhood.neighbourhood_name, COUNT(DISTINCT neighbourhood.business_id) AS business_count FROM yelp.neighbourhood GROUP BY neighbourhood.neighbourhood_name ORDER BY business_count DESC;SELECT neighbourhood.id, COUNT(DISTINCT neighbourhood.business_id) AS business_count FROM yelp.neighbourhood GROUP BY neighbourhood.id ORDER BY business_count DESC;SELECT neighbourhood.neighbourhood_name, neighbourhood.id, COUNT(DISTINCT neighbourhood.business_id) AS business_count FROM yelp.neighbourhood GROUP BY neighbourhood.neighbourhood_name, neighbourhood.id ORDER BY business_count DESC;","Which neighbourhoods have the highest number of businesses, and how many businesses are located in each neighbourhood?",
yelp,bigquery,group_by,"SELECT checkin.day, SUM(checkin.count) AS total_checkins FROM yelp.checkin WHERE checkin.business_id = 'abc123' GROUP BY checkin.day ORDER BY total_checkins DESC;","What is the total number of check-ins for each day of the week for the business with ID ""abc123""?",
yelp,bigquery,group_by,"SELECT checkin.business_id, SUM(checkin.count) AS total_checkins FROM yelp.checkin GROUP BY checkin.business_id ORDER BY total_checkins DESC;",What is the total count of check-ins for each business id?,
yelp,bigquery,group_by,"SELECT business.name, AVG(review.rating) AS average_rating FROM yelp.business JOIN yelp.review ON business.business_id = review.business_id WHERE LOWER(business.city) LIKE LOWER('%NEW YORK%') GROUP BY business.name;",Return the name and average rating for each business in new york,
yelp,bigquery,group_by,"SELECT checkin.day, SUM(checkin.count) AS total_checkins FROM yelp.checkin GROUP BY checkin.day ORDER BY total_checkins DESC;",How many check-ins occurred on each day of the week?,
yelp,bigquery,order_by,"SELECT business.name, AVG(review.rating) AS average_rating FROM yelp.business JOIN yelp.review ON business.business_id = review.business_id WHERE LOWER(business.city) LIKE LOWER('%New York%') GROUP BY business.name, business.business_id ORDER BY average_rating DESC;",Please provide a list of business names in New York and their average ratings ordered by the highest average rating first.,
yelp,bigquery,order_by,"SELECT business.latitude, business.longitude FROM yelp.business JOIN yelp.review ON business.business_id = review.business_id GROUP BY business.business_id, business.latitude, business.longitude ORDER BY AVG(review.rating) DESC NULLS FIRST LIMIT 1;",What is the latitude and longitude of the business with the highest rating?,
yelp,bigquery,order_by,"SELECT business.name, business.review_count FROM yelp.business ORDER BY business.review_count DESC LIMIT 3;SELECT business.business_id, business.review_count FROM yelp.business ORDER BY business.review_count DESC LIMIT 3;SELECT business.bid, business.review_count FROM yelp.business ORDER BY business.review_count DESC LIMIT 3;SELECT business.name, business.business_id, business.review_count FROM yelp.business ORDER BY business.review_count DESC LIMIT 3;SELECT business.name, business.bid, business.review_count FROM yelp.business ORDER BY business.review_count DESC LIMIT 3;SELECT business.business_id, business.bid, business.review_count FROM yelp.business ORDER BY business.review_count DESC LIMIT 3;SELECT business.name, business.business_id, business.bid, business.review_count FROM yelp.business ORDER BY business.review_count DESC LIMIT 3;",What are the top 3 businesses in terms of review count?,
yelp,bigquery,order_by,SELECT business.name FROM yelp.business ORDER BY business.name ASC NULLS LAST;,"What are the names of the businesses in the database, ordered alphabetically?",
yelp,bigquery,order_by,"SELECT review.month, COUNT(review.rid) AS review_count FROM yelp.review WHERE review.year = 2021 GROUP BY review.month ORDER BY FORMAT_DATE('%m', PARSE_DATE('%B', review.month));","How many reviews were posted in each month of the year 2021, ordered by the month?",
yelp,bigquery,ratio,"SELECT business.state, COUNT(business.business_id) / NULLIF(CAST((SELECT COUNT(*) FROM yelp.business) AS FLOAT64), 0) AS ratio FROM yelp.business GROUP BY business.state;",What is the ratio of the number of businesses in each state to the total number of businesses in the database?,
yelp,bigquery,ratio,"SELECT CAST(SUM(CASE WHEN business.is_open = 1 THEN 1 ELSE 0 END) AS FLOAT64) / NULLIF(SUM(CASE WHEN business.is_open = 0 THEN 1 ELSE 0 END), 0) AS ratio FROM yelp.business WHERE LOWER(LOWER(business.city)) LIKE LOWER('%san francisco%');",What is the ratio of open businesses to closed businesses in the city of San Francisco?,
yelp,bigquery,ratio,"SELECT CAST(SUM(CASE WHEN checkin.day IN ('Saturday', 'Sunday') THEN checkin.count ELSE 0 END) AS FLOAT64) / NULLIF(SUM(CASE WHEN NOT checkin.day IN ('Saturday', 'Sunday') THEN checkin.count ELSE 0 END), 0) AS ratio FROM yelp.checkin JOIN yelp.business ON checkin.business_id = business.business_id WHERE LOWER(business.name) LIKE LOWER('%Mark\'s Bistro%');","What is the ratio of check-ins on weekends to check-ins on weekdays for the business named ""Mark's Bistro""?",
yelp,bigquery,ratio,"SELECT CAST(COUNT(CASE WHEN business.state = 'CA' THEN 1 END) AS FLOAT64) / NULLIF(COUNT(CASE WHEN business.state = 'NY' THEN 1 END), 0) AS ratio FROM yelp.business;",What is the ratio of businesses in the state of California to businesses in the state of New York?,
yelp,bigquery,ratio,"SELECT category.category_name, CAST(COUNT(CASE WHEN review.rating > 3 THEN 1 END) AS FLOAT64) / NULLIF(COUNT(CASE WHEN review.rating < 3 THEN 1 END), 0) AS ratio FROM yelp.review JOIN yelp.category ON review.business_id = category.business_id GROUP BY category.category_name ORDER BY ratio DESC;SELECT category.id, CAST(COUNT(CASE WHEN review.rating > 3 THEN 1 END) AS FLOAT64) / NULLIF(COUNT(CASE WHEN review.rating < 3 THEN 1 END), 0) AS ratio FROM yelp.review JOIN yelp.category ON review.business_id = category.business_id GROUP BY category.id ORDER BY ratio DESC;SELECT category.category_name, category.id, CAST(COUNT(CASE WHEN review.rating > 3 THEN 1 END) AS FLOAT64) / NULLIF(COUNT(CASE WHEN review.rating < 3 THEN 1 END), 0) AS ratio FROM yelp.review JOIN yelp.category ON review.business_id = category.business_id GROUP BY category.category_name, category.id ORDER BY ratio DESC;","How does the ratio of positive reviews (rating > 3) to negative reviews (rating < 3) vary across different categories of businesses, ordered by descending ratio?",
yelp,bigquery,table_join,"SELECT users.name, COUNT(review.rid) AS review_count FROM yelp.review JOIN yelp.neighbourhood ON review.business_id = neighbourhood.business_id JOIN yelp.users ON review.user_id = users.user_id WHERE LOWER(neighbourhood.neighbourhood_name) LIKE LOWER('%Downtown%') GROUP BY users.name ORDER BY review_count DESC;SELECT users.user_id, COUNT(review.rid) AS review_count FROM yelp.review JOIN yelp.neighbourhood ON review.business_id = neighbourhood.business_id JOIN yelp.users ON review.user_id = users.user_id WHERE LOWER(neighbourhood.neighbourhood_name) LIKE LOWER('%Downtown%') GROUP BY users.user_id ORDER BY review_count DESC;SELECT users.name, users.user_id, COUNT(review.rid) AS review_count FROM yelp.review JOIN yelp.neighbourhood ON review.business_id = neighbourhood.business_id JOIN yelp.users ON review.user_id = users.user_id WHERE LOWER(neighbourhood.neighbourhood_name) LIKE LOWER('%Downtown%') GROUP BY users.name, users.user_id ORDER BY review_count DESC;","Which users have posted reviews for businesses located in the neighbourhood of ""Downtown"" and how many reviews have they posted?",
yelp,bigquery,table_join,"SELECT category.category_name, SUM(business.review_count) AS total_reviews FROM yelp.business JOIN yelp.category ON business.business_id = category.business_id WHERE business.state = 'CA' GROUP BY category.category_name ORDER BY total_reviews DESC;SELECT category.id, SUM(business.review_count) AS total_reviews FROM yelp.business JOIN yelp.category ON business.business_id = category.business_id WHERE business.state = 'CA' GROUP BY category.id ORDER BY total_reviews DESC;SELECT category.category_name, category.id, SUM(business.review_count) AS total_reviews FROM yelp.business JOIN yelp.category ON business.business_id = category.business_id WHERE business.state = 'CA' GROUP BY category.category_name, category.id ORDER BY total_reviews DESC;","What is the total number of reviews for each category in the state of ""California""?",
yelp,bigquery,table_join,"SELECT category.category_name, SUM(business.review_count) AS total_reviews FROM yelp.business JOIN yelp.category ON business.business_id = category.business_id GROUP BY category.category_name ORDER BY total_reviews DESC;SELECT category.id, SUM(business.review_count) AS total_reviews FROM yelp.business JOIN yelp.category ON business.business_id = category.business_id GROUP BY category.id ORDER BY total_reviews DESC;SELECT category.category_name, category.id, SUM(business.review_count) AS total_reviews FROM yelp.business JOIN yelp.category ON business.business_id = category.business_id GROUP BY category.category_name, category.id ORDER BY total_reviews DESC;",What is the total number of reviews for each business category?,
yelp,bigquery,table_join,"SELECT business.business_id, SUM(checkin.count) AS total_checkins FROM yelp.business JOIN yelp.checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' GROUP BY business.business_id ORDER BY total_checkins DESC;SELECT business.name, SUM(checkin.count) AS total_checkins FROM yelp.business JOIN yelp.checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' GROUP BY business.name ORDER BY total_checkins DESC;SELECT business.bid, SUM(checkin.count) AS total_checkins FROM yelp.business JOIN yelp.checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' GROUP BY business.bid ORDER BY total_checkins DESC;SELECT business.business_id, business.name, SUM(checkin.count) AS total_checkins FROM yelp.business JOIN yelp.checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' GROUP BY business.business_id, business.name ORDER BY total_checkins DESC;SELECT business.business_id, business.bid, SUM(checkin.count) AS total_checkins FROM yelp.business JOIN yelp.checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' GROUP BY business.business_id, business.bid ORDER BY total_checkins DESC;SELECT business.name, business.bid, SUM(checkin.count) AS total_checkins FROM yelp.business JOIN yelp.checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' GROUP BY business.name, business.bid ORDER BY total_checkins DESC;SELECT business.business_id, business.name, business.bid, SUM(checkin.count) AS total_checkins FROM yelp.business JOIN yelp.checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' GROUP BY business.business_id, business.name, business.bid ORDER BY total_checkins DESC;",What is the total number of check-ins for each business in the state of California?,
yelp,bigquery,table_join,"SELECT category.category_name FROM (SELECT business.business_id, AVG(review.rating) AS average_rating FROM yelp.business JOIN yelp.review ON business.business_id = review.business_id GROUP BY business.business_id) AS business_rating JOIN yelp.category ON business_rating.business_id = category.business_id GROUP BY category.category_name ORDER BY AVG(business_rating.average_rating) DESC LIMIT 2;SELECT category.id FROM (SELECT business.business_id, AVG(review.rating) AS average_rating FROM yelp.business JOIN yelp.review ON business.business_id = review.business_id GROUP BY business.business_id) AS business_rating JOIN yelp.category ON business_rating.business_id = category.business_id GROUP BY category.id ORDER BY AVG(business_rating.average_rating) DESC LIMIT 2;SELECT category.category_name, category.id FROM (SELECT business.business_id, AVG(review.rating) AS average_rating FROM yelp.business JOIN yelp.review ON business.business_id = review.business_id GROUP BY business.business_id) AS business_rating JOIN yelp.category ON business_rating.business_id = category.business_id GROUP BY category.category_name, category.id ORDER BY AVG(business_rating.average_rating) DESC LIMIT 2;",What are the top 2 categories of businesses with the highest average rating?,
yelp,bigquery,instruct,SELECT COUNT(review.rid) AS total_reviews FROM yelp.review JOIN yelp.category ON review.business_id = category.business_id WHERE review.year = 2021 AND LOWER(category.category_name) LIKE LOWER('%Cafe%');,"What is the total number of reviews posted in the year 2021 for businesses in the category ""Cafe""?","Filter strings of users, city, address, business.name using LIKE with wildcards.
Filter strings of state using exact upper case matches.
Assume the rating of a business to be its average rating, and compute it before computing other aggregates on it.
"
yelp,bigquery,instruct,"SELECT AVG(sf.average_rating) AS sf_average_rating FROM (SELECT business.business_id, AVG(review.rating) AS average_rating FROM yelp.business JOIN yelp.review ON business.business_id = review.business_id WHERE LOWER(LOWER(business.city)) LIKE LOWER('%san francisco%') GROUP BY business.business_id) AS sf;",What is the average rating of businesses in the city of San Francisco?,"Filter strings of users, city, address, business.name using LIKE with wildcards.
Filter strings of state using exact upper case matches.
The rating of businesses in a city refers to the average rating of the businesses in that city. I.e., you must compute the average rating of each business before computing the average rating of businesses in the city.
"
yelp,bigquery,instruct,"SELECT review.business_id, COUNT(*) AS review_count FROM yelp.review WHERE review.year = 2021 GROUP BY review.business_id ORDER BY review_count DESC;",How many reviews were posted for each business id in the year 2021?,"Filter strings of users, city, address, business.name using LIKE with wildcards.
Filter strings of state using exact upper case matches.
Assume the rating of a business to be its average rating, and compute it before computing other aggregates on it.
"
yelp,bigquery,instruct,SELECT COUNT(*) FROM yelp.review JOIN yelp.users ON review.user_id = users.user_id WHERE LOWER(users.name) LIKE LOWER('%Sarah Williams%') AND review.month = 'April' AND review.year = 2021;,"How many reviews were posted by users with the name ""Sarah Williams"" in the month of April 2021?","Filter strings of users, city, address, business.name using LIKE with wildcards.
Filter strings of state using exact upper case matches.
Assume the rating of a business to be its average rating, and compute it before computing other aggregates on it.
"
yelp,bigquery,instruct,SELECT SUM(checkin.count) AS total_checkins FROM yelp.business JOIN yelp.checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' AND LOWER(checkin.day) LIKE LOWER('%Monday%');,How many check-ins occurred on Mondays at businesses in the state of California?,"Filter strings of users, city, address, business.name using LIKE with wildcards.
Filter strings of state using exact upper case matches.
Assume the rating of a business to be its average rating, and compute it before computing other aggregates on it.
"
broker,bigquery,date_functions,"WITH SellTransactions AS (SELECT sbTxCustId, COUNT(*) AS num_tx FROM broker.sbTransaction WHERE CAST(sbTxDateTime AS DATE) = '2023-04-01' AND sbTxType = 'sell' GROUP BY sbTxCustId) SELECT c.sbCustId, c.sbCustName, st.num_tx FROM broker.sbCustomer AS c JOIN SellTransactions AS st ON c.sbCustId = st.sbTxCustId ORDER BY st.num_tx DESC NULLS FIRST LIMIT 1;","Return the customer who made the most sell transactions on 2023-04-01. Return the id, name and number of transactions.",
broker,bigquery,date_functions,"SELECT TIMESTAMP_TRUNC(sbTxDateTime, MONTH) AS MONTH, AVG(sbTxPrice) AS avg_price FROM broker.sbTransaction WHERE sbTxStatus = 'success' AND sbTxDateTime BETWEEN '2023-01-01' AND '2023-03-31' GROUP BY MONTH ORDER BY MONTH NULLS LAST;SELECT CAST(TIMESTAMP_TRUNC(sbTxDateTime, MONTH) AS DATE) AS MONTH, AVG(sbTxPrice) AS avg_price FROM broker.sbTransaction WHERE sbTxStatus = 'success' AND sbTxDateTime BETWEEN '2023-01-01' AND '2023-03-31' GROUP BY MONTH ORDER BY MONTH NULLS LAST;",What is the monthly average transaction price for successful transactions in the 1st quarter of 2023?,
broker,bigquery,date_functions,SELECT MIN(sdp.sbDpClose) AS lowest_price FROM broker.sbDailyPrice AS sdp JOIN broker.sbTicker AS st ON sdp.sbDpTickerId = st.sbTickerId WHERE st.sbTickerSymbol = 'VTI' AND sdp.sbDpDate >= CURRENT_DATE - INTERVAL '7' DAY;,Lowest daily closest price for symbol `VTI` in the past 7 days,
broker,bigquery,date_functions,"SELECT c.sbCustId, DATE_DIFF(MIN(t.sbTxDateTime), c.sbCustJoinDate, DAY) AS DaysFromJoinToFirstTransaction FROM broker.sbCustomer AS c JOIN broker.sbTransaction AS t ON c.sbCustId = t.sbTxCustId GROUP BY c.sbCustId, c.sbCustJoinDate;",Return the customer id and the difference between their time from joining to their first transaction. Ignore customers who haven't made any transactions.,
broker,bigquery,date_functions,SELECT COUNT(t.sbTxCustId) AS transaction_count FROM broker.sbTransaction AS t JOIN broker.sbCustomer AS c ON t.sbTxCustId = c.sbCustId WHERE c.sbCustJoinDate >= CURRENT_DATE - INTERVAL '70' DAY;,number of transactions by users who joined in the past 70 days,
derm_treatment,bigquery,date_functions,"SELECT t.treatment_id, t.start_dt, ae.reported_dt, ae.description FROM derm_treatment.adverse_events AS ae JOIN derm_treatment.treatments AS t ON ae.treatment_id = t.treatment_id WHERE ae.reported_dt BETWEEN t.start_dt AND t.start_dt + INTERVAL '10' DAY;","Return the treatment id, treatment start date, adverse event date and description of all adverse events that occured within 10 days after starting treatment",
derm_treatment,bigquery,date_functions,"WITH doc_first_treatment AS (SELECT d.doc_id, d.last_name, d.year_reg, t.treatment_id, t.start_dt, ROW_NUMBER() OVER (PARTITION BY d.doc_id ORDER BY t.start_dt ASC NULLS LAST) AS rn FROM derm_treatment.doctors AS d JOIN derm_treatment.treatments AS t ON d.doc_id = t.doc_id WHERE d.year_reg = EXTRACT(YEAR FROM CURRENT_DATE) - 2) SELECT last_name, year_reg, start_dt AS first_treatment_date, treatment_id AS first_treatment_id FROM doc_first_treatment WHERE rn = 1;","List the last name, year of registration, and first treatment (date and id) by doctors who were registered 2 years ago.",
derm_treatment,bigquery,date_functions,"SELECT AVG(DATE_DIFF(CURRENT_DATE(), date_of_birth, YEAR)) AS avg_age FROM derm_treatment.patients WHERE gender = 'Male' AND ins_type = 'private';",what is average age of all registered male patients with private insurance currently?,
derm_treatment,bigquery,date_functions,"SELECT t.treatment_id, t.start_dt AS treatment_start_date, t.end_dt AS treatment_end_date, cm.start_dt AS concomitant_med_start_date, cm.end_dt AS concomitant_med_end_date FROM derm_treatment.treatments AS t JOIN derm_treatment.concomitant_meds AS cm ON t.treatment_id = cm.treatment_id WHERE t.is_placebo = TRUE AND CAST(cm.start_dt AS DATE) BETWEEN t.start_dt AND DATE_ADD(t.start_dt, INTERVAL 14 DAY) ORDER BY t.treatment_id NULLS LAST;","show all placebo treatment id, start and end date, where there concomitant_meds were started within 2 weeks of starting the treatment. also return the start and end dates of all concomitant drug usage.",
derm_treatment,bigquery,date_functions,"SELECT COUNT(*) FROM derm_treatment.treatments t JOIN derm_treatment.diagnoses d ON t.diag_id = d.diag_id JOIN derm_treatment.drugs dr ON t.drug_id = dr.drug_id WHERE LOWER(d.diag_name) LIKE LOWER('%psoriasis%') AND dr.fda_appr_dt IS NOT NULL AND t.end_dt >= DATE_SUB(CURRENT_DATE(), INTERVAL 6 MONTH);",How many treatments for diagnoses containing 'psoriasis' (match with wildcards case-insensitively) involve drugs that have been FDA-approved and the treatments have ended within the last 6 months from today?,
ewallet,bigquery,date_functions,"SELECT AVG(tx_limit_daily) AS avg_daily_limit, AVG(tx_limit_monthly) AS avg_monthly_limit FROM ewallet.user_setting_snapshot WHERE snapshot_date = (SELECT MIN(snapshot_date) FROM ewallet.user_setting_snapshot WHERE snapshot_date >= '2023-01-01' AND snapshot_date < '2024-01-01');",What was the average transaction daily and monthly limit for the earliest setting snapshot in 2023?,
ewallet,bigquery,date_functions,"SELECT u.username, u.email, u.created_at FROM ewallet.users AS u LEFT JOIN ewallet.notifications AS n ON u.uid = n.user_id AND n.created_at BETWEEN u.created_at AND u.created_at + INTERVAL '1' YEAR WHERE n.user_id IS NULL;","Which users did not get a notification within the first year of signing up? Return their usernames, emails and signup dates.",
ewallet,bigquery,date_functions,"SELECT device_type, AVG(TIMESTAMP_DIFF(session_end_ts, session_start_ts, SECOND)) AS avg_session_duration_seconds FROM ewallet.user_sessions WHERE session_end_ts IS NOT NULL GROUP BY device_type;",what was the average user session duration in seconds split by device_type?,
ewallet,bigquery,date_functions,"WITH retail_merchants AS (SELECT mid FROM ewallet.merchants WHERE LOWER(category) LIKE LOWER('%retail%') AND status = 'active'), merchant_balances AS (SELECT balance FROM ewallet.wallet_merchant_balance_daily AS wmbd JOIN retail_merchants AS rm ON wmbd.merchant_id = rm.mid WHERE DATE(wmbd.updated_at) = CURRENT_DATE()) SELECT DISTINCT PERCENTILE_CONT(balance, 0.5) OVER() AS median_balance FROM merchant_balances;",Give me today's median merchant wallet balance for all active merchants whose category contains 'retail',
ewallet,bigquery,date_functions,"WITH earliest_coupons AS (SELECT c.merchant_id, MIN(c.start_date) AS earliest_coupon_start_date FROM ewallet.coupons AS c GROUP BY c.merchant_id) SELECT m.mid AS merchant_id, m.created_at AS merchant_registration_date, ec.earliest_coupon_start_date, c.cid AS earliest_coupon_id FROM ewallet.merchants AS m JOIN earliest_coupons AS ec ON m.mid = ec.merchant_id JOIN ewallet.coupons AS c ON ec.merchant_id = c.merchant_id AND ec.earliest_coupon_start_date = c.start_date WHERE ec.earliest_coupon_start_date <= m.created_at + INTERVAL '1' YEAR;","Which merchants earliest coupon start date was within a year of the merchant's registration? Return the merchant id, registration date, and earliest coupon id and start date",
car_dealership,bigquery,date_functions,"SELECT s.first_name, s.last_name, s.phone, s.termination_date - s.hire_date AS days_employed FROM car_dealership.salespersons AS s ORDER BY days_employed ASC NULLS LAST LIMIT 1;",Return the name and phone number of the salesperson with the shortest time from being hired to getting fired. Return the number of days he/she was employed for.,
car_dealership,bigquery,date_functions,"SELECT COUNT(*) AS weekend_payments FROM car_dealership.payments_made WHERE vendor_name = 'Utility Company' AND EXTRACT(DAYOFWEEK FROM payment_date) IN (1, 7);",Return the number of payments made on weekends to the vendor named 'Utility Company',
car_dealership,bigquery,date_functions,"SELECT payment_date, payment_method, SUM(payment_amount) AS total_amount FROM car_dealership.payments_received WHERE payment_date >= TIMESTAMP_TRUNC(CURRENT_DATE, WEEK) - INTERVAL '1' WEEK AND payment_date < TIMESTAMP_TRUNC(CURRENT_DATE, WEEK) GROUP BY payment_date, payment_method ORDER BY payment_date DESC NULLS FIRST, payment_method ASC NULLS LAST;","show me the daily total amount of payments received in the whole of the previous ISO week not including the current week, split by the payment_method",
car_dealership,bigquery,date_functions,"WITH latest_snapshot AS (SELECT MAX(snapshot_date) AS snapshot_date FROM car_dealership.inventory_snapshots WHERE snapshot_date BETWEEN '2023-03-01' AND '2023-03-31'), latest_snapshot_data AS (SELECT inv.car_id FROM car_dealership.inventory_snapshots AS inv JOIN latest_snapshot AS ls ON inv.snapshot_date = ls.snapshot_date WHERE inv.is_in_inventory = TRUE) SELECT c.id, c.make, c.model, c.year FROM car_dealership.cars AS c JOIN latest_snapshot_data AS lsd ON c.id = lsd.car_id;","Which cars were in inventory in the latest snapshot for march 2023? Return the car id, make, model, and year. Cars are considered to be "in inventory" if is_in_inventory is True.",
car_dealership,bigquery,date_functions,"SELECT TIMESTAMP_TRUNC(s.sale_date, QUARTER) AS QUARTER, c.state, SUM(s.sale_price) AS total_sales FROM car_dealership.sales AS s JOIN car_dealership.customers AS c ON s.customer_id = c.id WHERE EXTRACT(YEAR FROM s.sale_date) = 2023 GROUP BY c.state, QUARTER HAVING SUM(s.sale_price) > 0 ORDER BY QUARTER NULLS LAST, c.state NULLS LAST;",What were the total quarterly sales in 2023 grouped by customer's state? Represent each quarter as the first date in the quarter.,
