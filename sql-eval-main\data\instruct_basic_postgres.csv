db_name,query_category,question,query
broker,basic_join_date_group_order_limit,"What are the top 5 countries by total transaction amount in the past 30 days, inclusive of 30 days ago? Return the country name, number of transactions and total transaction amount.","SELECT c.sbCustCountry, COUNT(t.sbTxId) AS num_transactions, SUM(t.sbTxAmount) AS total_amount FROM sbCustomer c JOIN sbTransaction t ON c.sbCustId = t.sbTxCustId WHERE t.sbTxDateTime >= CURRENT_DATE - INTERVAL '30 days' GROUP BY c.sbCustCountry ORDER BY total_amount DESC LIMIT 5"
broker,basic_join_date_group_order_limit,"How many distinct customers made each type of transaction between Jan 1, 2023 and Mar 31, 2023 (inclusive of start and end dates)? Return the transaction type, number of distinct customers and average number of shares, for the top 3 transaction types by number of customers.","SELECT t.sbTxType, COUNT(DISTINCT t.sbTxCustId) AS num_customers, AVG(t.sbTxShares) AS avg_shares FROM sbTransaction t WHERE t.sbTxDateTime BETWEEN '2023-01-01' AND '2023-03-31 23:59:59' GROUP BY t.sbTxType ORDER BY num_customers DESC LIMIT 3"
broker,basic_join_group_order_limit,"What are the top 10 ticker symbols by total transaction amount? Return the ticker symbol, number of transactions and total transaction amount.","SELECT tk.sbTickerSymbol, COUNT(tx.sbTxId) AS num_transactions, SUM(tx.sbTxAmount) AS total_amount FROM sbTicker tk JOIN sbTransaction tx ON tk.sbTickerId = tx.sbTxTickerId GROUP BY tk.sbTickerSymbol ORDER BY total_amount DESC LIMIT 10"
broker,basic_join_group_order_limit,"What are the top 5 combinations of customer state and ticker type by number of transactions? Return the customer state, ticker type and number of transactions.","SELECT c.sbCustState, t.sbTickerType, COUNT(*) AS num_transactions FROM sbTransaction tx JOIN sbCustomer c ON tx.sbTxCustId = c.sbCustId JOIN sbTicker t ON tx.sbTxTickerId = t.sbTickerId GROUP BY c.sbCustState, t.sbTickerType ORDER BY num_transactions DESC LIMIT 5"
broker,basic_join_distinct,Return the distinct list of customer IDs who have made a 'buy' transaction.,SELECT DISTINCT c.sbCustId FROM sbCustomer c JOIN sbTransaction t ON c.sbCustId = t.sbTxCustId WHERE t.sbTxType = 'buy'
broker,basic_join_distinct,"Return the distinct list of ticker IDs that have daily price records on or after Apr 1, 2023.",SELECT DISTINCT tk.sbTickerId FROM sbTicker tk JOIN sbDailyPrice dp ON tk.sbTickerId = dp.sbDpTickerId WHERE dp.sbDpDate >= '2023-04-01'
broker,basic_group_order_limit,What are the top 3 transaction statuses by number of transactions? Return the status and number of transactions.,"SELECT sbTxStatus, COUNT(*) AS num_transactions FROM sbTransaction GROUP BY sbTxStatus ORDER BY num_transactions DESC LIMIT 3"
broker,basic_group_order_limit,What are the top 5 countries by number of customers? Return the country name and number of customers.,"SELECT sbCustCountry, COUNT(*) AS num_customers FROM sbCustomer GROUP BY sbCustCountry ORDER BY num_customers DESC LIMIT 5"
broker,basic_left_join,Return the customer ID and name of customers who have not made any transactions.,"SELECT c.sbCustId, c.sbCustName FROM sbCustomer c LEFT JOIN sbTransaction t ON c.sbCustId = t.sbTxCustId WHERE t.sbTxCustId IS NULL"
broker,basic_left_join,Return the ticker ID and symbol of tickers that do not have any daily price records.,"SELECT tk.sbTickerId, tk.sbTickerSymbol FROM sbTicker tk LEFT JOIN sbDailyPrice dp ON tk.sbTickerId = dp.sbDpTickerId WHERE dp.sbDpTickerId IS NULL"
car_dealership,basic_join_date_group_order_limit,"Who were the top 3 sales representatives by total revenue in the past 3 months, inclusive of today's date? Return their first name, last name, total number of sales and total revenue. Note that revenue refers to the sum of sale_price in the sales table.","SELECT c.first_name, c.last_name, COUNT(s.id) AS total_sales, SUM(s.sale_price) AS total_revenue FROM sales s JOIN salespersons c ON s.salesperson_id = c.id WHERE s.sale_date >= CURRENT_DATE - INTERVAL '3 months' GROUP BY c.first_name, c.last_name ORDER BY total_revenue DESC LIMIT 3"
car_dealership,basic_join_date_group_order_limit,"Return the top 5 salespersons by number of sales in the past 30 days? Return their first and last name, total sales count and total revenue amount.","SELECT sp.first_name, sp.last_name, COUNT(s.id) AS total_sales, SUM(s.sale_price) AS total_revenue FROM sales s JOIN salespersons sp ON s.salesperson_id = sp.id WHERE s.sale_date >= CURRENT_DATE - INTERVAL '30 days' GROUP BY sp.first_name, sp.last_name, sp.id ORDER BY total_sales DESC LIMIT 5"
car_dealership,basic_join_group_order_limit,"Return the top 5 states by total revenue, showing the number of unique customers and total revenue (based on sale price) for each state.","SELECT c.state, COUNT(DISTINCT s.customer_id) AS unique_customers, SUM(s.sale_price) AS total_revenue FROM sales s JOIN customers c ON s.customer_id = c.id GROUP BY c.state ORDER BY total_revenue DESC LIMIT 5"
car_dealership,basic_join_group_order_limit,"What are the top 5 best selling car models by total revenue? Return the make, model, total number of sales and total revenue.","SELECT c.make, c.model, COUNT(s.id) AS total_sales, SUM(s.sale_price) AS total_revenue FROM sales s JOIN cars c ON s.car_id = c.id GROUP BY c.make, c.model ORDER BY total_revenue DESC LIMIT 5"
car_dealership,basic_join_distinct,"Return the distinct list of customer IDs that have made a purchase, based on joining the customers and sales tables.",SELECT DISTINCT c.id AS customer_id FROM customers c JOIN sales s ON c.id = s.customer_id
car_dealership,basic_join_distinct,"Return the distinct list of salesperson IDs that have received a cash payment, based on joining the salespersons, sales and payments_received tables.",SELECT DISTINCT s.id AS salesperson_id FROM salespersons s JOIN sales sa ON s.id = sa.salesperson_id JOIN payments_received p ON sa.id = p.sale_id WHERE p.payment_method = 'cash'
car_dealership,basic_group_order_limit,"What are the top 3 payment methods by total payment amount received? Return the payment method, total number of payments and total amount.","SELECT payment_method, COUNT(*) AS total_payments, SUM(payment_amount) AS total_amount FROM payments_received GROUP BY payment_method ORDER BY total_amount DESC LIMIT 3"
car_dealership,basic_group_order_limit,"What are the total number of customer signups for the top 2 states? Return the state and total signups, starting from the top.","SELECT state, COUNT(*) AS total_signups FROM customers GROUP BY state ORDER BY total_signups DESC LIMIT 2"
car_dealership,basic_left_join,"Return the car ID, make, model and year for cars that have no sales records, by doing a left join from the cars to sales table.","SELECT c.id AS car_id, c.make, c.model, c.year FROM cars c LEFT JOIN sales s ON c.id = s.car_id WHERE s.car_id IS NULL"
car_dealership,basic_left_join,"Return the salesperson ID, first name and last name for salespersons that have no sales records, by doing a left join from the salespersons to sales table.","SELECT s.id AS salesperson_id, s.first_name, s.last_name FROM salespersons s LEFT JOIN sales sa ON s.id = sa.salesperson_id WHERE sa.salesperson_id IS NULL"
derm_treatment,basic_join_date_group_order_limit,"What are the top 3 doctor specialties by total drug amount prescribed for treatments started in the past 6 calendar months? Return the specialty, number of treatments, and total drug amount.","SELECT d.specialty, COUNT(*) AS num_treatments, SUM(t.tot_drug_amt) AS total_drug_amt FROM treatments t JOIN doctors d ON t.doc_id = d.doc_id WHERE t.start_dt >= DATE_TRUNC('month', CURRENT_DATE - INTERVAL '6 months') GROUP BY d.specialty ORDER BY total_drug_amt DESC LIMIT 3"
derm_treatment,basic_join_date_group_order_limit,"For treatments that ended in the year 2022 (from Jan 1st to Dec 31st inclusive), what is the average PASI score at day 100 and number of distinct patients per insurance type? Return the top 5 insurance types sorted by lowest average PASI score first.","SELECT p.ins_type, COUNT(DISTINCT t.patient_id) AS num_patients, AVG(o.day100_pasi_score) AS avg_pasi_score FROM treatments t JOIN patients p ON t.patient_id = p.patient_id JOIN outcomes o ON t.treatment_id = o.treatment_id WHERE t.end_dt BETWEEN '2022-01-01' AND '2022-12-31' GROUP BY p.ins_type ORDER BY avg_pasi_score LIMIT 5"
derm_treatment,basic_join_group_order_limit,"What are the top 5 drugs by number of treatments and average drug amount per treatment? Return the drug name, number of treatments, and average drug amount.","SELECT d.drug_name, COUNT(*) AS num_treatments, AVG(t.tot_drug_amt) AS avg_drug_amt FROM treatments t JOIN drugs d ON t.drug_id = d.drug_id GROUP BY d.drug_name ORDER BY num_treatments DESC, avg_drug_amt DESC LIMIT 5"
derm_treatment,basic_join_group_order_limit,"What are the top 3 diagnoses by maximum itch VAS score at day 100 and number of distinct patients? Return the diagnosis name, number of patients, and maximum itch score.","SELECT di.diag_name, COUNT(DISTINCT t.patient_id) AS num_patients, MAX(o.day100_itch_vas) AS max_itch_score FROM treatments t JOIN diagnoses di ON t.diag_id = di.diag_id JOIN outcomes o ON t.treatment_id = o.treatment_id GROUP BY di.diag_name ORDER BY max_itch_score DESC, num_patients DESC LIMIT 3"
derm_treatment,basic_join_distinct,"Return the distinct list of doctor IDs, first names and last names that have prescribed treatments.","SELECT DISTINCT d.doc_id, d.first_name, d.last_name FROM treatments t JOIN doctors d ON t.doc_id = d.doc_id"
derm_treatment,basic_join_distinct,"Return the distinct list of patient IDs, first names and last names that have outcome assessments.","SELECT DISTINCT p.patient_id, p.first_name, p.last_name FROM outcomes o JOIN treatments t ON o.treatment_id = t.treatment_id JOIN patients p ON t.patient_id = p.patient_id"
derm_treatment,basic_group_order_limit,"What are the top 3 insurance types by average patient height in cm? Return the insurance type, average height and average weight.","SELECT ins_type, AVG(height_cm) AS avg_height, AVG(weight_kg) AS avg_weight FROM patients GROUP BY ins_type ORDER BY avg_height DESC LIMIT 3"
derm_treatment,basic_group_order_limit,What are the top 2 specialties by number of doctors? Return the specialty and number of doctors.,"SELECT specialty, COUNT(*) AS num_doctors FROM doctors GROUP BY specialty ORDER BY num_doctors DESC LIMIT 2"
derm_treatment,basic_left_join,"Return the patient IDs, first names and last names of patients who have not received any treatments.","SELECT p.patient_id, p.first_name, p.last_name FROM patients p LEFT JOIN treatments t ON p.patient_id = t.patient_id WHERE t.patient_id IS NULL"
derm_treatment,basic_left_join,Return the drug IDs and names of drugs that have not been used in any treatments.,"SELECT d.drug_id, d.drug_name FROM drugs d LEFT JOIN treatments t ON d.drug_id = t.drug_id WHERE t.drug_id IS NULL"
ewallet,basic_join_date_group_order_limit,"Who are the top 2 merchants (receiver type 1) by total transaction amount in the past 150 days (inclusive of 150 days ago)? Return the merchant name, total number of transactions, and total transaction amount.","SELECT m.name AS merchant_name, COUNT(t.txid) AS total_transactions, SUM(t.amount) AS total_amount FROM consumer_div.merchants m JOIN consumer_div.wallet_transactions_daily t ON m.mid = t.receiver_id WHERE t.receiver_type = 1 AND t.created_at >= CURRENT_DATE - INTERVAL '150 days' GROUP BY m.name ORDER BY total_amount DESC LIMIT 2"
ewallet,basic_join_date_group_order_limit,"How many distinct active users sent money per month in 2023? Return the number of active users per month (as a date), starting from the earliest date. Do not include merchants in the query. Only include successful transactions.","SELECT DATE_TRUNC('month', t.created_at) AS MONTH, COUNT(DISTINCT t.sender_id) AS active_users FROM consumer_div.wallet_transactions_daily t JOIN consumer_div.users u ON t.sender_id = u.uid WHERE t.sender_type = 0 AND t.status = 'success' AND u.status = 'active' AND t.created_at >= '2023-01-01' AND t.created_at < '2024-01-01' GROUP BY MONTH ORDER BY MONTH;SELECT DATE_TRUNC('month', w.created_at)::DATE AS MONTH, COUNT(DISTINCT w.sender_id) AS active_user_count FROM consumer_div.wallet_transactions_daily w JOIN consumer_div.users u ON w.sender_id = u.uid WHERE w.sender_type = 0 AND w.status = 'success' AND w.created_at >= '2023-01-01' AND w.created_at < '2024-01-01' AND u.status = 'active' GROUP BY DATE_TRUNC('month', w.created_at) ORDER BY MONTH ASC;"
ewallet,basic_join_group_order_limit,"What are the top 3 most frequently used coupon codes? Return the coupon code, total number of redemptions, and total amount redeemed.","SELECT c.code AS coupon_code, COUNT(t.txid) AS redemption_count, SUM(t.amount) AS total_discount FROM consumer_div.coupons c JOIN consumer_div.wallet_transactions_daily t ON c.cid = t.coupon_id GROUP BY c.code ORDER BY redemption_count DESC LIMIT 3"
ewallet,basic_join_group_order_limit,"Which are the top 5 countries by total transaction amount sent by users, sender_type = 0? Return the country, number of distinct users who sent, and total transaction amount.","SELECT u.country, COUNT(DISTINCT t.sender_id) AS user_count, SUM(t.amount) AS total_amount FROM consumer_div.users u JOIN consumer_div.wallet_transactions_daily t ON u.uid = t.sender_id WHERE t.sender_type = 0 GROUP BY u.country ORDER BY total_amount DESC LIMIT 5"
ewallet,basic_join_distinct,"Return the distinct list of merchant IDs that have received money from a transaction. Consider all transaction types in the results you return, but only include the merchant ids in your final answer.",SELECT DISTINCT m.mid AS merchant_id FROM consumer_div.merchants m JOIN consumer_div.wallet_transactions_daily t ON m.mid = t.receiver_id WHERE t.receiver_type = 1
ewallet,basic_join_distinct,Return the distinct list of user IDs who have received transaction notifications.,SELECT DISTINCT user_id FROM consumer_div.notifications WHERE type = 'transaction'
ewallet,basic_group_order_limit,What are the top 3 most common transaction statuses and their respective counts?,"SELECT status, COUNT(*) AS COUNT FROM consumer_div.wallet_transactions_daily GROUP BY status ORDER BY COUNT DESC LIMIT 3"
ewallet,basic_group_order_limit,What are the top 2 most frequently used device types for user sessions and their respective counts?,"SELECT device_type, COUNT(*) AS COUNT FROM consumer_div.user_sessions GROUP BY device_type ORDER BY COUNT DESC LIMIT 2"
ewallet,basic_left_join,Return users (user ID and username) who have not received any notifications,"SELECT u.uid, u.username FROM consumer_div.users u LEFT JOIN consumer_div.notifications n ON u.uid = n.user_id WHERE n.id IS NULL"
ewallet,basic_left_join,Return merchants (merchant ID and name) who have not issued any coupons.,"SELECT m.mid AS merchant_id, m.name AS merchant_name FROM consumer_div.merchants m LEFT JOIN consumer_div.coupons c ON m.mid = c.merchant_id WHERE c.cid IS NULL"
