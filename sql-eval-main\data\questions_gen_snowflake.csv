question,query,db_name,query_category,k_shot_prompt
Which conference published the most publications in the last 15 years? Give the conference name and publication count.,"SELECT conference.name, count(publication.pid) AS publication_count FROM publication JOIN conference ON publication.cid = conference.cid WHERE publication.year >= extract(YEAR FROM CURRENT_DATE) - 15 GROUP BY conference.name ORDER BY publication_count DESC LIMIT 1;",academic,date_functions,"EXAMPLE QUESTION 1: How many publications were presented at each conference, ordered by the number of publications in descending order? Give the names of the conferences and their corresponding number of publications.
EXAMPLE QUERY 1: SELECT conference.name, COUNT(publication.pid) AS num_publications FROM publication JOIN conference ON publication.cid=conference.cid GROUP BY conference.name, conference.cid ORDER BY num_publications DESC NULLS LAST

EXAMPLE QUESTION 2: What is the total number of publications presented in each conference?
EXAMPLE QUERY 2: SELECT conference.name, COUNT(publication.pid) AS total_publications FROM publication JOIN conference ON publication.cid = conference.cid GROUP BY conference.name ORDER BY total_publications DESC

"
How many publications were published between 2019 and 2021?,SELECT count(DISTINCT publication.pid) FROM publication WHERE publication.year BETWEEN 2019 AND 2021;,academic,date_functions,"EXAMPLE QUESTION 1: What is the total number of publications published in each year?
EXAMPLE QUERY 1: SELECT publication.year, COUNT(DISTINCT publication.pid) AS total_publications FROM publication GROUP BY publication.year ORDER BY publication.year

EXAMPLE QUESTION 2: Which author had the most publications in the year 2021 and how many publications did he/she have that year?
EXAMPLE QUERY 2: SELECT author.name, COUNT(publication.pid) AS publication_count FROM writes JOIN author ON writes.aid = author.aid JOIN publication ON writes.pid = publication.pid WHERE publication.year = 2021 GROUP BY author.name ORDER BY publication_count DESC NULLS LAST LIMIT 1

"
What is the average number of citations received by publications in the last 5 years?,SELECT avg(publication.citation_num) FROM publication WHERE publication.year >= extract(YEAR FROM CURRENT_DATE) - 5;,academic,date_functions,"EXAMPLE QUESTION 1: What is the total number of citations received by each author?
EXAMPLE QUERY 1: SELECT author.name, sum(publication.citation_num) AS total_citations FROM author JOIN writes ON author.aid = writes.aid JOIN publication ON writes.pid = publication.pid GROUP BY author.name ORDER BY total_citations DESC NULLS LAST

EXAMPLE QUESTION 2: What is the average number of references cited by publications in each domain name?
EXAMPLE QUERY 2: SELECT domain.name, AVG(publication.reference_num) AS average_references FROM domain_publication JOIN publication ON domain_publication.pid = publication.pid JOIN DOMAIN ON domain.did = domain_publication.did GROUP BY domain.name

"
Which authors have published papers in journals within the past 20 years?,"SELECT DISTINCT {author.name, author.aid} FROM author JOIN writes ON author.aid = writes.aid JOIN publication ON writes.pid = publication.pid WHERE publication.year >= extract(YEAR FROM CURRENT_DATE) - 20;",academic,date_functions,"EXAMPLE QUESTION 1: Which author had the most publications in the year 2021 and how many publications did he/she have that year?
EXAMPLE QUERY 1: SELECT author.name, COUNT(publication.pid) AS publication_count FROM writes JOIN author ON writes.aid = author.aid JOIN publication ON writes.pid = publication.pid WHERE publication.year = 2021 GROUP BY author.name ORDER BY publication_count DESC NULLS LAST LIMIT 1

EXAMPLE QUESTION 2: How many publications were published between 2019 and 2021?
EXAMPLE QUERY 2: SELECT count(DISTINCT publication.pid) FROM publication WHERE publication.year BETWEEN 2019 AND 2021

"
What's the difference in time between the first and last paper published?,SELECT max(YEAR) - min(YEAR) AS time_difference FROM publication;,academic,date_functions,"EXAMPLE QUESTION 1: Which authors have published papers in journals within the past 20 years?
EXAMPLE QUERY 1: SELECT DISTINCT author.name FROM author JOIN writes ON author.aid = writes.aid JOIN publication ON writes.pid = publication.pid WHERE publication.year >= extract(YEAR FROM CURRENT_DATE) - 20

EXAMPLE QUESTION 2: How many publications were published between 2019 and 2021?
EXAMPLE QUERY 2: SELECT count(DISTINCT publication.pid) FROM publication WHERE publication.year BETWEEN 2019 AND 2021

"
"Which authors have written publications in both the domain ""Machine Learning"" and the domain ""Data Science""?","SELECT {author.name,author.aid} FROM author WHERE author.aid IN (SELECT domain_author.aid FROM domain_author WHERE domain_author.did IN (SELECT domain.did FROM DOMAIN WHERE domain.name IN ('Machine Learning', 'Data Science') ) GROUP BY 1 HAVING COUNT(DISTINCT domain_author.did) = 2);",academic,group_by,"EXAMPLE QUESTION 1: Which organizations have authors who have written publications in the domain ""Machine Learning""?
EXAMPLE QUERY 1: SELECT DISTINCT organization.name FROM organization JOIN author ON organization.oid = author.oid JOIN writes ON author.aid = writes.aid JOIN domain_publication ON writes.pid = domain_publication.pid JOIN DOMAIN ON domain_publication.did = domain.did WHERE domain.name ILIKE '%Machine Learning%'

EXAMPLE QUESTION 2: What are the names of the authors who have written publications in the domain ""Computer Science""?
EXAMPLE QUERY 2: SELECT DISTINCT author.name FROM author JOIN writes ON author.aid = writes.aid JOIN publication ON writes.pid = publication.pid JOIN domain_publication ON publication.pid = domain_publication.pid JOIN DOMAIN ON domain_publication.did = domain.did WHERE domain.name ilike '%computer%science%'

"
What is the total number of citations received by each author?,"SELECT {author.name, author.aid}, sum(publication.citation_num) AS total_citations FROM author JOIN writes ON author.aid = writes.aid JOIN publication ON writes.pid = publication.pid GROUP BY {} ORDER BY total_citations DESC NULLS LAST;",academic,group_by,"EXAMPLE QUESTION 1: What is the average number of citations received by publications in the last 5 years?
EXAMPLE QUERY 1: SELECT avg(publication.citation_num) FROM publication WHERE publication.year >= extract(YEAR FROM CURRENT_DATE) - 5

EXAMPLE QUESTION 2: What is the average number of citations received by publications in each year?
EXAMPLE QUERY 2: SELECT publication.year, AVG(publication.citation_num) AS average_citations FROM publication GROUP BY publication.year ORDER BY publication.year NULLS LAST

"
What is the total number of publications published in each year?,"SELECT publication.year, COUNT(DISTINCT publication.pid) AS total_publications FROM publication GROUP BY publication.year ORDER BY publication.year;",academic,group_by,"EXAMPLE QUESTION 1: How many publications were published between 2019 and 2021?
EXAMPLE QUERY 1: SELECT count(DISTINCT publication.pid) FROM publication WHERE publication.year BETWEEN 2019 AND 2021

EXAMPLE QUESTION 2: Which author had the most publications in the year 2021 and how many publications did he/she have that year?
EXAMPLE QUERY 2: SELECT author.name, COUNT(publication.pid) AS publication_count FROM writes JOIN author ON writes.aid = author.aid JOIN publication ON writes.pid = publication.pid WHERE publication.year = 2021 GROUP BY author.name ORDER BY publication_count DESC NULLS LAST LIMIT 1

"
What is the average number of references cited by publications in each domain name?,"SELECT {domain.name,domain.did}, AVG(publication.reference_num) AS average_references FROM domain_publication JOIN publication ON domain_publication.pid = publication.pid JOIN DOMAIN ON domain.did = domain_publication.did GROUP BY {};",academic,group_by,"EXAMPLE QUESTION 1: What is the average number of citations received by publications in the last 5 years?
EXAMPLE QUERY 1: SELECT avg(publication.citation_num) FROM publication WHERE publication.year >= extract(YEAR FROM CURRENT_DATE) - 5

EXAMPLE QUESTION 2: What is the average number of citations received by publications in each year?
EXAMPLE QUERY 2: SELECT publication.year, AVG(publication.citation_num) AS average_citations FROM publication GROUP BY publication.year ORDER BY publication.year NULLS LAST

"
What is the average number of citations received by publications in each year?,"SELECT publication.year, AVG(publication.citation_num) AS average_citations FROM publication GROUP BY publication.year ORDER BY publication.year NULLS LAST;",academic,group_by,"EXAMPLE QUESTION 1: What is the total number of citations received by each author?
EXAMPLE QUERY 1: SELECT author.name, sum(publication.citation_num) AS total_citations FROM author JOIN writes ON author.aid = writes.aid JOIN publication ON writes.pid = publication.pid GROUP BY author.name ORDER BY total_citations DESC NULLS LAST

EXAMPLE QUESTION 2: What is the average number of references cited by publications in each domain name?
EXAMPLE QUERY 2: SELECT domain.name, AVG(publication.reference_num) AS average_references FROM domain_publication JOIN publication ON domain_publication.pid = publication.pid JOIN DOMAIN ON domain.did = domain_publication.did GROUP BY domain.name

"
What is the title of the publication that has received the highest number of citations?,SELECT publication.title FROM publication ORDER BY publication.citation_num DESC NULLS LAST LIMIT 1;,academic,order_by,"EXAMPLE QUESTION 1: What are the top 3 publications with the highest number of citations?
EXAMPLE QUERY 1: SELECT publication.title, publication.citation_num FROM publication ORDER BY publication.citation_num DESC LIMIT 3

EXAMPLE QUESTION 2: What are the top 3 titles of the publications that have the highest number of references cited, ordered by the number of references cited in descending order?
EXAMPLE QUERY 2: SELECT publication.title FROM publication ORDER BY publication.reference_num DESC LIMIT 3

"
What are the top 5 domains with the highest number of authors associated with them?,"SELECT {d.name, d.did}, COUNT(DISTINCT a.aid) AS author_count FROM author a JOIN domain_author da ON a.aid = da.aid JOIN DOMAIN d ON da.did = d.did GROUP BY {} ORDER BY author_count DESC LIMIT 5;",academic,order_by,"EXAMPLE QUESTION 1: What are the top 3 publications with the highest number of citations?
EXAMPLE QUERY 1: SELECT publication.title, publication.citation_num FROM publication ORDER BY publication.citation_num DESC LIMIT 3

EXAMPLE QUESTION 2: Which authors belong to the same domain as Martin?
EXAMPLE QUERY 2: SELECT DISTINCT a2.name FROM author a1 JOIN domain_author da1 ON a1.aid = da1.aid JOIN domain_author da2 ON da1.did = da2.did JOIN author a2 ON da2.aid = a2.aid WHERE LOWER(a1.name) LIKE '%martin%'

"
"What are the top 3 titles of the publications that have the highest number of references cited, ordered by the number of references cited in descending order?",SELECT publication.title FROM publication ORDER BY publication.reference_num DESC LIMIT 3;,academic,order_by,"EXAMPLE QUESTION 1: What are the top 3 publications with the highest number of citations?
EXAMPLE QUERY 1: SELECT publication.title, publication.citation_num FROM publication ORDER BY publication.citation_num DESC LIMIT 3

EXAMPLE QUESTION 2: What is the title of the publication that has received the highest number of citations?
EXAMPLE QUERY 2: SELECT publication.title FROM publication ORDER BY publication.citation_num DESC NULLS LAST LIMIT 1

"
What are the top 3 publications with the highest number of citations?,"SELECT {publication.title, publication.pid}, publication.citation_num FROM publication ORDER BY publication.citation_num DESC LIMIT 3;",academic,order_by,"EXAMPLE QUESTION 1: What are the top 3 titles of the publications that have the highest number of references cited, ordered by the number of references cited in descending order?
EXAMPLE QUERY 1: SELECT publication.title FROM publication ORDER BY publication.reference_num DESC LIMIT 3

EXAMPLE QUESTION 2: What is the title of the publication that has received the highest number of citations?
EXAMPLE QUERY 2: SELECT publication.title FROM publication ORDER BY publication.citation_num DESC NULLS LAST LIMIT 1

"
What are the titles of all publications ordered alphabetically?,SELECT DISTINCT publication.title FROM publication ORDER BY publication.title ASC NULLS LAST;,academic,order_by,"EXAMPLE QUESTION 1: What is the total number of publications in each journal, ordered by the number of publications in descending order?
EXAMPLE QUERY 1: SELECT journal.name, COUNT(publication.pid) AS total_publications FROM publication JOIN journal ON publication.jid=journal.jid GROUP BY journal.name ORDER BY total_publications DESC NULLS LAST

EXAMPLE QUESTION 2: What are the top 3 titles of the publications that have the highest number of references cited, ordered by the number of references cited in descending order?
EXAMPLE QUERY 2: SELECT publication.title FROM publication ORDER BY publication.reference_num DESC LIMIT 3

"
What is the ratio of publications to authors in the database?,"SELECT CAST(COUNT(DISTINCT publication.pid) AS FLOAT) / NULLIF(COUNT(DISTINCT author.aid), 0) AS publication_to_author_ratio FROM publication, author;",academic,ratio,"EXAMPLE QUESTION 1: What is the ratio of publications presented in conferences to publications published in journals?
EXAMPLE QUERY 1: SELECT CAST(COUNT(DISTINCT publication.cid) AS FLOAT) / NULLIF(COUNT(DISTINCT publication.jid), 0) AS ratio FROM publication

EXAMPLE QUESTION 2: How does the ratio of publications to journals change over the years? Return the annual numbers of publications and journals as well.
EXAMPLE QUERY 2: SELECT publication.year, COUNT(DISTINCT publication.pid) AS num_publications, COUNT(DISTINCT publication.jid) AS num_journals, CAST(COUNT(DISTINCT publication.pid) AS FLOAT) / NULLIF(COUNT(DISTINCT publication.jid), 0) AS ratio FROM publication GROUP BY publication.year ORDER BY publication.year

"
What is the ratio of publications presented in conferences to publications published in journals?,"SELECT CAST(COUNT(DISTINCT publication.cid) AS FLOAT) / NULLIF(COUNT(DISTINCT publication.jid), 0) AS ratio FROM publication;",academic,ratio,"EXAMPLE QUESTION 1: What is the total number of publications presented in each conference?
EXAMPLE QUERY 1: SELECT conference.name, COUNT(publication.pid) AS total_publications FROM publication JOIN conference ON publication.cid = conference.cid GROUP BY conference.name ORDER BY total_publications DESC

EXAMPLE QUESTION 2: What is the ratio of publications to authors in the database?
EXAMPLE QUERY 2: SELECT CAST(COUNT(DISTINCT publication.pid) AS FLOAT) / NULLIF(COUNT(DISTINCT author.aid), 0) AS publication_to_author_ratio FROM publication, author

"
What is the ratio of the total number of publications to the total number of keywords within each domain ID? Show all domain IDs.,"SELECT domain_publication.did, CAST(COUNT(DISTINCT domain_publication.pid) AS FLOAT) / NULLIF(COUNT(DISTINCT domain_keyword.kid), 0) AS publication_to_keyword_ratio FROM domain_publication LEFT JOIN domain_keyword ON domain_publication.did = domain_keyword.did GROUP BY domain_publication.did ORDER BY publication_to_keyword_ratio DESC NULLS LAST;",academic,ratio,"EXAMPLE QUESTION 1: What is the ratio of publications to authors in the database?
EXAMPLE QUERY 1: SELECT CAST(COUNT(DISTINCT publication.pid) AS FLOAT) / NULLIF(COUNT(DISTINCT author.aid), 0) AS publication_to_author_ratio FROM publication, author

EXAMPLE QUESTION 2: What is the average number of references cited by publications in each domain name?
EXAMPLE QUERY 2: SELECT domain.name, AVG(publication.reference_num) AS average_references FROM domain_publication JOIN publication ON domain_publication.pid = publication.pid JOIN DOMAIN ON domain.did = domain_publication.did GROUP BY domain.name

"
How does the ratio of publications to journals change over the years? Return the annual numbers of publications and journals as well.,"SELECT publication.year, COUNT(DISTINCT publication.pid) AS num_publications, COUNT(DISTINCT publication.jid) AS num_journals, CAST(COUNT(DISTINCT publication.pid) AS FLOAT) / NULLIF(COUNT(DISTINCT publication.jid), 0) AS ratio FROM publication GROUP BY publication.year ORDER BY publication.year;",academic,ratio,"EXAMPLE QUESTION 1: What is the ratio of publications presented in conferences to publications published in journals?
EXAMPLE QUERY 1: SELECT CAST(COUNT(DISTINCT publication.cid) AS FLOAT) / NULLIF(COUNT(DISTINCT publication.jid), 0) AS ratio FROM publication

EXAMPLE QUESTION 2: What is the total number of publications published in each year?
EXAMPLE QUERY 2: SELECT publication.year, COUNT(DISTINCT publication.pid) AS total_publications FROM publication GROUP BY publication.year ORDER BY publication.year

"
How does the ratio of authors to organizations differ by continent?,"SELECT organization.continent, COUNT(DISTINCT author.aid)::float / NULLIF(COUNT(DISTINCT organization.oid), 0) AS ratio FROM organization LEFT JOIN author ON author.oid = organization.oid GROUP BY organization.continent ORDER BY ratio DESC NULLS LAST;",academic,ratio,"EXAMPLE QUESTION 1: What is the ratio of publications to authors in the database?
EXAMPLE QUERY 1: SELECT CAST(COUNT(DISTINCT publication.pid) AS FLOAT) / NULLIF(COUNT(DISTINCT author.aid), 0) AS publication_to_author_ratio FROM publication, author

EXAMPLE QUESTION 2: Which authors are not part of any organization?
EXAMPLE QUERY 2: SELECT DISTINCT name FROM author WHERE oid IS NULL

"
Which author had the most publications in the year 2021 and how many publications did he/she have that year?,"SELECT {author.name, author.aid}, COUNT(publication.pid) AS publication_count FROM writes JOIN author ON writes.aid = author.aid JOIN publication ON writes.pid = publication.pid WHERE publication.year = 2021 GROUP BY {} ORDER BY publication_count DESC NULLS LAST LIMIT 1;",academic,table_join,"EXAMPLE QUESTION 1: How many publications were published between 2019 and 2021?
EXAMPLE QUERY 1: SELECT count(DISTINCT publication.pid) FROM publication WHERE publication.year BETWEEN 2019 AND 2021

EXAMPLE QUESTION 2: What is the total number of publications published in each year?
EXAMPLE QUERY 2: SELECT publication.year, COUNT(DISTINCT publication.pid) AS total_publications FROM publication GROUP BY publication.year ORDER BY publication.year

"
What is the total number of publications presented in each conference?,"SELECT {conference.name, conference.cid}, COUNT(publication.pid) AS total_publications FROM publication JOIN conference ON publication.cid = conference.cid GROUP BY {} ORDER BY total_publications DESC;",academic,table_join,"EXAMPLE QUESTION 1: What is the total number of publications published in each year?
EXAMPLE QUERY 1: SELECT publication.year, COUNT(DISTINCT publication.pid) AS total_publications FROM publication GROUP BY publication.year ORDER BY publication.year

EXAMPLE QUESTION 2: What is the ratio of publications presented in conferences to publications published in journals?
EXAMPLE QUERY 2: SELECT CAST(COUNT(DISTINCT publication.cid) AS FLOAT) / NULLIF(COUNT(DISTINCT publication.jid), 0) AS ratio FROM publication

"
"What is the total number of publications in each journal, ordered by the number of publications in descending order?","SELECT {journal.name, journal.jid}, COUNT(publication.pid) AS total_publications FROM publication JOIN journal ON publication.jid=journal.jid GROUP BY {} ORDER BY total_publications DESC NULLS LAST;SELECT {journal.name, journal.jid}, COUNT(publication.pid) AS total_publications FROM journal LEFT JOIN publication ON journal.jid=publication.jid GROUP BY {} ORDER BY total_publications DESC NULLS LAST;",academic,table_join,"EXAMPLE QUESTION 1: How many publications were presented at each conference, ordered by the number of publications in descending order? Give the names of the conferences and their corresponding number of publications.
EXAMPLE QUERY 1: SELECT conference.name, COUNT(publication.pid) AS num_publications FROM publication JOIN conference ON publication.cid=conference.cid GROUP BY conference.name, conference.cid ORDER BY num_publications DESC NULLS LAST

EXAMPLE QUESTION 2: What is the total number of publications published in each year?
EXAMPLE QUERY 2: SELECT publication.year, COUNT(DISTINCT publication.pid) AS total_publications FROM publication GROUP BY publication.year ORDER BY publication.year

"
"How many publications were presented at each conference, ordered by the number of publications in descending order? Give the names of the conferences and their corresponding number of publications.","SELECT conference.name, COUNT(publication.pid) AS num_publications FROM publication JOIN conference ON publication.cid=conference.cid GROUP BY conference.name, conference.cid ORDER BY num_publications DESC NULLS LAST;",academic,table_join,"EXAMPLE QUESTION 1: Which conference published the most publications in the last 15 years? Give the conference name and publication count.
EXAMPLE QUERY 1: SELECT conference.name, count(publication.pid) AS publication_count FROM publication JOIN conference ON publication.cid = conference.cid WHERE publication.year >= extract(YEAR FROM CURRENT_DATE) - 15 GROUP BY conference.name ORDER BY publication_count DESC LIMIT 1

EXAMPLE QUESTION 2: What is the total number of publications in each journal, ordered by the number of publications in descending order?
EXAMPLE QUERY 2: SELECT journal.name, COUNT(publication.pid) AS total_publications FROM publication JOIN journal ON publication.jid=journal.jid GROUP BY journal.name ORDER BY total_publications DESC NULLS LAST

"
"How many publications were published in journals whose names start with the letter ""J""?",SELECT count(DISTINCT publication.pid) FROM publication JOIN journal ON publication.jid = journal.jid WHERE journal.name ilike 'J%';,academic,table_join,"EXAMPLE QUESTION 1: How many publications were published between 2019 and 2021?
EXAMPLE QUERY 1: SELECT count(DISTINCT publication.pid) FROM publication WHERE publication.year BETWEEN 2019 AND 2021

EXAMPLE QUESTION 2: Which author had the most publications in the year 2021 and how many publications did he/she have that year?
EXAMPLE QUERY 2: SELECT author.name, COUNT(publication.pid) AS publication_count FROM writes JOIN author ON writes.aid = author.aid JOIN publication ON writes.pid = publication.pid WHERE publication.year = 2021 GROUP BY author.name ORDER BY publication_count DESC NULLS LAST LIMIT 1

"
"Which organizations have authors who have written publications in the domain ""Machine Learning""?","SELECT DISTINCT {organization.name, organization.oid} FROM organization JOIN author ON organization.oid = author.oid JOIN writes ON author.aid = writes.aid JOIN domain_publication ON writes.pid = domain_publication.pid JOIN DOMAIN ON domain_publication.did = domain.did WHERE domain.name ILIKE '%Machine Learning%';",academic,where,"EXAMPLE QUESTION 1: Which authors have written publications in both the domain ""Machine Learning"" and the domain ""Data Science""?
EXAMPLE QUERY 1: SELECT author.name FROM author WHERE author.aid IN (SELECT domain_author.aid FROM domain_author WHERE domain_author.did IN (SELECT domain.did FROM DOMAIN WHERE domain.name IN ('Machine Learning', 'Data Science') ) GROUP BY 1 HAVING COUNT(DISTINCT domain_author.did) = 2)

EXAMPLE QUESTION 2: What are the names of the authors who have written publications in the domain ""Computer Science""?
EXAMPLE QUERY 2: SELECT DISTINCT author.name FROM author JOIN writes ON author.aid = writes.aid JOIN publication ON writes.pid = publication.pid JOIN domain_publication ON publication.pid = domain_publication.pid JOIN DOMAIN ON domain_publication.did = domain.did WHERE domain.name ilike '%computer%science%'

"
Which authors belong to the same domain as Martin?,"SELECT DISTINCT {a2.name, a2.aid} FROM author a1 JOIN domain_author da1 ON a1.aid = da1.aid JOIN domain_author da2 ON da1.did = da2.did JOIN author a2 ON da2.aid = a2.aid WHERE LOWER(a1.name) LIKE '%martin%';",academic,where,"EXAMPLE QUESTION 1: What are the top 5 domains with the highest number of authors associated with them?
EXAMPLE QUERY 1: SELECT d.name, COUNT(DISTINCT a.aid) AS author_count FROM author a JOIN domain_author da ON a.aid = da.aid JOIN DOMAIN d ON da.did = d.did GROUP BY d.name ORDER BY author_count DESC LIMIT 5

EXAMPLE QUESTION 2: Which authors are not part of any organization?
EXAMPLE QUERY 2: SELECT DISTINCT name FROM author WHERE oid IS NULL

"
Which authors are not part of any organization?,"SELECT DISTINCT {name, aid} FROM author WHERE oid IS NULL",academic,where,"EXAMPLE QUESTION 1: Which authors belong to the same domain as Martin?
EXAMPLE QUERY 1: SELECT DISTINCT a2.name FROM author a1 JOIN domain_author da1 ON a1.aid = da1.aid JOIN domain_author da2 ON da1.did = da2.did JOIN author a2 ON da2.aid = a2.aid WHERE LOWER(a1.name) LIKE '%martin%'

EXAMPLE QUESTION 2: How does the ratio of authors to organizations differ by continent?
EXAMPLE QUERY 2: SELECT organization.continent, COUNT(DISTINCT author.aid)::float / NULLIF(COUNT(DISTINCT organization.oid), 0) AS ratio FROM organization LEFT JOIN author ON author.oid = organization.oid GROUP BY organization.continent ORDER BY ratio DESC NULLS LAST

"
What are the publications written by authors from the 'Sociology' domain and presented at conferences in the year 2020?,"SELECT DISTINCT {publication.title, publication.pid} FROM DOMAIN JOIN domain_author ON domain.did = domain_author.did JOIN writes ON domain_author.aid = writes.aid JOIN publication ON writes.pid = publication.pid JOIN domain_conference ON domain.did = domain_conference.did WHERE domain.name ILIKE '%Sociology%' AND publication.year = 2020 AND publication.cid = domain_conference.cid;",academic,where,"EXAMPLE QUESTION 1: Which conference published the most publications in the last 15 years? Give the conference name and publication count.
EXAMPLE QUERY 1: SELECT conference.name, count(publication.pid) AS publication_count FROM publication JOIN conference ON publication.cid = conference.cid WHERE publication.year >= extract(YEAR FROM CURRENT_DATE) - 15 GROUP BY conference.name ORDER BY publication_count DESC LIMIT 1

EXAMPLE QUESTION 2: How many publications were published between 2019 and 2021?
EXAMPLE QUERY 2: SELECT count(DISTINCT publication.pid) FROM publication WHERE publication.year BETWEEN 2019 AND 2021

"
"What are the names of the authors who have written publications in the domain ""Computer Science""?",SELECT DISTINCT author.name FROM author JOIN writes ON author.aid = writes.aid JOIN publication ON writes.pid = publication.pid JOIN domain_publication ON publication.pid = domain_publication.pid JOIN DOMAIN ON domain_publication.did = domain.did WHERE domain.name ilike '%computer%science%';,academic,where,"EXAMPLE QUESTION 1: Which authors have written publications in both the domain ""Machine Learning"" and the domain ""Data Science""?
EXAMPLE QUERY 1: SELECT author.name FROM author WHERE author.aid IN (SELECT domain_author.aid FROM domain_author WHERE domain_author.did IN (SELECT domain.did FROM DOMAIN WHERE domain.name IN ('Machine Learning', 'Data Science') ) GROUP BY 1 HAVING COUNT(DISTINCT domain_author.did) = 2)

EXAMPLE QUESTION 2: Which organizations have authors who have written publications in the domain ""Machine Learning""?
EXAMPLE QUERY 2: SELECT DISTINCT organization.name FROM organization JOIN author ON organization.oid = author.oid JOIN writes ON author.aid = writes.aid JOIN domain_publication ON writes.pid = domain_publication.pid JOIN DOMAIN ON domain_publication.did = domain.did WHERE domain.name ILIKE '%Machine Learning%'

"
What month were most students admitted? Return the month as a date,"SELECT date_trunc('month', s.admit_term) AS MONTH, COUNT(*) AS total_students FROM student s GROUP BY MONTH ORDER BY total_students DESC LIMIT 1;",advising,date_functions,"EXAMPLE QUESTION 1: How long has it been in days since the last admitted student?
EXAMPLE QUERY 1: SELECT CURRENT_DATE - max(admit_term) AS duration_since_last_admitted_student FROM student

EXAMPLE QUESTION 2: How many students were predicted to graduate in the last 10 years?
EXAMPLE QUERY 2: SELECT count(*) AS num_students_graduated FROM student WHERE predicted_graduation_semester >= DATE_TRUNC('year', CURRENT_DATE) - interval '10 year'

"
What's the average predicted time to graduation since admission in no. of days?,SELECT avg(predicted_graduation_semester - admit_term) AS average_predicted_time_to_graduation FROM student;,advising,date_functions,"EXAMPLE QUESTION 1: How many students were predicted to graduate in the last 10 years?
EXAMPLE QUERY 1: SELECT count(*) AS num_students_graduated FROM student WHERE predicted_graduation_semester >= DATE_TRUNC('year', CURRENT_DATE) - interval '10 year'

EXAMPLE QUESTION 2: Subtract 2 weeks from the most recent predicted graduation date and give the month as an integer.
EXAMPLE QUERY 2: SELECT extract(MONTH FROM date_trunc('month', predicted_graduation_semester) - interval '2 weeks') AS MONTH FROM student ORDER BY predicted_graduation_semester DESC LIMIT 1

"
How many students were predicted to graduate in the last 10 years?,"SELECT count(*) AS num_students_graduated FROM student WHERE predicted_graduation_semester >= DATE_TRUNC('year', CURRENT_DATE) - interval '10 year';",advising,date_functions,"EXAMPLE QUESTION 1: What's the average predicted time to graduation since admission in no. of days?
EXAMPLE QUERY 1: SELECT avg(predicted_graduation_semester - admit_term) AS average_predicted_time_to_graduation FROM student

EXAMPLE QUESTION 2: What month were most students admitted? Return the month as a date
EXAMPLE QUERY 2: SELECT date_trunc('month', s.admit_term) AS MONTH, COUNT(*) AS total_students FROM student s GROUP BY MONTH ORDER BY total_students DESC LIMIT 1

"
How long has it been in days since the last admitted student?,SELECT CURRENT_DATE - max(admit_term) AS duration_since_last_admitted_student FROM student;,advising,date_functions,"EXAMPLE QUESTION 1: What month were most students admitted? Return the month as a date
EXAMPLE QUERY 1: SELECT date_trunc('month', s.admit_term) AS MONTH, COUNT(*) AS total_students FROM student s GROUP BY MONTH ORDER BY total_students DESC LIMIT 1

EXAMPLE QUESTION 2: What's the average predicted time to graduation since admission in no. of days?
EXAMPLE QUERY 2: SELECT avg(predicted_graduation_semester - admit_term) AS average_predicted_time_to_graduation FROM student

"
Subtract 2 weeks from the most recent predicted graduation date and give the month as an integer.,"SELECT extract(MONTH FROM date_trunc('month', predicted_graduation_semester) - interval '2 weeks') AS MONTH FROM student ORDER BY predicted_graduation_semester DESC LIMIT 1;",advising,date_functions,"EXAMPLE QUESTION 1: How many students were predicted to graduate in the last 10 years?
EXAMPLE QUERY 1: SELECT count(*) AS num_students_graduated FROM student WHERE predicted_graduation_semester >= DATE_TRUNC('year', CURRENT_DATE) - interval '10 year'

EXAMPLE QUESTION 2: What's the average predicted time to graduation since admission in no. of days?
EXAMPLE QUERY 2: SELECT avg(predicted_graduation_semester - admit_term) AS average_predicted_time_to_graduation FROM student

"
What is the total number of students who found the instructor to be hilarious per course id?,"SELECT course_tags_count.course_id, SUM(course_tags_count.hilarious) AS total_hilarious FROM course_tags_count GROUP BY course_tags_count.course_id;",advising,group_by,"EXAMPLE QUESTION 1: What is the name of the instructor who has taught the most courses, and how many courses have they taught?
EXAMPLE QUERY 1: SELECT instructor.name, count(offering_instructor.offering_id) AS num_courses FROM offering_instructor JOIN instructor ON offering_instructor.instructor_id = instructor.instructor_id GROUP BY instructor.name ORDER BY num_courses DESC LIMIT 1

EXAMPLE QUESTION 2: What is the ratio of the number of students who found the grading criteria clear and easy to understand to the number of students who received good feedback from the instructor for each course id?
EXAMPLE QUERY 2: SELECT course_tags_count.course_id, CAST(course_tags_count.clear_grading AS FLOAT) / NULLIF(course_tags_count.good_feedback, 0) AS ratio FROM course_tags_count ORDER BY course_tags_count.course_id NULLS LAST

"
What is the average clarity score for each instructor who taught a course?,"SELECT {instructor.name, instructor.instructor_id}, avg(course.clarity_score) AS average_clarity_score FROM course JOIN instructor ON course.course_id = instructor.instructor_id GROUP BY {} ORDER BY average_clarity_score DESC NULLS LAST;",advising,group_by,"EXAMPLE QUESTION 1: What is the ratio of helpfulness scores to clarity scores for each course ID?
EXAMPLE QUERY 1: SELECT course.course_id, CAST(course.helpfulness_score AS FLOAT) / NULLIF(course.clarity_score, 0) AS ratio FROM course

EXAMPLE QUESTION 2: What is the ratio of the number of students who found the grading criteria clear and easy to understand to the number of students who received good feedback from the instructor for each course id?
EXAMPLE QUERY 2: SELECT course_tags_count.course_id, CAST(course_tags_count.clear_grading AS FLOAT) / NULLIF(course_tags_count.good_feedback, 0) AS ratio FROM course_tags_count ORDER BY course_tags_count.course_id NULLS LAST

"
How many courses have a final exam and how many do not?,"SELECT course_offering.has_final_exam, COUNT(*) AS num_courses FROM course_offering GROUP BY course_offering.has_final_exam;SELECT COUNT(CASE WHEN co.has_final_exam THEN 1 END) AS num_with_final_exam, COUNT(CASE WHEN NOT co.has_final_exam THEN 1 END) AS num_without_final_exam FROM course_offering co;",advising,group_by,"EXAMPLE QUESTION 1: Which courses have a final project and a final exam?
EXAMPLE QUERY 1: SELECT DISTINCT course.name FROM course_offering JOIN course ON course_offering.course_id = course.course_id WHERE course_offering.has_final_project AND course_offering.has_final_exam

EXAMPLE QUESTION 2: What is the total number of students who have taken a course with a final project or exam?
EXAMPLE QUERY 2: SELECT COUNT(DISTINCT student_record.student_id) AS total_students FROM student_record JOIN course_offering ON student_record.course_id = course_offering.course_id WHERE course_offering.has_final_project OR course_offering.has_final_exam

"
How many courses does each department offer?,"SELECT course.department, COUNT(DISTINCT course.course_id) AS num_courses FROM course GROUP BY course.department ORDER BY num_courses DESC NULLS LAST;",advising,group_by,"EXAMPLE QUESTION 1: How many courses are offered for each semester id?
EXAMPLE QUERY 1: SELECT course_offering.semester, COUNT(DISTINCT course_offering.course_id) AS num_courses FROM course_offering GROUP BY course_offering.semester ORDER BY course_offering.semester

EXAMPLE QUESTION 2: How many courses have a final exam and how many do not?
EXAMPLE QUERY 2: SELECT course_offering.has_final_exam, COUNT(*) AS num_courses FROM course_offering GROUP BY course_offering.has_final_exam

"
How many courses are offered for each semester id?,"SELECT course_offering.semester, COUNT(DISTINCT course_offering.course_id) AS num_courses FROM course_offering GROUP BY course_offering.semester ORDER BY course_offering.semester;SELECT semester.semester_id, COUNT(DISTINCT course_offering.course_id) AS num_courses FROM semester LEFT JOIN course_offering ON semester.semester_id = course_offering.semester GROUP BY semester.semester_id;",advising,group_by,"EXAMPLE QUESTION 1: How many courses does each department offer?
EXAMPLE QUERY 1: SELECT course.department, COUNT(DISTINCT course.course_id) AS num_courses FROM course GROUP BY course.department ORDER BY num_courses DESC NULLS LAST

EXAMPLE QUESTION 2: What is the ratio of the number of courses with projects to the number of courses with exams in each semester id?
EXAMPLE QUERY 2: SELECT course_offering.semester, CAST(SUM(CASE WHEN course.has_projects THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN course.has_exams THEN 1 ELSE 0 END), 0) AS ratio FROM course JOIN course_offering ON course.course_id = course_offering.course_id GROUP BY course_offering.semester ORDER BY course_offering.semester NULLS LAST

"
"Which course has the highest number of enrolled students, and what is the enrollment number?","SELECT {course.name, course.course_id, course.number}, course.num_enrolled FROM course ORDER BY course.num_enrolled DESC NULLS LAST LIMIT 1;",advising,order_by,"EXAMPLE QUESTION 1: What is the total number of students enrolled in each course, ordered from highest to lowest?
EXAMPLE QUERY 1: SELECT course.course_id, SUM(course.num_enrolled) AS total_students FROM course GROUP BY course.course_id ORDER BY total_students DESC NULLS LAST

EXAMPLE QUESTION 2: What is the total number of students who participated actively for each course id, ordered from highest to lowest participants?
EXAMPLE QUERY 2: SELECT course_tags_count.course_id, course_tags_count.participation FROM course_tags_count ORDER BY course_tags_count.participation DESC NULLS LAST

"
"What is the total number of students who participated actively for each course id, ordered from highest to lowest participants?","SELECT course_tags_count.course_id, course_tags_count.participation FROM course_tags_count ORDER BY course_tags_count.participation DESC NULLS LAST;",advising,order_by,"EXAMPLE QUESTION 1: What is the total number of students enrolled in each course, ordered from highest to lowest?
EXAMPLE QUERY 1: SELECT course.course_id, SUM(course.num_enrolled) AS total_students FROM course GROUP BY course.course_id ORDER BY total_students DESC NULLS LAST

EXAMPLE QUESTION 2: Which course has the highest number of enrolled students, and what is the enrollment number?
EXAMPLE QUERY 2: SELECT course.name, course.num_enrolled FROM course ORDER BY course.num_enrolled DESC NULLS LAST LIMIT 1

"
"What is the total number of students enrolled in each course, ordered from highest to lowest?","SELECT {course.course_id, course.name, course.number}, SUM(course.num_enrolled) AS total_students FROM course GROUP BY {} ORDER BY total_students DESC NULLS LAST;",advising,order_by,"EXAMPLE QUESTION 1: What is the total number of students who participated actively for each course id, ordered from highest to lowest participants?
EXAMPLE QUERY 1: SELECT course_tags_count.course_id, course_tags_count.participation FROM course_tags_count ORDER BY course_tags_count.participation DESC NULLS LAST

EXAMPLE QUESTION 2: Which course has the highest number of enrolled students, and what is the enrollment number?
EXAMPLE QUERY 2: SELECT course.name, course.num_enrolled FROM course ORDER BY course.num_enrolled DESC NULLS LAST LIMIT 1

"
"What is the total number of credits earned by each student, ordered from highest to lowest? Give the student id and the total number of credits.","SELECT {student.student_id, student.lastname, student.firstname}, student.total_credit FROM student ORDER BY student.total_credit DESC NULLS LAST;",advising,order_by,"EXAMPLE QUESTION 1: What is the total number of students enrolled in each course, ordered from highest to lowest?
EXAMPLE QUERY 1: SELECT course.course_id, SUM(course.num_enrolled) AS total_students FROM course GROUP BY course.course_id ORDER BY total_students DESC NULLS LAST

EXAMPLE QUESTION 2: What is the total number of students who participated actively for each course id, ordered from highest to lowest participants?
EXAMPLE QUERY 2: SELECT course_tags_count.course_id, course_tags_count.participation FROM course_tags_count ORDER BY course_tags_count.participation DESC NULLS LAST

"
"What is the name of the instructor who has taught the most courses, and how many courses have they taught?","SELECT instructor.name, count(offering_instructor.offering_id) AS num_courses FROM offering_instructor JOIN instructor ON offering_instructor.instructor_id = instructor.instructor_id GROUP BY instructor.name ORDER BY num_courses DESC LIMIT 1;",advising,order_by,"EXAMPLE QUESTION 1: What is the average clarity score for each instructor who taught a course?
EXAMPLE QUERY 1: SELECT instructor.name, avg(course.clarity_score) AS average_clarity_score FROM course JOIN instructor ON course.course_id = instructor.instructor_id GROUP BY instructor.name ORDER BY average_clarity_score DESC NULLS LAST

EXAMPLE QUESTION 2: Which course has the highest number of enrolled students, and what is the enrollment number?
EXAMPLE QUERY 2: SELECT course.name, course.num_enrolled FROM course ORDER BY course.num_enrolled DESC NULLS LAST LIMIT 1

"
What is the ratio of the total number of students enrolled in courses with exams to the total number of students enrolled in courses without exams?,"SELECT CAST(SUM(CASE WHEN course.has_exams THEN course.num_enrolled ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN NOT course.has_exams THEN course.num_enrolled ELSE 0 END), 0) AS ratio FROM course;",advising,ratio,"EXAMPLE QUESTION 1: What is the ratio of the number of courses with projects to the number of courses with exams in each semester id?
EXAMPLE QUERY 1: SELECT course_offering.semester, CAST(SUM(CASE WHEN course.has_projects THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN course.has_exams THEN 1 ELSE 0 END), 0) AS ratio FROM course JOIN course_offering ON course.course_id = course_offering.course_id GROUP BY course_offering.semester ORDER BY course_offering.semester NULLS LAST

EXAMPLE QUESTION 2: What is the total number of students who have taken a course with a final project or exam?
EXAMPLE QUERY 2: SELECT COUNT(DISTINCT student_record.student_id) AS total_students FROM student_record JOIN course_offering ON student_record.course_id = course_offering.course_id WHERE course_offering.has_final_project OR course_offering.has_final_exam

"
What is the ratio of the number of students who found the grading criteria clear and easy to understand to the number of students who received good feedback from the instructor for each course id?,"SELECT course_tags_count.course_id, CAST(course_tags_count.clear_grading AS FLOAT) / NULLIF(course_tags_count.good_feedback, 0) AS ratio FROM course_tags_count ORDER BY course_tags_count.course_id NULLS LAST;",advising,ratio,"EXAMPLE QUESTION 1: What is the ratio of helpfulness scores to clarity scores for each course ID?
EXAMPLE QUERY 1: SELECT course.course_id, CAST(course.helpfulness_score AS FLOAT) / NULLIF(course.clarity_score, 0) AS ratio FROM course

EXAMPLE QUESTION 2: How does the ratio of enrolled students to the number of reviews vary across different courses?
EXAMPLE QUERY 2: SELECT course.course_id, CAST(course.num_enrolled AS FLOAT) / NULLIF(course.num_reviews, 0) AS student_review_ratio FROM course ORDER BY student_review_ratio NULLS LAST

"
What is the ratio of the number of courses with projects to the number of courses with exams in each semester id?,"SELECT course_offering.semester, CAST(SUM(CASE WHEN course.has_projects THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN course.has_exams THEN 1 ELSE 0 END), 0) AS ratio FROM course JOIN course_offering ON course.course_id = course_offering.course_id GROUP BY course_offering.semester ORDER BY course_offering.semester NULLS LAST;",advising,ratio,"EXAMPLE QUESTION 1: What is the ratio of the total number of students enrolled in courses with exams to the total number of students enrolled in courses without exams?
EXAMPLE QUERY 1: SELECT CAST(SUM(CASE WHEN course.has_exams THEN course.num_enrolled ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN NOT course.has_exams THEN course.num_enrolled ELSE 0 END), 0) AS ratio FROM course

EXAMPLE QUESTION 2: How many courses are offered for each semester id?
EXAMPLE QUERY 2: SELECT course_offering.semester, COUNT(DISTINCT course_offering.course_id) AS num_courses FROM course_offering GROUP BY course_offering.semester ORDER BY course_offering.semester

"
What is the ratio of helpfulness scores to clarity scores for each course ID?,"SELECT course.course_id, CAST(course.helpfulness_score AS FLOAT) / NULLIF(course.clarity_score, 0) AS ratio FROM course;",advising,ratio,"EXAMPLE QUESTION 1: What is the average clarity score for each instructor who taught a course?
EXAMPLE QUERY 1: SELECT instructor.name, avg(course.clarity_score) AS average_clarity_score FROM course JOIN instructor ON course.course_id = instructor.instructor_id GROUP BY instructor.name ORDER BY average_clarity_score DESC NULLS LAST

EXAMPLE QUESTION 2: What is the ratio of the number of students who found the grading criteria clear and easy to understand to the number of students who received good feedback from the instructor for each course id?
EXAMPLE QUERY 2: SELECT course_tags_count.course_id, CAST(course_tags_count.clear_grading AS FLOAT) / NULLIF(course_tags_count.good_feedback, 0) AS ratio FROM course_tags_count ORDER BY course_tags_count.course_id NULLS LAST

"
How does the ratio of enrolled students to the number of reviews vary across different courses?,"SELECT {course.course_id, course.name, course.number}, CAST(course.num_enrolled AS FLOAT) / NULLIF(course.num_reviews, 0) AS student_review_ratio FROM course ORDER BY student_review_ratio NULLS LAST;",advising,ratio,"EXAMPLE QUESTION 1: What is the ratio of the number of students who found the grading criteria clear and easy to understand to the number of students who received good feedback from the instructor for each course id?
EXAMPLE QUERY 1: SELECT course_tags_count.course_id, CAST(course_tags_count.clear_grading AS FLOAT) / NULLIF(course_tags_count.good_feedback, 0) AS ratio FROM course_tags_count ORDER BY course_tags_count.course_id NULLS LAST

EXAMPLE QUESTION 2: What is the ratio of the total number of students enrolled in courses with exams to the total number of students enrolled in courses without exams?
EXAMPLE QUERY 2: SELECT CAST(SUM(CASE WHEN course.has_exams THEN course.num_enrolled ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN NOT course.has_exams THEN course.num_enrolled ELSE 0 END), 0) AS ratio FROM course

"
Which courses have been taken by students in the Computer Science program?,"SELECT DISTINCT {course.name, course.course_id, course.number} AS course_name FROM student JOIN student_record ON student.student_id = student_record.student_id JOIN program ON student.program_id = program.program_id JOIN course ON student_record.course_id = course.course_id WHERE program.name ILIKE '%Computer Science%';",advising,table_join,"EXAMPLE QUESTION 1: What are the names of all the courses offered by the department of Computer Science?
EXAMPLE QUERY 1: SELECT course.name FROM course WHERE course.department ILIKE '%Computer Science%' ORDER BY course.name ASC NULLS LAST

EXAMPLE QUESTION 2: What are the easiness scores for courses in the ""Computer Science"" department? Show both courses and scores.
EXAMPLE QUERY 2: SELECT course.name, course.easiness_score FROM course WHERE course.department ilike '%Computer Science%'

"
Which courses have a final project and a final exam?,"SELECT DISTINCT {course.name, course.course_id, course.number} FROM course_offering JOIN course ON course_offering.course_id = course.course_id WHERE course_offering.has_final_project AND course_offering.has_final_exam;",advising,table_join,"EXAMPLE QUESTION 1: How many courses have a final exam and how many do not?
EXAMPLE QUERY 1: SELECT course_offering.has_final_exam, COUNT(*) AS num_courses FROM course_offering GROUP BY course_offering.has_final_exam

EXAMPLE QUESTION 2: What is the total number of students who have taken a course with a final project or exam?
EXAMPLE QUERY 2: SELECT COUNT(DISTINCT student_record.student_id) AS total_students FROM student_record JOIN course_offering ON student_record.course_id = course_offering.course_id WHERE course_offering.has_final_project OR course_offering.has_final_exam

"
What is the total number of students who have taken a course with a final project or exam?,SELECT COUNT(DISTINCT student_record.student_id) AS total_students FROM student_record JOIN course_offering ON student_record.course_id = course_offering.course_id WHERE course_offering.has_final_project OR course_offering.has_final_exam;,advising,table_join,"EXAMPLE QUESTION 1: How many courses have a final exam and how many do not?
EXAMPLE QUERY 1: SELECT course_offering.has_final_exam, COUNT(*) AS num_courses FROM course_offering GROUP BY course_offering.has_final_exam

EXAMPLE QUESTION 2: What is the ratio of the number of courses with projects to the number of courses with exams in each semester id?
EXAMPLE QUERY 2: SELECT course_offering.semester, CAST(SUM(CASE WHEN course.has_projects THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN course.has_exams THEN 1 ELSE 0 END), 0) AS ratio FROM course JOIN course_offering ON course.course_id = course_offering.course_id GROUP BY course_offering.semester ORDER BY course_offering.semester NULLS LAST

"
What is the total number of credits earned by students in each program?,"SELECT {program.name, program.program_id}, SUM(student.total_credit) AS total_credits FROM student JOIN program ON student.program_id = program.program_id GROUP BY {};",advising,table_join,"EXAMPLE QUESTION 1: How many students have declared a major in each program?
EXAMPLE QUERY 1: SELECT program.name, COUNT(student.student_id) AS number_of_students FROM student JOIN program ON student.program_id = program.program_id WHERE student.declare_major IS NOT NULL GROUP BY program.name ORDER BY number_of_students DESC

EXAMPLE QUESTION 2: How many courses are offered for each semester id?
EXAMPLE QUERY 2: SELECT course_offering.semester, COUNT(DISTINCT course_offering.course_id) AS num_courses FROM course_offering GROUP BY course_offering.semester ORDER BY course_offering.semester

"
How many students have declared a major in each program?,"SELECT {program.name, program.program_id}, COUNT(student.student_id) AS number_of_students FROM student JOIN program ON student.program_id = program.program_id WHERE student.declare_major IS NOT NULL GROUP BY {} ORDER BY number_of_students DESC;",advising,table_join,"EXAMPLE QUESTION 1: What is the total number of credits earned by students in each program?
EXAMPLE QUERY 1: SELECT program.name, SUM(student.total_credit) AS total_credits FROM student JOIN program ON student.program_id = program.program_id GROUP BY program.name

EXAMPLE QUESTION 2: Which course has the highest number of enrolled students, and what is the enrollment number?
EXAMPLE QUERY 2: SELECT course.name, course.num_enrolled FROM course ORDER BY course.num_enrolled DESC NULLS LAST LIMIT 1

"
Which students have declared a minor program? List their firstname and lastname. Order the results by the students' last names.,"SELECT student.firstname, student.lastname FROM student WHERE student.minor IS NOT NULL ORDER BY student.lastname NULLS LAST;",advising,where,"EXAMPLE QUESTION 1: How many students have declared a major in each program?
EXAMPLE QUERY 1: SELECT program.name, COUNT(student.student_id) AS number_of_students FROM student JOIN program ON student.program_id = program.program_id WHERE student.declare_major IS NOT NULL GROUP BY program.name ORDER BY number_of_students DESC

EXAMPLE QUESTION 2: What is the total number of credits earned by students in each program?
EXAMPLE QUERY 2: SELECT program.name, SUM(student.total_credit) AS total_credits FROM student JOIN program ON student.program_id = program.program_id GROUP BY program.name

"
"What is the average GPA of students in the program ""Computer Engineering""?",SELECT AVG(student.total_gpa) FROM student JOIN program ON student.program_id = program.program_id WHERE LOWER(program.name) LIKE '%computer engineering%';,advising,where,"EXAMPLE QUESTION 1: What is the total number of credits earned by students in each program?
EXAMPLE QUERY 1: SELECT program.name, SUM(student.total_credit) AS total_credits FROM student JOIN program ON student.program_id = program.program_id GROUP BY program.name

EXAMPLE QUESTION 2: How many students have declared a major in each program?
EXAMPLE QUERY 2: SELECT program.name, COUNT(student.student_id) AS number_of_students FROM student JOIN program ON student.program_id = program.program_id WHERE student.declare_major IS NOT NULL GROUP BY program.name ORDER BY number_of_students DESC

"
What are the names of all the courses offered by the department of Computer Science?,SELECT course.name FROM course WHERE course.department ILIKE '%Computer Science%' ORDER BY course.name ASC NULLS LAST;,advising,where,"EXAMPLE QUESTION 1: Which courses have been taken by students in the Computer Science program?
EXAMPLE QUERY 1: SELECT DISTINCT course.name AS course_name FROM student JOIN student_record ON student.student_id = student_record.student_id JOIN program ON student.program_id = program.program_id JOIN course ON student_record.course_id = course.course_id WHERE program.name ILIKE '%Computer Science%'

EXAMPLE QUESTION 2: What are the easiness scores for courses in the ""Computer Science"" department? Show both courses and scores.
EXAMPLE QUERY 2: SELECT course.name, course.easiness_score FROM course WHERE course.department ilike '%Computer Science%'

"
"What are the easiness scores for courses in the ""Computer Science"" department? Show both courses and scores.","SELECT {course.name, course.course_id, course.number}, course.easiness_score FROM course WHERE course.department ilike '%Computer Science%';",advising,where,"EXAMPLE QUESTION 1: What are the names of all the courses offered by the department of Computer Science?
EXAMPLE QUERY 1: SELECT course.name FROM course WHERE course.department ILIKE '%Computer Science%' ORDER BY course.name ASC NULLS LAST

EXAMPLE QUESTION 2: Which courses have been taken by students in the Computer Science program?
EXAMPLE QUERY 2: SELECT DISTINCT course.name AS course_name FROM student JOIN student_record ON student.student_id = student_record.student_id JOIN program ON student.program_id = program.program_id JOIN course ON student_record.course_id = course.course_id WHERE program.name ILIKE '%Computer Science%'

"
How many students have taken a course in-person or online?,SELECT count(DISTINCT sr.student_id) AS num_students FROM student_record sr JOIN student s ON sr.student_id = s.student_id WHERE sr.how ilike '%in-person%' OR sr.how ilike '%online%';,advising,where,"EXAMPLE QUESTION 1: What is the total number of students who have taken a course with a final project or exam?
EXAMPLE QUERY 1: SELECT COUNT(DISTINCT student_record.student_id) AS total_students FROM student_record JOIN course_offering ON student_record.course_id = course_offering.course_id WHERE course_offering.has_final_project OR course_offering.has_final_exam

EXAMPLE QUESTION 2: Which course has the highest number of enrolled students, and what is the enrollment number?
EXAMPLE QUERY 2: SELECT course.name, course.num_enrolled FROM course ORDER BY course.num_enrolled DESC NULLS LAST LIMIT 1

"
Which flight has the shortest duration between departure and arrival times? Convert to minutes.,"SELECT {flight.flight_number, flight.flight_id}, (arrival_time - departure_time) / 60 AS duration_minutes FROM flight ORDER BY duration_minutes LIMIT 1;",atis,date_functions,"EXAMPLE QUESTION 1: What's the difference in time in days between today and the earliest flight departure?
EXAMPLE QUERY 1: SELECT date_part('day', CURRENT_DATE - to_timestamp(departure_time)) AS difference_in_days FROM flight ORDER BY departure_time LIMIT 1

EXAMPLE QUESTION 2: What's the average duration between departure and arrival times minus 34 minutes? Convert from UNIX to regular datetime.
EXAMPLE QUERY 2: SELECT avg(to_timestamp(arrival_time) - to_timestamp(departure_time) - interval '34 minutes') AS average_duration FROM flight

"
What's the average duration between departure and arrival times minus 34 minutes? Convert from UNIX to regular datetime.,SELECT avg(to_timestamp(arrival_time) - to_timestamp(departure_time) - interval '34 minutes') AS average_duration FROM flight;SELECT AVG(arrival_time - departure_time)/60 - 34 AS average_duration FROM flight;,atis,date_functions,"EXAMPLE QUESTION 1: Which flight has the shortest duration between departure and arrival times? Convert to minutes.
EXAMPLE QUERY 1: SELECT flight.flight_number, (arrival_time - departure_time) / 60 AS duration_minutes FROM flight ORDER BY duration_minutes LIMIT 1

EXAMPLE QUESTION 2: What's the difference in time in days between today and the earliest flight departure?
EXAMPLE QUERY 2: SELECT date_part('day', CURRENT_DATE - to_timestamp(departure_time)) AS difference_in_days FROM flight ORDER BY departure_time LIMIT 1

"
Count the number of flight departures for each month?,"SELECT month.month_name, count(*) AS departure_count FROM flight JOIN MONTH ON extract(MONTH FROM to_timestamp(flight.departure_time)) = month.month_number GROUP BY month.month_name, month.month_number ORDER BY month.month_number;SELECT date_trunc('month', to_timestamp(flight.departure_time)) AS MONTH, COUNT(*) AS num_departures FROM flight GROUP BY MONTH ORDER BY MONTH;",atis,date_functions,"EXAMPLE QUESTION 1: Which flights operate on Mondays and Wednesdays? Give me the relevant flight numbers
EXAMPLE QUERY 1: SELECT flight.flight_number FROM flight WHERE LOWER(flight.flight_days) LIKE '%mon%' AND LOWER(flight.flight_days) LIKE '%wed%'

EXAMPLE QUESTION 2: How many flights depart from each airport code, excluding departures from stopovers?
EXAMPLE QUERY 2: SELECT airport.airport_code, COUNT(flight.from_airport) AS num_departures FROM airport LEFT JOIN flight ON airport.airport_code = flight.from_airport GROUP BY airport.airport_code

"
What's the earliest flight departure time in the day in HH:MM?,"SELECT to_char(to_timestamp(departure_time)::TIME, 'HH24:MI') AS earliest_departure_time FROM flight ORDER BY earliest_departure_time LIMIT 1;",atis,date_functions,"EXAMPLE QUESTION 1: What's the difference in time in days between today and the earliest flight departure?
EXAMPLE QUERY 1: SELECT date_part('day', CURRENT_DATE - to_timestamp(departure_time)) AS difference_in_days FROM flight ORDER BY departure_time LIMIT 1

EXAMPLE QUESTION 2: Which flight has the shortest duration between departure and arrival times? Convert to minutes.
EXAMPLE QUERY 2: SELECT flight.flight_number, (arrival_time - departure_time) / 60 AS duration_minutes FROM flight ORDER BY duration_minutes LIMIT 1

"
What's the difference in time in days between today and the earliest flight departure?,"SELECT date_part('day', CURRENT_DATE - to_timestamp(departure_time)) AS difference_in_days FROM flight ORDER BY departure_time LIMIT 1;",atis,date_functions,"EXAMPLE QUESTION 1: What's the earliest flight departure time in the day in HH:MM?
EXAMPLE QUERY 1: SELECT to_char(to_timestamp(departure_time)::TIME, 'HH24:MI') AS earliest_departure_time FROM flight ORDER BY earliest_departure_time LIMIT 1

EXAMPLE QUESTION 2: Which flight has the shortest duration between departure and arrival times? Convert to minutes.
EXAMPLE QUERY 2: SELECT flight.flight_number, (arrival_time - departure_time) / 60 AS duration_minutes FROM flight ORDER BY duration_minutes LIMIT 1

"
What is the total cost of round-trip fares for each airline code?,"SELECT fare.fare_airline, SUM(fare.round_trip_cost) AS total_round_trip_cost FROM fare GROUP BY fare.fare_airline ORDER BY total_round_trip_cost DESC;",atis,group_by,"EXAMPLE QUESTION 1: What is the total cost of all round-trip fares from New York (JFK) to Los Angeles?
EXAMPLE QUERY 1: SELECT SUM(fare.round_trip_cost) AS total_round_trip_cost FROM fare WHERE fare.from_airport = 'JFK' AND fare.to_airport = 'LAX'

EXAMPLE QUESTION 2: How many flights require a round-trip to purchase the fare?
EXAMPLE QUERY 2: SELECT COUNT(*) FROM fare WHERE round_trip_required = 'Yes'

"
"What is the average cost of round-trip fares from Los Angeles (LAX) to Chicago (ORD) for each airline, sorted in descending order by average cost?","SELECT fare.fare_airline, AVG(fare.round_trip_cost) AS average_cost FROM fare WHERE fare.from_airport = 'LAX' AND fare.to_airport = 'ORD' GROUP BY fare.fare_airline ORDER BY average_cost DESC NULLS LAST;SELECT airline.airline_name, AVG(fare.round_trip_cost) AS avg_round_trip_cost FROM fare JOIN airline ON fare.fare_airline = airline.airline_code WHERE fare.from_airport = 'LAX' AND fare.to_airport = 'ORD' GROUP BY airline.airline_name ORDER BY avg_round_trip_cost DESC;",atis,group_by,"EXAMPLE QUESTION 1: What is the average cost of a one-way trip for each fare id, sorted in ascending order?
EXAMPLE QUERY 1: SELECT fare.fare_id, AVG(fare.one_direction_cost) AS average_cost FROM fare GROUP BY fare.fare_id ORDER BY average_cost ASC NULLS LAST

EXAMPLE QUESTION 2: What is the total cost of all round-trip fares from New York (JFK) to Los Angeles?
EXAMPLE QUERY 2: SELECT SUM(fare.round_trip_cost) AS total_round_trip_cost FROM fare WHERE fare.from_airport = 'JFK' AND fare.to_airport = 'LAX'

"
"What is the average cost of a one-way trip for each fare id, sorted in ascending order?","SELECT fare.fare_id, AVG(fare.one_direction_cost) AS average_cost FROM fare GROUP BY fare.fare_id ORDER BY average_cost ASC NULLS LAST;",atis,group_by,"EXAMPLE QUESTION 1: What is the average cost of round-trip fares from Los Angeles (LAX) to Chicago (ORD) for each airline, sorted in descending order by average cost?
EXAMPLE QUERY 1: SELECT fare.fare_airline, AVG(fare.round_trip_cost) AS average_cost FROM fare WHERE fare.from_airport = 'LAX' AND fare.to_airport = 'ORD' GROUP BY fare.fare_airline ORDER BY average_cost DESC NULLS LAST

EXAMPLE QUESTION 2: What is the ratio of one-way trip costs to round-trip costs for each fare?
EXAMPLE QUERY 2: SELECT fare.fare_id, fare.one_direction_cost::float / NULLIF(fare.round_trip_cost, 0) AS cost_ratio FROM fare ORDER BY cost_ratio

"
"How many meals are served in each compartment, sorted by the number of meals in descending order?","SELECT food_service.compartment, COUNT(food_service.meal_number) AS number_of_meals FROM food_service GROUP BY food_service.compartment ORDER BY number_of_meals DESC NULLS LAST;",atis,group_by,"EXAMPLE QUESTION 1: Which flights serve meals in first class? Give me the flight id and meal description.
EXAMPLE QUERY 1: SELECT flight.flight_id, food_service.meal_description FROM flight JOIN food_service ON flight.meal_code = food_service.meal_code WHERE LOWER(food_service.compartment) LIKE '%first class%'

EXAMPLE QUESTION 2: What is the average cost of a one-way trip for each fare id, sorted in ascending order?
EXAMPLE QUERY 2: SELECT fare.fare_id, AVG(fare.one_direction_cost) AS average_cost FROM fare GROUP BY fare.fare_id ORDER BY average_cost ASC NULLS LAST

"
"How many flights depart from each airport code, excluding departures from stopovers?","SELECT airport.airport_code, COUNT(flight.from_airport) AS num_departures FROM airport LEFT JOIN flight ON airport.airport_code = flight.from_airport GROUP BY airport.airport_code;SELECT airport.airport_code, COUNT(flight.from_airport) AS num_departures FROM airport JOIN flight ON airport.airport_code = flight.from_airport GROUP BY airport.airport_code;",atis,group_by,"EXAMPLE QUESTION 1: What is the proportion of flights with stops out of all flights for each airline code?
EXAMPLE QUERY 1: SELECT flight.airline_code, CAST(SUM(CASE WHEN flight.stops > 0 THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(COUNT(*), 0) AS ratio FROM flight GROUP BY flight.airline_code

EXAMPLE QUESTION 2: Count the number of flight departures for each month?
EXAMPLE QUERY 2: SELECT month.month_name, count(*) AS departure_count FROM flight JOIN MONTH ON extract(MONTH FROM to_timestamp(flight.departure_time)) = month.month_number GROUP BY month.month_name, month.month_number ORDER BY month.month_number

"
"Which flight ids to Chicago (ORD) have the longest duration from departure to arrival, sorted in ascending order?","SELECT flight.flight_id, (flight.arrival_time - flight.departure_time) AS duration FROM flight WHERE to_airport = 'ORD' ORDER BY duration ASC NULLS LAST;",atis,order_by,"EXAMPLE QUESTION 1: Which airlines offer flights from Chicago (ORD) to New York (JFK), and how many stops do they have, sorted by number of stops in ascending order?
EXAMPLE QUERY 1: SELECT airline.airline_name, flight.stops FROM flight JOIN airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'ORD' AND flight.to_airport = 'JFK' GROUP BY airline.airline_name, flight.stops ORDER BY flight.stops NULLS LAST

EXAMPLE QUESTION 2: Which flight has the shortest duration between departure and arrival times? Convert to minutes.
EXAMPLE QUERY 2: SELECT flight.flight_number, (arrival_time - departure_time) / 60 AS duration_minutes FROM flight ORDER BY duration_minutes LIMIT 1

"
"Which airports have the shortest minimum connect time, sorted in ascending order? Show the minimum connect time.","SELECT {airport.airport_name, airport.airport_code}, airport.minimum_connect_time FROM airport ORDER BY airport.minimum_connect_time ASC NULLS LAST;",atis,order_by,"EXAMPLE QUESTION 1: What is the minimum amount of time required for a connecting flight at JFK Airport?
EXAMPLE QUERY 1: SELECT minimum_connect_time FROM airport WHERE airport_code = 'JFK'

EXAMPLE QUESTION 2: Which flight has the shortest duration between departure and arrival times? Convert to minutes.
EXAMPLE QUERY 2: SELECT flight.flight_number, (arrival_time - departure_time) / 60 AS duration_minutes FROM flight ORDER BY duration_minutes LIMIT 1

"
Which aircraft code can carry the highest weight of cargo that any aircraft can carry?,SELECT aircraft.aircraft_code FROM aircraft ORDER BY pay_load DESC NULLS LAST LIMIT 1;,atis,order_by,"EXAMPLE QUESTION 1: What is the ratio of aircraft capacity to its range in miles for each aircraft code?
EXAMPLE QUERY 1: SELECT aircraft.aircraft_code, CAST(aircraft.capacity AS FLOAT) / NULLIF(aircraft.range_miles, 0) AS capacity_range_ratio FROM aircraft

EXAMPLE QUESTION 2: Calculate the ratio of the maximum range to the maximum payload for each aircraft
EXAMPLE QUERY 2: SELECT aircraft.range_miles::float / NULLIF(aircraft.pay_load, 0) AS range_to_payload_ratio FROM aircraft

"
What are the top 2 airlines with the most flights?,"SELECT {airline.airline_name, airline.airline_code}, COUNT(flight.flight_id) AS number_of_flights FROM flight JOIN airline ON flight.airline_code = airline.airline_code GROUP BY {} ORDER BY number_of_flights DESC NULLS LAST LIMIT 2;",atis,order_by,"EXAMPLE QUESTION 1: Which airlines offer flights from LAX to ORD?
EXAMPLE QUERY 1: SELECT DISTINCT airline.airline_name FROM flight JOIN airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'LAX' AND flight.to_airport = 'ORD'

EXAMPLE QUESTION 2: Which airlines offer flights from Chicago (ORD) to New York (JFK), and how many stops do they have, sorted by number of stops in ascending order?
EXAMPLE QUERY 2: SELECT airline.airline_name, flight.stops FROM flight JOIN airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'ORD' AND flight.to_airport = 'JFK' GROUP BY airline.airline_name, flight.stops ORDER BY flight.stops NULLS LAST

"
What are the aircraft codes for all aircraft with a cruising speed of over 200 mph? sort the aircraft codes in ascending order.,SELECT aircraft.aircraft_code FROM aircraft WHERE aircraft.cruising_speed > 200 ORDER BY aircraft.aircraft_code ASC NULLS LAST;,atis,order_by,"EXAMPLE QUESTION 1: What is the proportion of flights with stops out of all flights for each airline code?
EXAMPLE QUERY 1: SELECT flight.airline_code, CAST(SUM(CASE WHEN flight.stops > 0 THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(COUNT(*), 0) AS ratio FROM flight GROUP BY flight.airline_code

EXAMPLE QUESTION 2: Which flight ids to Chicago (ORD) have the longest duration from departure to arrival, sorted in ascending order?
EXAMPLE QUERY 2: SELECT flight.flight_id, (flight.arrival_time - flight.departure_time) AS duration FROM flight WHERE to_airport = 'ORD' ORDER BY duration ASC NULLS LAST

"
Calculate the ratio of the maximum range to the maximum payload for each aircraft,"SELECT aircraft.range_miles::float / NULLIF(aircraft.pay_load, 0) AS range_to_payload_ratio FROM aircraft;",atis,ratio,"EXAMPLE QUESTION 1: What is the ratio of aircraft capacity to its range in miles for each aircraft code?
EXAMPLE QUERY 1: SELECT aircraft.aircraft_code, CAST(aircraft.capacity AS FLOAT) / NULLIF(aircraft.range_miles, 0) AS capacity_range_ratio FROM aircraft

EXAMPLE QUESTION 2: How does the average ratio of the cruising speed to the payload of an aircraft vary across different aircraft manufacturers?
EXAMPLE QUERY 2: SELECT aircraft.manufacturer, AVG(CAST(aircraft.cruising_speed AS FLOAT) / NULLIF(aircraft.pay_load, 0)) AS speed_payload_ratio FROM aircraft GROUP BY aircraft.manufacturer ORDER BY speed_payload_ratio DESC NULLS LAST

"
What is the ratio of one-way trip costs to round-trip costs for each fare?,"SELECT fare.fare_id, fare.one_direction_cost::float / NULLIF(fare.round_trip_cost, 0) AS cost_ratio FROM fare ORDER BY cost_ratio;",atis,ratio,"EXAMPLE QUESTION 1: What is the total cost of all round-trip fares from New York (JFK) to Los Angeles?
EXAMPLE QUERY 1: SELECT SUM(fare.round_trip_cost) AS total_round_trip_cost FROM fare WHERE fare.from_airport = 'JFK' AND fare.to_airport = 'LAX'

EXAMPLE QUESTION 2: What is the average cost of a one-way trip for each fare id, sorted in ascending order?
EXAMPLE QUERY 2: SELECT fare.fare_id, AVG(fare.one_direction_cost) AS average_cost FROM fare GROUP BY fare.fare_id ORDER BY average_cost ASC NULLS LAST

"
What is the ratio of aircraft capacity to its range in miles for each aircraft code?,"SELECT aircraft.aircraft_code, CAST(aircraft.capacity AS FLOAT) / NULLIF(aircraft.range_miles, 0) AS capacity_range_ratio FROM aircraft;",atis,ratio,"EXAMPLE QUESTION 1: What is the proportion of flights with stops out of all flights for each airline code?
EXAMPLE QUERY 1: SELECT flight.airline_code, CAST(SUM(CASE WHEN flight.stops > 0 THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(COUNT(*), 0) AS ratio FROM flight GROUP BY flight.airline_code

EXAMPLE QUESTION 2: Calculate the ratio of the maximum range to the maximum payload for each aircraft
EXAMPLE QUERY 2: SELECT aircraft.range_miles::float / NULLIF(aircraft.pay_load, 0) AS range_to_payload_ratio FROM aircraft

"
What is the proportion of flights with stops out of all flights for each airline code?,"SELECT flight.airline_code, CAST(SUM(CASE WHEN flight.stops > 0 THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(COUNT(*), 0) AS ratio FROM flight GROUP BY flight.airline_code;",atis,ratio,"EXAMPLE QUESTION 1: How many flights depart from each airport code, excluding departures from stopovers?
EXAMPLE QUERY 1: SELECT airport.airport_code, COUNT(flight.from_airport) AS num_departures FROM airport LEFT JOIN flight ON airport.airport_code = flight.from_airport GROUP BY airport.airport_code

EXAMPLE QUESTION 2: What is the ratio of aircraft capacity to its range in miles for each aircraft code?
EXAMPLE QUERY 2: SELECT aircraft.aircraft_code, CAST(aircraft.capacity AS FLOAT) / NULLIF(aircraft.range_miles, 0) AS capacity_range_ratio FROM aircraft

"
How does the average ratio of the cruising speed to the payload of an aircraft vary across different aircraft manufacturers?,"SELECT aircraft.manufacturer, AVG(CAST(aircraft.cruising_speed AS FLOAT) / NULLIF(aircraft.pay_load, 0)) AS speed_payload_ratio FROM aircraft GROUP BY aircraft.manufacturer ORDER BY speed_payload_ratio DESC NULLS LAST;",atis,ratio,"EXAMPLE QUESTION 1: Calculate the ratio of the maximum range to the maximum payload for each aircraft
EXAMPLE QUERY 1: SELECT aircraft.range_miles::float / NULLIF(aircraft.pay_load, 0) AS range_to_payload_ratio FROM aircraft

EXAMPLE QUESTION 2: What is the ratio of aircraft capacity to its range in miles for each aircraft code?
EXAMPLE QUERY 2: SELECT aircraft.aircraft_code, CAST(aircraft.capacity AS FLOAT) / NULLIF(aircraft.range_miles, 0) AS capacity_range_ratio FROM aircraft

"
Which flights serve meals in first class? Give me the flight id and meal description.,"SELECT flight.flight_id, food_service.meal_description FROM flight JOIN food_service ON flight.meal_code = food_service.meal_code WHERE LOWER(food_service.compartment) LIKE '%first class%';",atis,table_join,"EXAMPLE QUESTION 1: How many meals are served in each compartment, sorted by the number of meals in descending order?
EXAMPLE QUERY 1: SELECT food_service.compartment, COUNT(food_service.meal_number) AS number_of_meals FROM food_service GROUP BY food_service.compartment ORDER BY number_of_meals DESC NULLS LAST

EXAMPLE QUESTION 2: Which flights operate on Mondays and Wednesdays? Give me the relevant flight numbers
EXAMPLE QUERY 2: SELECT flight.flight_number FROM flight WHERE LOWER(flight.flight_days) LIKE '%mon%' AND LOWER(flight.flight_days) LIKE '%wed%'

"
Which airlines offer flights with a stopover in Dallas?,"SELECT DISTINCT {airline.airline_name, airline.airline_code} FROM flight_stop JOIN airport ON flight_stop.stop_airport = airport.airport_code JOIN flight ON flight_stop.flight_id = flight.flight_id JOIN airline ON flight.airline_code = airline.airline_code WHERE airport.airport_location ILIKE '%Dallas%';",atis,table_join,"EXAMPLE QUESTION 1: Which airlines do not have any flights that either depart from/arrive at JFK, or have one or more stops?
EXAMPLE QUERY 1: SELECT DISTINCT airline.airline_name FROM airline WHERE airline.airline_code NOT IN (SELECT flight.airline_code FROM flight WHERE flight.from_airport = 'JFK' OR flight.to_airport = 'JFK' OR flight.stops > 0)

EXAMPLE QUESTION 2: Which airlines offer flights from LAX to ORD?
EXAMPLE QUERY 2: SELECT DISTINCT airline.airline_name FROM flight JOIN airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'LAX' AND flight.to_airport = 'ORD'

"
Which airlines offer flights from LAX to ORD?,"SELECT DISTINCT {airline.airline_name, airline.airline_code} FROM flight JOIN airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'LAX' AND flight.to_airport = 'ORD';",atis,table_join,"EXAMPLE QUESTION 1: Which airlines offer flights with a stopover in Dallas?
EXAMPLE QUERY 1: SELECT DISTINCT airline.airline_name FROM flight_stop JOIN airport ON flight_stop.stop_airport = airport.airport_code JOIN flight ON flight_stop.flight_id = flight.flight_id JOIN airline ON flight.airline_code = airline.airline_code WHERE airport.airport_location ILIKE '%Dallas%'

EXAMPLE QUESTION 2: Which airlines offer flights from Chicago (ORD) to New York (JFK), and how many stops do they have, sorted by number of stops in ascending order?
EXAMPLE QUERY 2: SELECT airline.airline_name, flight.stops FROM flight JOIN airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'ORD' AND flight.to_airport = 'JFK' GROUP BY airline.airline_name, flight.stops ORDER BY flight.stops NULLS LAST

"
"Which airlines offer flights from Chicago (ORD) to New York (JFK), and how many stops do they have, sorted by number of stops in ascending order?","SELECT {airline.airline_name, airline.airline_code}, flight.stops FROM flight JOIN airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'ORD' AND flight.to_airport = 'JFK' GROUP BY {}, flight.stops ORDER BY flight.stops NULLS LAST;",atis,table_join,"EXAMPLE QUESTION 1: Which airlines offer flights from LAX to ORD?
EXAMPLE QUERY 1: SELECT DISTINCT airline.airline_name FROM flight JOIN airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'LAX' AND flight.to_airport = 'ORD'

EXAMPLE QUESTION 2: Which airlines do not have any flights that either depart from/arrive at JFK, or have one or more stops?
EXAMPLE QUERY 2: SELECT DISTINCT airline.airline_name FROM airline WHERE airline.airline_code NOT IN (SELECT flight.airline_code FROM flight WHERE flight.from_airport = 'JFK' OR flight.to_airport = 'JFK' OR flight.stops > 0)

"
"Which airlines do not have any flights that either depart from/arrive at JFK, or have one or more stops?","SELECT DISTINCT {airline.airline_name, airline.airline_code} FROM airline WHERE airline.airline_code NOT IN (SELECT flight.airline_code FROM flight WHERE flight.from_airport = 'JFK' OR flight.to_airport = 'JFK' OR flight.stops > 0);",atis,table_join,"EXAMPLE QUESTION 1: Which airlines offer flights with a stopover in Dallas?
EXAMPLE QUERY 1: SELECT DISTINCT airline.airline_name FROM flight_stop JOIN airport ON flight_stop.stop_airport = airport.airport_code JOIN flight ON flight_stop.flight_id = flight.flight_id JOIN airline ON flight.airline_code = airline.airline_code WHERE airport.airport_location ILIKE '%Dallas%'

EXAMPLE QUESTION 2: Which airlines offer flights from LAX to ORD?
EXAMPLE QUERY 2: SELECT DISTINCT airline.airline_name FROM flight JOIN airline ON flight.airline_code = airline.airline_code WHERE flight.from_airport = 'LAX' AND flight.to_airport = 'ORD'

"
Which state code is Orlando International Airport in?,SELECT state_code FROM airport WHERE airport_name ILIKE '%Orlando International Airport%';,atis,where,"EXAMPLE QUESTION 1: Which aircraft code can carry the highest weight of cargo that any aircraft can carry?
EXAMPLE QUERY 1: SELECT aircraft.aircraft_code FROM aircraft ORDER BY pay_load DESC NULLS LAST LIMIT 1

EXAMPLE QUESTION 2: Which flight ids to Chicago (ORD) have the longest duration from departure to arrival, sorted in ascending order?
EXAMPLE QUERY 2: SELECT flight.flight_id, (flight.arrival_time - flight.departure_time) AS duration FROM flight WHERE to_airport = 'ORD' ORDER BY duration ASC NULLS LAST

"
Which flights operate on Mondays and Wednesdays? Give me the relevant flight numbers,"SELECT {flight.flight_number, flight.flight_id} FROM flight WHERE LOWER(flight.flight_days) LIKE '%mon%' AND LOWER(flight.flight_days) LIKE '%wed%';",atis,where,"EXAMPLE QUESTION 1: Count the number of flight departures for each month?
EXAMPLE QUERY 1: SELECT month.month_name, count(*) AS departure_count FROM flight JOIN MONTH ON extract(MONTH FROM to_timestamp(flight.departure_time)) = month.month_number GROUP BY month.month_name, month.month_number ORDER BY month.month_number

EXAMPLE QUESTION 2: What's the difference in time in days between today and the earliest flight departure?
EXAMPLE QUERY 2: SELECT date_part('day', CURRENT_DATE - to_timestamp(departure_time)) AS difference_in_days FROM flight ORDER BY departure_time LIMIT 1

"
What is the total cost of all round-trip fares from New York (JFK) to Los Angeles?,SELECT SUM(fare.round_trip_cost) AS total_round_trip_cost FROM fare WHERE fare.from_airport = 'JFK' AND fare.to_airport = 'LAX';,atis,where,"EXAMPLE QUESTION 1: What is the ratio of one-way trip costs to round-trip costs for each fare?
EXAMPLE QUERY 1: SELECT fare.fare_id, fare.one_direction_cost::float / NULLIF(fare.round_trip_cost, 0) AS cost_ratio FROM fare ORDER BY cost_ratio

EXAMPLE QUESTION 2: What is the average cost of round-trip fares from Los Angeles (LAX) to Chicago (ORD) for each airline, sorted in descending order by average cost?
EXAMPLE QUERY 2: SELECT fare.fare_airline, AVG(fare.round_trip_cost) AS average_cost FROM fare WHERE fare.from_airport = 'LAX' AND fare.to_airport = 'ORD' GROUP BY fare.fare_airline ORDER BY average_cost DESC NULLS LAST

"
What is the minimum amount of time required for a connecting flight at JFK Airport?,SELECT minimum_connect_time FROM airport WHERE airport_code = 'JFK';,atis,where,"EXAMPLE QUESTION 1: Which airports have the shortest minimum connect time, sorted in ascending order? Show the minimum connect time.
EXAMPLE QUERY 1: SELECT airport.airport_name, airport.minimum_connect_time FROM airport ORDER BY airport.minimum_connect_time ASC NULLS LAST

EXAMPLE QUESTION 2: Which flight has the shortest duration between departure and arrival times? Convert to minutes.
EXAMPLE QUERY 2: SELECT flight.flight_number, (arrival_time - departure_time) / 60 AS duration_minutes FROM flight ORDER BY duration_minutes LIMIT 1

"
How many flights require a round-trip to purchase the fare?,SELECT COUNT(*) FROM fare WHERE round_trip_required = 'Yes';,atis,where,"EXAMPLE QUESTION 1: What is the total cost of round-trip fares for each airline code?
EXAMPLE QUERY 1: SELECT fare.fare_airline, SUM(fare.round_trip_cost) AS total_round_trip_cost FROM fare GROUP BY fare.fare_airline ORDER BY total_round_trip_cost DESC

EXAMPLE QUESTION 2: What is the ratio of one-way trip costs to round-trip costs for each fare?
EXAMPLE QUERY 2: SELECT fare.fare_id, fare.one_direction_cost::float / NULLIF(fare.round_trip_cost, 0) AS cost_ratio FROM fare ORDER BY cost_ratio

"
What is the total population in cities by country?,"SELECT city.country_name, SUM(city.population) AS total_population FROM city GROUP BY city.country_name ORDER BY total_population DESC NULLS LAST;",geography,group_by,"EXAMPLE QUESTION 1: What are the top 5 cities with the highest population? Give both city names and the population.
EXAMPLE QUERY 1: SELECT city.city_name, city.population FROM city ORDER BY city.population DESC NULLS LAST LIMIT 5

EXAMPLE QUESTION 2: Get the cities in the United States and their population
EXAMPLE QUERY 2: SELECT city_name, population FROM city WHERE country_name ILIKE '%United States%'

"
What is the average length of rivers in each country?,"SELECT river.country_name, AVG(river.length) AS average_length FROM river GROUP BY river.country_name ORDER BY average_length DESC NULLS LAST;",geography,group_by,"EXAMPLE QUESTION 1: What is the average length of rivers per country in countries with a lake?
EXAMPLE QUERY 1: SELECT l.country_name, AVG(r.length) AS average_length FROM river r JOIN lake l ON r.country_name = l.country_name GROUP BY 1

EXAMPLE QUESTION 2: How many rivers flow through each country?
EXAMPLE QUERY 2: SELECT river.country_name, COUNT(DISTINCT river.river_name) AS number_of_rivers FROM river GROUP BY river.country_name ORDER BY number_of_rivers DESC

"
How many rivers flow through each country?,"SELECT river.country_name, COUNT(DISTINCT river.river_name) AS number_of_rivers FROM river GROUP BY river.country_name ORDER BY number_of_rivers DESC;",geography,group_by,"EXAMPLE QUESTION 1: What is the average length of rivers in each country?
EXAMPLE QUERY 1: SELECT river.country_name, AVG(river.length) AS average_length FROM river GROUP BY river.country_name ORDER BY average_length DESC NULLS LAST

EXAMPLE QUESTION 2: What is the average length of rivers per country in countries with a lake?
EXAMPLE QUERY 2: SELECT l.country_name, AVG(r.length) AS average_length FROM river r JOIN lake l ON r.country_name = l.country_name GROUP BY 1

"
How many mountains are there in each country?,"SELECT mountain.country_name, COUNT(mountain.mountain_name) AS number_of_mountains FROM mountain GROUP BY mountain.country_name ORDER BY number_of_mountains DESC;",geography,group_by,"EXAMPLE QUESTION 1: What are the names and altitudes of the mountains in Nepal?
EXAMPLE QUERY 1: SELECT mountain_name, mountain_altitude FROM mountain WHERE country_name ILIKE '%Nepal%'

EXAMPLE QUESTION 2: What are the highest mountains in meters, ordered from highest to lowest altitude?
EXAMPLE QUERY 2: SELECT mountain.mountain_name, mountain.mountain_altitude FROM mountain ORDER BY mountain.mountain_altitude DESC NULLS LAST

"
How many lakes are there in each state?,"SELECT lake.state_name, COUNT(lake.lake_name) AS lake_count FROM lake GROUP BY lake.state_name ORDER BY lake_count DESC;",geography,group_by,"EXAMPLE QUESTION 1: Which lakes have a name that starts with ""Lake""? They should be located in states with an area greater than 1000 square kilometers.
EXAMPLE QUERY 1: SELECT lake.lake_name FROM lake JOIN state ON lake.state_name = state.state_name WHERE state.area > 1000 AND lake.lake_name ilike 'Lake%' ORDER BY lake.lake_name NULLS LAST

EXAMPLE QUESTION 2: Which countries have both lakes and rivers?
EXAMPLE QUERY 2: SELECT DISTINCT lake.country_name FROM public.lake JOIN public.river ON lake.country_name = river.country_name

"
"Which states have the highest population density in people per square kilometer, ordered from highest to lowest?","SELECT state.state_name, state.density FROM state ORDER BY state.density DESC NULLS LAST;",geography,order_by,"EXAMPLE QUESTION 1: What is the highest point in each state and what is the population density of that state?
EXAMPLE QUERY 1: SELECT highlow.state_name, highlow.highest_point, state.density FROM highlow JOIN state ON highlow.state_name = state.state_name GROUP BY highlow.state_name, highlow.highest_point, state.density

EXAMPLE QUESTION 2: What are the top 5 cities with the highest population? Give both city names and the population.
EXAMPLE QUERY 2: SELECT city.city_name, city.population FROM city ORDER BY city.population DESC NULLS LAST LIMIT 5

"
"Which lakes have the largest areas in square kilometers, ordered from largest to smallest?","SELECT lake.lake_name, lake.area FROM lake ORDER BY lake.area DESC NULLS LAST;",geography,order_by,"EXAMPLE QUESTION 1: Which lakes have a name that starts with ""Lake""? They should be located in states with an area greater than 1000 square kilometers.
EXAMPLE QUERY 1: SELECT lake.lake_name FROM lake JOIN state ON lake.state_name = state.state_name WHERE state.area > 1000 AND lake.lake_name ilike 'Lake%' ORDER BY lake.lake_name NULLS LAST

EXAMPLE QUESTION 2: What are the names and areas of the lakes in Michigan?
EXAMPLE QUERY 2: SELECT lake_name, area FROM lake WHERE state_name ILIKE '%Michigan%'

"
What are the top 5 cities with the highest population? Give both city names and the population.,"SELECT city.city_name, city.population FROM city ORDER BY city.population DESC NULLS LAST LIMIT 5;",geography,order_by,"EXAMPLE QUESTION 1: Get the cities in the United States and their population
EXAMPLE QUERY 1: SELECT city_name, population FROM city WHERE country_name ILIKE '%United States%'

EXAMPLE QUESTION 2: What is the total population in cities by country?
EXAMPLE QUERY 2: SELECT city.country_name, SUM(city.population) AS total_population FROM city GROUP BY city.country_name ORDER BY total_population DESC NULLS LAST

"
"What are the longest rivers in meters, ordered from longest to shortest?","SELECT river.river_name, river.length FROM river ORDER BY river.length DESC NULLS LAST;",geography,order_by,"EXAMPLE QUESTION 1: What is the average length of rivers in each country?
EXAMPLE QUERY 1: SELECT river.country_name, AVG(river.length) AS average_length FROM river GROUP BY river.country_name ORDER BY average_length DESC NULLS LAST

EXAMPLE QUESTION 2: What is the average length of rivers per country in countries with a lake?
EXAMPLE QUERY 2: SELECT l.country_name, AVG(r.length) AS average_length FROM river r JOIN lake l ON r.country_name = l.country_name GROUP BY 1

"
"What are the highest mountains in meters, ordered from highest to lowest altitude?","SELECT mountain.mountain_name, mountain.mountain_altitude FROM mountain ORDER BY mountain.mountain_altitude DESC NULLS LAST;",geography,order_by,"EXAMPLE QUESTION 1: What are the names and altitudes of the mountains in Nepal?
EXAMPLE QUERY 1: SELECT mountain_name, mountain_altitude FROM mountain WHERE country_name ILIKE '%Nepal%'

EXAMPLE QUESTION 2: How many mountains are there in each country?
EXAMPLE QUERY 2: SELECT mountain.country_name, COUNT(mountain.mountain_name) AS number_of_mountains FROM mountain GROUP BY mountain.country_name ORDER BY number_of_mountains DESC

"
What is the ratio of the population of the United States to the population of California?,"SELECT CAST(SUM(NULLIF(state.population, 0)) FILTER (WHERE LOWER(state.country_name) LIKE '%united states%') AS FLOAT) / CAST(SUM(NULLIF(state.population, 0)) FILTER (WHERE LOWER(state.state_name) LIKE '%california%') AS FLOAT) AS population_ratio FROM state;",geography,ratio,"EXAMPLE QUESTION 1: What is the ratio of the length of the Mississippi River to the length of the Rhine River?
EXAMPLE QUERY 1: SELECT CAST((SELECT LENGTH FROM river WHERE LOWER(river_name) LIKE '%mississippi%') AS FLOAT) / NULLIF((SELECT LENGTH FROM river WHERE LOWER(river_name) LIKE '%rhine%'), 0) AS ratio

EXAMPLE QUESTION 2: What is the ratio of the altitude of Mount Everest to the altitude of Dhaulagiri?
EXAMPLE QUERY 2: SELECT (CAST(everest.mountain_altitude AS FLOAT) / NULLIF(dhaulagiri.mountain_altitude, 0)) AS altitude_ratio FROM (SELECT mountain_altitude FROM mountain WHERE LOWER(mountain_name) LIKE '%mount everest%') AS everest, (SELECT mountain_altitude FROM mountain WHERE LOWER(mountain_name) LIKE '%dhaulagiri%') AS dhaulagiri

"
What is the ratio of the length of the Mississippi River to the length of the Rhine River?,"SELECT CAST((SELECT LENGTH FROM river WHERE LOWER(river_name) LIKE '%mississippi%') AS FLOAT) / NULLIF((SELECT LENGTH FROM river WHERE LOWER(river_name) LIKE '%rhine%'), 0) AS ratio ;",geography,ratio,"EXAMPLE QUESTION 1: What is the ratio of the altitude of Mount Everest to the altitude of Dhaulagiri?
EXAMPLE QUERY 1: SELECT (CAST(everest.mountain_altitude AS FLOAT) / NULLIF(dhaulagiri.mountain_altitude, 0)) AS altitude_ratio FROM (SELECT mountain_altitude FROM mountain WHERE LOWER(mountain_name) LIKE '%mount everest%') AS everest, (SELECT mountain_altitude FROM mountain WHERE LOWER(mountain_name) LIKE '%dhaulagiri%') AS dhaulagiri

EXAMPLE QUESTION 2: What is the ratio of the population of the United States to the population of California?
EXAMPLE QUERY 2: SELECT CAST(SUM(NULLIF(state.population, 0)) FILTER (WHERE LOWER(state.country_name) LIKE '%united states%') AS FLOAT) / CAST(SUM(NULLIF(state.population, 0)) FILTER (WHERE LOWER(state.state_name) LIKE '%california%') AS FLOAT) AS population_ratio FROM state

"
What is the ratio of the altitude of Mount Everest to the altitude of Dhaulagiri?,"SELECT (CAST(everest.mountain_altitude AS FLOAT) / NULLIF(dhaulagiri.mountain_altitude, 0)) AS altitude_ratio FROM (SELECT mountain_altitude FROM mountain WHERE LOWER(mountain_name) LIKE '%mount everest%') AS everest, (SELECT mountain_altitude FROM mountain WHERE LOWER(mountain_name) LIKE '%dhaulagiri%') AS dhaulagiri;",geography,ratio,"EXAMPLE QUESTION 1: What is the ratio of the length of the Mississippi River to the length of the Rhine River?
EXAMPLE QUERY 1: SELECT CAST((SELECT LENGTH FROM river WHERE LOWER(river_name) LIKE '%mississippi%') AS FLOAT) / NULLIF((SELECT LENGTH FROM river WHERE LOWER(river_name) LIKE '%rhine%'), 0) AS ratio

EXAMPLE QUESTION 2: What are the names and altitudes of the mountains in Nepal?
EXAMPLE QUERY 2: SELECT mountain_name, mountain_altitude FROM mountain WHERE country_name ILIKE '%Nepal%'

"
"How does the population of each city vary in relation to the population of its corresponding state? Return the city name, and the proportion of each city's population relative to the state.","SELECT city.city_name, CAST(city.population AS float) / NULLIF(state.population, 0) AS population_ratio FROM city JOIN state ON city.state_name = state.state_name ORDER BY population_ratio DESC NULLS LAST;",geography,ratio,"EXAMPLE QUESTION 1: Get the ratio of population per area for each state
EXAMPLE QUERY 1: SELECT state_name, population / NULLIF(area, 0) AS population_density FROM state

EXAMPLE QUESTION 2: What is the total population in cities by country?
EXAMPLE QUERY 2: SELECT city.country_name, SUM(city.population) AS total_population FROM city GROUP BY city.country_name ORDER BY total_population DESC NULLS LAST

"
Get the ratio of population per area for each state,"SELECT state_name, population / NULLIF(area, 0) AS population_density FROM state;",geography,ratio,"EXAMPLE QUESTION 1: How does the population of each city vary in relation to the population of its corresponding state? Return the city name, and the proportion of each city's population relative to the state.
EXAMPLE QUERY 1: SELECT city.city_name, CAST(city.population AS float) / NULLIF(state.population, 0) AS population_ratio FROM city JOIN state ON city.state_name = state.state_name ORDER BY population_ratio DESC NULLS LAST

EXAMPLE QUESTION 2: What is the highest point in each state and what is the population density of that state?
EXAMPLE QUERY 2: SELECT highlow.state_name, highlow.highest_point, state.density FROM highlow JOIN state ON highlow.state_name = state.state_name GROUP BY highlow.state_name, highlow.highest_point, state.density

"
Which countries have both lakes and rivers?,SELECT DISTINCT lake.country_name FROM public.lake JOIN public.river ON lake.country_name = river.country_name;,geography,table_join,"EXAMPLE QUESTION 1: What is the average length of rivers per country in countries with a lake?
EXAMPLE QUERY 1: SELECT l.country_name, AVG(r.length) AS average_length FROM river r JOIN lake l ON r.country_name = l.country_name GROUP BY 1

EXAMPLE QUESTION 2: How many rivers flow through each country?
EXAMPLE QUERY 2: SELECT river.country_name, COUNT(DISTINCT river.river_name) AS number_of_rivers FROM river GROUP BY river.country_name ORDER BY number_of_rivers DESC

"
Which states border the state where lake ontario is?,SELECT border_info.border FROM border_info JOIN lake ON border_info.state_name = lake.state_name WHERE lake.lake_name ilike '%Ontario%';,geography,table_join,"EXAMPLE QUESTION 1: Which states have fewer than a hundred thousand people?
EXAMPLE QUERY 1: SELECT state_name FROM state WHERE population < 100000

EXAMPLE QUESTION 2: What is the highest point in each state and what is the population density of that state?
EXAMPLE QUERY 2: SELECT highlow.state_name, highlow.highest_point, state.density FROM highlow JOIN state ON highlow.state_name = state.state_name GROUP BY highlow.state_name, highlow.highest_point, state.density

"
"Which lakes have a name that starts with ""Lake""? They should be located in states with an area greater than 1000 square kilometers.",SELECT lake.lake_name FROM lake JOIN state ON lake.state_name = state.state_name WHERE state.area > 1000 AND lake.lake_name ilike 'Lake%' ORDER BY lake.lake_name NULLS LAST;,geography,table_join,"EXAMPLE QUESTION 1: Which lakes have the largest areas in square kilometers, ordered from largest to smallest?
EXAMPLE QUERY 1: SELECT lake.lake_name, lake.area FROM lake ORDER BY lake.area DESC NULLS LAST

EXAMPLE QUESTION 2: How many lakes are there in each state?
EXAMPLE QUERY 2: SELECT lake.state_name, COUNT(lake.lake_name) AS lake_count FROM lake GROUP BY lake.state_name ORDER BY lake_count DESC

"
What is the highest point in each state and what is the population density of that state?,"SELECT highlow.state_name, highlow.highest_point, state.density FROM highlow JOIN state ON highlow.state_name = state.state_name GROUP BY highlow.state_name, highlow.highest_point, state.density;",geography,table_join,"EXAMPLE QUESTION 1: Which states have the highest population density in people per square kilometer, ordered from highest to lowest?
EXAMPLE QUERY 1: SELECT state.state_name, state.density FROM state ORDER BY state.density DESC NULLS LAST

EXAMPLE QUESTION 2: Get the ratio of population per area for each state
EXAMPLE QUERY 2: SELECT state_name, population / NULLIF(area, 0) AS population_density FROM state

"
What is the average length of rivers per country in countries with a lake?,"SELECT l.country_name, AVG(r.length) AS average_length FROM river r JOIN lake l ON r.country_name = l.country_name GROUP BY 1;",geography,table_join,"EXAMPLE QUESTION 1: What is the average length of rivers in each country?
EXAMPLE QUERY 1: SELECT river.country_name, AVG(river.length) AS average_length FROM river GROUP BY river.country_name ORDER BY average_length DESC NULLS LAST

EXAMPLE QUESTION 2: Which countries have both lakes and rivers?
EXAMPLE QUERY 2: SELECT DISTINCT lake.country_name FROM public.lake JOIN public.river ON lake.country_name = river.country_name

"
Which states have fewer than a hundred thousand people?,SELECT state_name FROM state WHERE population < 100000;,geography,where,"EXAMPLE QUESTION 1: Which states border the state where lake ontario is?
EXAMPLE QUERY 1: SELECT border_info.border FROM border_info JOIN lake ON border_info.state_name = lake.state_name WHERE lake.lake_name ilike '%Ontario%'

EXAMPLE QUESTION 2: What are the top 5 cities with the highest population? Give both city names and the population.
EXAMPLE QUERY 2: SELECT city.city_name, city.population FROM city ORDER BY city.population DESC NULLS LAST LIMIT 5

"
Which rivers traverse at least 3 cities/landmarks?,"SELECT river_name FROM river WHERE traverse LIKE '%,%,%';",geography,where,"EXAMPLE QUESTION 1: How many rivers flow through each country?
EXAMPLE QUERY 1: SELECT river.country_name, COUNT(DISTINCT river.river_name) AS number_of_rivers FROM river GROUP BY river.country_name ORDER BY number_of_rivers DESC

EXAMPLE QUESTION 2: What are the names and areas of the lakes in Michigan?
EXAMPLE QUERY 2: SELECT lake_name, area FROM lake WHERE state_name ILIKE '%Michigan%'

"
What are the names and areas of the lakes in Michigan?,"SELECT lake_name, area FROM lake WHERE state_name ILIKE '%Michigan%';",geography,where,"EXAMPLE QUESTION 1: Which lakes have a name that starts with ""Lake""? They should be located in states with an area greater than 1000 square kilometers.
EXAMPLE QUERY 1: SELECT lake.lake_name FROM lake JOIN state ON lake.state_name = state.state_name WHERE state.area > 1000 AND lake.lake_name ilike 'Lake%' ORDER BY lake.lake_name NULLS LAST

EXAMPLE QUESTION 2: Which lakes have the largest areas in square kilometers, ordered from largest to smallest?
EXAMPLE QUERY 2: SELECT lake.lake_name, lake.area FROM lake ORDER BY lake.area DESC NULLS LAST

"
What are the names and altitudes of the mountains in Nepal?,"SELECT mountain_name, mountain_altitude FROM mountain WHERE country_name ILIKE '%Nepal%';",geography,where,"EXAMPLE QUESTION 1: What are the highest mountains in meters, ordered from highest to lowest altitude?
EXAMPLE QUERY 1: SELECT mountain.mountain_name, mountain.mountain_altitude FROM mountain ORDER BY mountain.mountain_altitude DESC NULLS LAST

EXAMPLE QUESTION 2: What are the names and areas of the lakes in Michigan?
EXAMPLE QUERY 2: SELECT lake_name, area FROM lake WHERE state_name ILIKE '%Michigan%'

"
Get the cities in the United States and their population,"SELECT city_name, population FROM city WHERE country_name ILIKE '%United States%';",geography,where,"EXAMPLE QUESTION 1: What are the top 5 cities with the highest population? Give both city names and the population.
EXAMPLE QUERY 1: SELECT city.city_name, city.population FROM city ORDER BY city.population DESC NULLS LAST LIMIT 5

EXAMPLE QUESTION 2: What is the total population in cities by country?
EXAMPLE QUERY 2: SELECT city.country_name, SUM(city.population) AS total_population FROM city GROUP BY city.country_name ORDER BY total_population DESC NULLS LAST

"
What is the total number of restaurants serving each type of food?,"SELECT restaurant.food_type, COUNT(DISTINCT restaurant.id) AS total_number_of_restaurants FROM restaurant GROUP BY restaurant.food_type;",restaurants,group_by,"EXAMPLE QUESTION 1: What is the average rating of restaurants serving each type of food?
EXAMPLE QUERY 1: SELECT restaurant.food_type, AVG(restaurant.rating) AS average_rating FROM restaurant GROUP BY restaurant.food_type ORDER BY average_rating DESC NULLS LAST

EXAMPLE QUESTION 2: What is the total count of restaurants in each city?
EXAMPLE QUERY 2: SELECT location.city_name, COUNT(DISTINCT location.restaurant_id) AS total_count FROM LOCATION GROUP BY location.city_name

"
What is the total count of restaurants in each city?,"SELECT location.city_name, COUNT(DISTINCT location.restaurant_id) AS total_count FROM LOCATION GROUP BY location.city_name;",restaurants,group_by,"EXAMPLE QUESTION 1: Which restaurants have the same name in each city, and what is the count?
EXAMPLE QUERY 1: SELECT r.city_name, COUNT(r.id) AS restaurant_count FROM restaurant r GROUP BY r.city_name, r.name HAVING COUNT(r.id) > 1

EXAMPLE QUESTION 2: How many restaurants are there in each region?
EXAMPLE QUERY 2: SELECT geographic.region, COUNT(DISTINCT location.restaurant_id) AS number_of_restaurants FROM geographic JOIN LOCATION ON geographic.city_name = location.city_name GROUP BY geographic.region ORDER BY number_of_restaurants DESC

"
What is the average rating of restaurants serving each type of food?,"SELECT restaurant.food_type, AVG(restaurant.rating) AS average_rating FROM restaurant GROUP BY restaurant.food_type ORDER BY average_rating DESC NULLS LAST;",restaurants,group_by,"EXAMPLE QUESTION 1: What is the total number of restaurants serving each type of food?
EXAMPLE QUERY 1: SELECT restaurant.food_type, COUNT(DISTINCT restaurant.id) AS total_number_of_restaurants FROM restaurant GROUP BY restaurant.food_type

EXAMPLE QUESTION 2: What is the average rating of restaurants in each region?
EXAMPLE QUERY 2: SELECT geographic.region, AVG(restaurant.rating) AS average_rating FROM geographic JOIN restaurant ON geographic.city_name=restaurant.city_name GROUP BY 1

"
How many restaurants serve Italian food in each city?,"SELECT location.city_name, COUNT(*) AS number_of_restaurants FROM LOCATION JOIN restaurant ON location.restaurant_id = restaurant.id WHERE restaurant.food_type ILIKE '%Italian%' GROUP BY location.city_name ORDER BY number_of_restaurants DESC NULLS LAST;",restaurants,group_by,"EXAMPLE QUESTION 1: How many restaurants serve Italian food in each region?
EXAMPLE QUERY 1: SELECT geographic.region, COUNT(restaurant.id) AS number_of_restaurants FROM restaurant JOIN geographic ON restaurant.city_name = geographic.city_name WHERE LOWER(restaurant.food_type) LIKE '%italian%' GROUP BY geographic.region ORDER BY number_of_restaurants DESC NULLS LAST

EXAMPLE QUESTION 2: What are the names of the restaurants that serve Italian food?
EXAMPLE QUERY 2: SELECT restaurant.name FROM restaurant WHERE LOWER(restaurant.food_type) ILIKE '%italian%' ORDER BY restaurant.rating DESC NULLS LAST

"
How many restaurants are there in each city? Order the results by the number of restaurants in descending order.,"SELECT location.city_name, COUNT(DISTINCT location.restaurant_id) AS number_of_restaurants FROM LOCATION GROUP BY location.city_name ORDER BY number_of_restaurants DESC NULLS LAST;",restaurants,group_by,"EXAMPLE QUESTION 1: What is the average rating of restaurants in each region? Order the results by the region name.
EXAMPLE QUERY 1: SELECT geographic.region, AVG(restaurant.rating) AS average_rating FROM restaurant JOIN geographic ON restaurant.city_name = geographic.city_name GROUP BY geographic.region ORDER BY geographic.region NULLS LAST

EXAMPLE QUESTION 2: What is the total count of restaurants in each city?
EXAMPLE QUERY 2: SELECT location.city_name, COUNT(DISTINCT location.restaurant_id) AS total_count FROM LOCATION GROUP BY location.city_name

"
Which street has the most number of restaurants?,SELECT DISTINCT location.street_name FROM LOCATION WHERE street_name = (SELECT street_name FROM LOCATION GROUP BY 1 ORDER BY COUNT(restaurant_id) DESC LIMIT 1);,restaurants,order_by,"EXAMPLE QUESTION 1: Which restaurants have the same name in each city, and what is the count?
EXAMPLE QUERY 1: SELECT r.city_name, COUNT(r.id) AS restaurant_count FROM restaurant r GROUP BY r.city_name, r.name HAVING COUNT(r.id) > 1

EXAMPLE QUESTION 2: What is the total count of restaurants in each city?
EXAMPLE QUERY 2: SELECT location.city_name, COUNT(DISTINCT location.restaurant_id) AS total_count FROM LOCATION GROUP BY location.city_name

"
Which restaurants serve Italian cuisine or are located in New York? Order the results by the restaurant name.,SELECT restaurant.name FROM restaurant JOIN LOCATION ON restaurant.id = location.restaurant_id WHERE restaurant.food_type ILIKE '%Italian%' OR location.city_name ILIKE '%New York%' ORDER BY restaurant.name NULLS LAST;,restaurants,order_by,"EXAMPLE QUESTION 1: What's the name and food type of all the restaurants located on Market St in San Francisco?
EXAMPLE QUERY 1: SELECT restaurant.name, restaurant.food_type FROM restaurant JOIN LOCATION ON restaurant.id = location.restaurant_id WHERE location.street_name ILIKE '%Market St%' AND location.city_name ILIKE '%San Francisco%'

EXAMPLE QUESTION 2: What are the names of the restaurants that serve Italian food?
EXAMPLE QUERY 2: SELECT restaurant.name FROM restaurant WHERE LOWER(restaurant.food_type) ILIKE '%italian%' ORDER BY restaurant.rating DESC NULLS LAST

"
What is the average rating of restaurants in each region? Order the results by the region name.,"SELECT geographic.region, AVG(restaurant.rating) AS average_rating FROM restaurant JOIN geographic ON restaurant.city_name = geographic.city_name GROUP BY geographic.region ORDER BY geographic.region NULLS LAST;",restaurants,order_by,"EXAMPLE QUESTION 1: How many restaurants are there in each region?
EXAMPLE QUERY 1: SELECT geographic.region, COUNT(DISTINCT location.restaurant_id) AS number_of_restaurants FROM geographic JOIN LOCATION ON geographic.city_name = location.city_name GROUP BY geographic.region ORDER BY number_of_restaurants DESC

EXAMPLE QUESTION 2: What's the name and rating of all the restaurants that have a rating greater than 4 and are located in the city of New York?
EXAMPLE QUERY 2: SELECT restaurant.name, restaurant.rating FROM restaurant WHERE restaurant.rating > 4 AND restaurant.city_name ILIKE '%New York%'

"
What are the names of the top 3 restaurants with the highest ratings?,SELECT restaurant.name FROM restaurant ORDER BY restaurant.rating DESC NULLS LAST LIMIT 3;,restaurants,order_by,"EXAMPLE QUESTION 1: What are the names of the restaurants in Los Angeles that have a rating higher than 4?
EXAMPLE QUERY 1: SELECT DISTINCT restaurant.name FROM restaurant WHERE restaurant.city_name ILIKE '%Los Angeles%' AND restaurant.rating > 4 ORDER BY restaurant.name NULLS LAST

EXAMPLE QUESTION 2: What's the name and rating of all the restaurants that have a rating greater than 4 and are located in the city of New York?
EXAMPLE QUERY 2: SELECT restaurant.name, restaurant.rating FROM restaurant WHERE restaurant.rating > 4 AND restaurant.city_name ILIKE '%New York%'

"
List the restaurants starting from the best ratings to the lowest,"SELECT {name, id}, rating FROM restaurant ORDER BY rating DESC;",restaurants,order_by,"EXAMPLE QUESTION 1: What are the names of the top 3 restaurants with the highest ratings?
EXAMPLE QUERY 1: SELECT restaurant.name FROM restaurant ORDER BY restaurant.rating DESC NULLS LAST LIMIT 3

EXAMPLE QUESTION 2: Which city has the highest-rated restaurant?
EXAMPLE QUERY 2: SELECT DISTINCT restaurant.city_name FROM restaurant WHERE rating=(SELECT MAX(rating) FROM restaurant)

"
What is the ratio of restaurants with rating > 4.5 to the total number of restaurants in the database.,SELECT COUNT(*)::float / (SELECT COUNT(*) FROM restaurant) AS rating_ratio FROM restaurant WHERE rating > 4.5;,restaurants,ratio,"EXAMPLE QUESTION 1: What is the ratio of restaurants with a rating above 4 to restaurants with a rating below 4 in New York?
EXAMPLE QUERY 1: SELECT CAST(COUNT(CASE WHEN rating > 4 THEN 1 END) AS FLOAT) / NULLIF(COUNT(CASE WHEN rating < 4 THEN 1 END), 0) AS ratio FROM restaurant WHERE city_name ILIKE 'New York'

EXAMPLE QUESTION 2: What is the ratio of restaurants with a rating above 4.0 to restaurants with a rating below 4.0 overall?
EXAMPLE QUERY 2: SELECT CAST(SUM(CASE WHEN restaurant.rating > 4.0 THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN restaurant.rating < 4.0 THEN 1 ELSE 0 END), 0) AS ratio FROM restaurant

"
What is the ratio of restaurants with a rating above 4.0 to restaurants with a rating below 4.0 overall?,"SELECT CAST(SUM(CASE WHEN restaurant.rating > 4.0 THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN restaurant.rating < 4.0 THEN 1 ELSE 0 END), 0) AS ratio FROM restaurant;",restaurants,ratio,"EXAMPLE QUESTION 1: What is the ratio of restaurants with a rating above 4 to restaurants with a rating below 4 in New York?
EXAMPLE QUERY 1: SELECT CAST(COUNT(CASE WHEN rating > 4 THEN 1 END) AS FLOAT) / NULLIF(COUNT(CASE WHEN rating < 4 THEN 1 END), 0) AS ratio FROM restaurant WHERE city_name ILIKE 'New York'

EXAMPLE QUESTION 2: What is the ratio of restaurants with rating > 4.5 to the total number of restaurants in the database.
EXAMPLE QUERY 2: SELECT COUNT(*)::float / (SELECT COUNT(*) FROM restaurant) AS rating_ratio FROM restaurant WHERE rating > 4.5

"
What is the ratio of restaurants with a rating above 4 to restaurants with a rating below 4 in New York?,"SELECT CAST(COUNT(CASE WHEN rating > 4 THEN 1 END) AS FLOAT) / NULLIF(COUNT(CASE WHEN rating < 4 THEN 1 END), 0) AS ratio FROM restaurant WHERE city_name ILIKE 'New York';",restaurants,ratio,"EXAMPLE QUESTION 1: What is the ratio of restaurants with a rating above 4.0 to restaurants with a rating below 4.0 overall?
EXAMPLE QUERY 1: SELECT CAST(SUM(CASE WHEN restaurant.rating > 4.0 THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN restaurant.rating < 4.0 THEN 1 ELSE 0 END), 0) AS ratio FROM restaurant

EXAMPLE QUESTION 2: What is the ratio of restaurants with rating > 4.5 to the total number of restaurants in the database.
EXAMPLE QUERY 2: SELECT COUNT(*)::float / (SELECT COUNT(*) FROM restaurant) AS rating_ratio FROM restaurant WHERE rating > 4.5

"
What is the ratio of restaurants serving vegan food to restaurants serving non-vegan food in San Francisco?,"SELECT CAST(SUM(CASE WHEN LOWER(restaurant.food_type) LIKE '%vegan%' THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN LOWER(restaurant.food_type) NOT LIKE '%vegan%' THEN 1 ELSE 0 END), 0) AS ratio FROM restaurant WHERE LOWER(restaurant.city_name) ILIKE '%san francisco%';",restaurants,ratio,"EXAMPLE QUESTION 1: What is the ratio of restaurants with a rating above 4 to restaurants with a rating below 4 in New York?
EXAMPLE QUERY 1: SELECT CAST(COUNT(CASE WHEN rating > 4 THEN 1 END) AS FLOAT) / NULLIF(COUNT(CASE WHEN rating < 4 THEN 1 END), 0) AS ratio FROM restaurant WHERE city_name ILIKE 'New York'

EXAMPLE QUESTION 2: What is the ratio of Italian restaurants out of all restaurants in Los Angeles?
EXAMPLE QUERY 2: SELECT CAST(COUNT(CASE WHEN food_type ILIKE '%Italian%' THEN 1 END) AS FLOAT) / NULLIF(COUNT(food_type), 0) AS ratio FROM restaurant WHERE city_name ILIKE '%Los Angeles%'

"
What is the ratio of Italian restaurants out of all restaurants in Los Angeles?,"SELECT CAST(COUNT(CASE WHEN food_type ILIKE '%Italian%' THEN 1 END) AS FLOAT) / NULLIF(COUNT(food_type), 0) AS ratio FROM restaurant WHERE city_name ILIKE '%Los Angeles%';",restaurants,ratio,"EXAMPLE QUESTION 1: What is the ratio of restaurants with a rating above 4 to restaurants with a rating below 4 in New York?
EXAMPLE QUERY 1: SELECT CAST(COUNT(CASE WHEN rating > 4 THEN 1 END) AS FLOAT) / NULLIF(COUNT(CASE WHEN rating < 4 THEN 1 END), 0) AS ratio FROM restaurant WHERE city_name ILIKE 'New York'

EXAMPLE QUESTION 2: What is the ratio of restaurants with a rating above 4.0 to restaurants with a rating below 4.0 overall?
EXAMPLE QUERY 2: SELECT CAST(SUM(CASE WHEN restaurant.rating > 4.0 THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN restaurant.rating < 4.0 THEN 1 ELSE 0 END), 0) AS ratio FROM restaurant

"
"Which restaurants have the same name in each city, and what is the count?","SELECT {r.city_name, r.name}, COUNT(r.id) AS restaurant_count FROM restaurant r GROUP BY r.city_name, r.name HAVING COUNT(r.id) > 1;",restaurants,table_join,"EXAMPLE QUESTION 1: What is the total count of restaurants in each city?
EXAMPLE QUERY 1: SELECT location.city_name, COUNT(DISTINCT location.restaurant_id) AS total_count FROM LOCATION GROUP BY location.city_name

EXAMPLE QUESTION 2: Which street has the most number of restaurants?
EXAMPLE QUERY 2: SELECT DISTINCT location.street_name FROM LOCATION WHERE street_name = (SELECT street_name FROM LOCATION GROUP BY 1 ORDER BY COUNT(restaurant_id) DESC LIMIT 1)

"
What is the average rating of restaurants that serve Mexican food in each city?,"SELECT location.city_name, AVG(restaurant.rating) AS average_rating FROM restaurant JOIN LOCATION ON restaurant.id = location.restaurant_id WHERE LOWER(restaurant.food_type) LIKE '%mexican%' GROUP BY location.city_name;",restaurants,table_join,"EXAMPLE QUESTION 1: What is the average rating of restaurants serving each type of food?
EXAMPLE QUERY 1: SELECT restaurant.food_type, AVG(restaurant.rating) AS average_rating FROM restaurant GROUP BY restaurant.food_type ORDER BY average_rating DESC NULLS LAST

EXAMPLE QUESTION 2: What is the average rating of restaurants in each region?
EXAMPLE QUERY 2: SELECT geographic.region, AVG(restaurant.rating) AS average_rating FROM geographic JOIN restaurant ON geographic.city_name=restaurant.city_name GROUP BY 1

"
What is the average rating of restaurants in each region?,"SELECT geographic.region, AVG(restaurant.rating) AS average_rating FROM geographic JOIN restaurant ON geographic.city_name=restaurant.city_name GROUP BY 1;",restaurants,table_join,"EXAMPLE QUESTION 1: What is the average rating of restaurants in each region? Order the results by the region name.
EXAMPLE QUERY 1: SELECT geographic.region, AVG(restaurant.rating) AS average_rating FROM restaurant JOIN geographic ON restaurant.city_name = geographic.city_name GROUP BY geographic.region ORDER BY geographic.region NULLS LAST

EXAMPLE QUESTION 2: What is the average rating of restaurants serving each type of food?
EXAMPLE QUERY 2: SELECT restaurant.food_type, AVG(restaurant.rating) AS average_rating FROM restaurant GROUP BY restaurant.food_type ORDER BY average_rating DESC NULLS LAST

"
How many restaurants serve Italian food in each region?,"SELECT geographic.region, COUNT(restaurant.id) AS number_of_restaurants FROM restaurant JOIN geographic ON restaurant.city_name = geographic.city_name WHERE LOWER(restaurant.food_type) LIKE '%italian%' GROUP BY geographic.region ORDER BY number_of_restaurants DESC NULLS LAST;",restaurants,table_join,"EXAMPLE QUESTION 1: How many restaurants serve Italian food in each city?
EXAMPLE QUERY 1: SELECT location.city_name, COUNT(*) AS number_of_restaurants FROM LOCATION JOIN restaurant ON location.restaurant_id = restaurant.id WHERE restaurant.food_type ILIKE '%Italian%' GROUP BY location.city_name ORDER BY number_of_restaurants DESC NULLS LAST

EXAMPLE QUESTION 2: What are the names of the restaurants that serve Italian food?
EXAMPLE QUERY 2: SELECT restaurant.name FROM restaurant WHERE LOWER(restaurant.food_type) ILIKE '%italian%' ORDER BY restaurant.rating DESC NULLS LAST

"
How many restaurants are there in each region?,"SELECT geographic.region, COUNT(DISTINCT location.restaurant_id) AS number_of_restaurants FROM geographic JOIN LOCATION ON geographic.city_name = location.city_name GROUP BY geographic.region ORDER BY number_of_restaurants DESC;SELECT geographic.region, COUNT(DISTINCT location.restaurant_id) AS number_of_restaurants FROM geographic LEFT JOIN LOCATION ON geographic.city_name = location.city_name GROUP BY geographic.region ORDER BY number_of_restaurants DESC;",restaurants,table_join,"EXAMPLE QUESTION 1: How many restaurants serve Italian food in each region?
EXAMPLE QUERY 1: SELECT geographic.region, COUNT(restaurant.id) AS number_of_restaurants FROM restaurant JOIN geographic ON restaurant.city_name = geographic.city_name WHERE LOWER(restaurant.food_type) LIKE '%italian%' GROUP BY geographic.region ORDER BY number_of_restaurants DESC NULLS LAST

EXAMPLE QUESTION 2: What is the total count of restaurants in each city?
EXAMPLE QUERY 2: SELECT location.city_name, COUNT(DISTINCT location.restaurant_id) AS total_count FROM LOCATION GROUP BY location.city_name

"
Which city has the highest-rated restaurant?,SELECT DISTINCT restaurant.city_name FROM restaurant WHERE rating=(SELECT MAX(rating) FROM restaurant);,restaurants,where,"EXAMPLE QUESTION 1: What's the name and rating of all the restaurants that have a rating greater than 4 and are located in the city of New York?
EXAMPLE QUERY 1: SELECT restaurant.name, restaurant.rating FROM restaurant WHERE restaurant.rating > 4 AND restaurant.city_name ILIKE '%New York%'

EXAMPLE QUESTION 2: What are the names of the top 3 restaurants with the highest ratings?
EXAMPLE QUERY 2: SELECT restaurant.name FROM restaurant ORDER BY restaurant.rating DESC NULLS LAST LIMIT 3

"
What's the name and rating of all the restaurants that have a rating greater than 4 and are located in the city of New York?,"SELECT {restaurant.name, restaurant.id}, restaurant.rating FROM restaurant WHERE restaurant.rating > 4 AND restaurant.city_name ILIKE '%New York%';",restaurants,where,"EXAMPLE QUESTION 1: What are the names of the restaurants in Los Angeles that have a rating higher than 4?
EXAMPLE QUERY 1: SELECT DISTINCT restaurant.name FROM restaurant WHERE restaurant.city_name ILIKE '%Los Angeles%' AND restaurant.rating > 4 ORDER BY restaurant.name NULLS LAST

EXAMPLE QUESTION 2: What is the ratio of restaurants with a rating above 4 to restaurants with a rating below 4 in New York?
EXAMPLE QUERY 2: SELECT CAST(COUNT(CASE WHEN rating > 4 THEN 1 END) AS FLOAT) / NULLIF(COUNT(CASE WHEN rating < 4 THEN 1 END), 0) AS ratio FROM restaurant WHERE city_name ILIKE 'New York'

"
What's the name and food type of all the restaurants located on Market St in San Francisco?,"SELECT restaurant.name, restaurant.food_type FROM restaurant JOIN LOCATION ON restaurant.id = location.restaurant_id WHERE location.street_name ILIKE '%Market St%' AND location.city_name ILIKE '%San Francisco%';",restaurants,where,"EXAMPLE QUESTION 1: What are the names of the restaurants in Los Angeles that have a rating higher than 4?
EXAMPLE QUERY 1: SELECT DISTINCT restaurant.name FROM restaurant WHERE restaurant.city_name ILIKE '%Los Angeles%' AND restaurant.rating > 4 ORDER BY restaurant.name NULLS LAST

EXAMPLE QUESTION 2: Which restaurants serve Italian cuisine or are located in New York? Order the results by the restaurant name.
EXAMPLE QUERY 2: SELECT restaurant.name FROM restaurant JOIN LOCATION ON restaurant.id = location.restaurant_id WHERE restaurant.food_type ILIKE '%Italian%' OR location.city_name ILIKE '%New York%' ORDER BY restaurant.name NULLS LAST

"
What are the names of the restaurants that serve Italian food?,SELECT restaurant.name FROM restaurant WHERE LOWER(restaurant.food_type) ILIKE '%italian%' ORDER BY restaurant.rating DESC NULLS LAST;,restaurants,where,"EXAMPLE QUESTION 1: How many restaurants serve Italian food in each city?
EXAMPLE QUERY 1: SELECT location.city_name, COUNT(*) AS number_of_restaurants FROM LOCATION JOIN restaurant ON location.restaurant_id = restaurant.id WHERE restaurant.food_type ILIKE '%Italian%' GROUP BY location.city_name ORDER BY number_of_restaurants DESC NULLS LAST

EXAMPLE QUESTION 2: How many restaurants serve Italian food in each region?
EXAMPLE QUERY 2: SELECT geographic.region, COUNT(restaurant.id) AS number_of_restaurants FROM restaurant JOIN geographic ON restaurant.city_name = geographic.city_name WHERE LOWER(restaurant.food_type) LIKE '%italian%' GROUP BY geographic.region ORDER BY number_of_restaurants DESC NULLS LAST

"
What are the names of the restaurants in Los Angeles that have a rating higher than 4?,SELECT DISTINCT restaurant.name FROM restaurant WHERE restaurant.city_name ILIKE '%Los Angeles%' AND restaurant.rating > 4 ORDER BY restaurant.name NULLS LAST;,restaurants,where,"EXAMPLE QUESTION 1: What's the name and rating of all the restaurants that have a rating greater than 4 and are located in the city of New York?
EXAMPLE QUERY 1: SELECT restaurant.name, restaurant.rating FROM restaurant WHERE restaurant.rating > 4 AND restaurant.city_name ILIKE '%New York%'

EXAMPLE QUESTION 2: What are the names of the top 3 restaurants with the highest ratings?
EXAMPLE QUERY 2: SELECT restaurant.name FROM restaurant ORDER BY restaurant.rating DESC NULLS LAST LIMIT 3

"
How many authors have written a paper that was published prior to exactly one year ago from today's date?,SELECT count(DISTINCT w.authorid) AS num_authors FROM paper p JOIN writes w ON p.paperid = w.paperid WHERE p.year < extract(YEAR FROM CURRENT_DATE - interval '1 year');,scholar,date_functions,"EXAMPLE QUESTION 1: How many authors wrote papers that were published in the journal ""Science"" in the year 2020?
EXAMPLE QUERY 1: SELECT COUNT(DISTINCT writes.authorid) AS number_of_authors FROM writes JOIN paper ON writes.paperid = paper.paperid JOIN journal ON paper.journalid = journal.journalid WHERE journal.journalname ILIKE '%Science%' AND paper.year = 2020

EXAMPLE QUESTION 2: What is the total number of papers published in each year?
EXAMPLE QUERY 2: SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM paper GROUP BY paper.year ORDER BY paper.year

"
How many keyphrases are associated with papers published between 2020 and 2035?,SELECT count(DISTINCT pk.keyphraseid) AS num_keyphrases FROM paper p JOIN paperkeyphrase pk ON p.paperid = pk.paperid WHERE p.year >= 2020 AND p.year <= 2035 ;,scholar,date_functions,"EXAMPLE QUESTION 1: How many keyphrases are associated with each paper?
EXAMPLE QUERY 1: SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS keyphrase_count FROM paperkeyphrase GROUP BY paperkeyphrase.paperid ORDER BY keyphrase_count DESC NULLS LAST

EXAMPLE QUESTION 2: What is the total number of keyphrases associated with each paper, ordered by the paper ID in ascending order?
EXAMPLE QUERY 2: SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS total_keyphrases FROM paperkeyphrase GROUP BY paperkeyphrase.paperid ORDER BY paperkeyphrase.paperid ASC NULLS LAST

"
What's the number of papers published per year excluding those published in the year that is 6 years before 2025?,"SELECT YEAR, count(*) AS num_papers FROM paper WHERE YEAR != 2025 - 6 GROUP BY YEAR ORDER BY YEAR;",scholar,date_functions,"EXAMPLE QUESTION 1: What is the total number of papers published in each year?
EXAMPLE QUERY 1: SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM paper GROUP BY paper.year ORDER BY paper.year

EXAMPLE QUESTION 2: What is the total number of papers published per year?
EXAMPLE QUERY 2: SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM paper GROUP BY paper.year ORDER BY paper.year NULLS LAST

"
Give me the total number of papers published in the first 12 months of 2019.,SELECT count(*) AS total_papers FROM paper WHERE YEAR = 2019;,scholar,date_functions,"EXAMPLE QUESTION 1: What is the total number of papers published in each year?
EXAMPLE QUERY 1: SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM paper GROUP BY paper.year ORDER BY paper.year

EXAMPLE QUESTION 2: What is the total number of papers published per year?
EXAMPLE QUERY 2: SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM paper GROUP BY paper.year ORDER BY paper.year NULLS LAST

"
"On average, how many papers per month were published in the whole of 2020?",SELECT cast(count(*) AS float)/ 12 AS average_papers_per_month FROM paper WHERE YEAR = 2020;,scholar,date_functions,"EXAMPLE QUESTION 1: What is the total number of papers published per year?
EXAMPLE QUERY 1: SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM paper GROUP BY paper.year ORDER BY paper.year NULLS LAST

EXAMPLE QUESTION 2: What is the total number of papers published in each year?
EXAMPLE QUERY 2: SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM paper GROUP BY paper.year ORDER BY paper.year

"
What is the total number of papers published per year?,"SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM paper GROUP BY paper.year ORDER BY paper.year NULLS LAST;",scholar,group_by,"EXAMPLE QUESTION 1: What is the total number of papers published in each year?
EXAMPLE QUERY 1: SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM paper GROUP BY paper.year ORDER BY paper.year

EXAMPLE QUESTION 2: What's the number of papers published per year excluding those published in the year that is 6 years before 2025?
EXAMPLE QUERY 2: SELECT YEAR, count(*) AS num_papers FROM paper WHERE YEAR != 2025 - 6 GROUP BY YEAR ORDER BY YEAR

"
What is the total number of papers published in each year?,"SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM paper GROUP BY paper.year ORDER BY paper.year;",scholar,group_by,"EXAMPLE QUESTION 1: Give me the total number of papers published in the first 12 months of 2019.
EXAMPLE QUERY 1: SELECT count(*) AS total_papers FROM paper WHERE YEAR = 2019

EXAMPLE QUESTION 2: What is the total number of papers published per year?
EXAMPLE QUERY 2: SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM paper GROUP BY paper.year ORDER BY paper.year NULLS LAST

"
What is the total number of papers associated with each dataset?,"SELECT paperdataset.datasetid, COUNT(DISTINCT paperdataset.paperid) AS total_papers FROM paperdataset GROUP BY paperdataset.datasetid;SELECT dataset.datasetname, COUNT(paperdataset.paperid) AS total_papers FROM paperdataset JOIN dataset ON paperdataset.datasetid = dataset.datasetid GROUP BY dataset.datasetname ORDER BY total_papers DESC NULLS LAST;",scholar,group_by,"EXAMPLE QUESTION 1: What is the proportion of papers that belong to more than 1 dataset to papers that belong to1 dataset?
EXAMPLE QUERY 1: SELECT CAST(COUNT(CASE WHEN dataset_count > 1 THEN 1 END) AS FLOAT) / NULLIF(COUNT(CASE WHEN dataset_count = 1 THEN 1 END), 0) AS ratio FROM (SELECT paperdataset.paperid, COUNT(paperdataset.datasetid) AS dataset_count FROM paperdataset GROUP BY paperdataset.paperid) AS subquery

EXAMPLE QUESTION 2: What is the total number of papers published in each year?
EXAMPLE QUERY 2: SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM paper GROUP BY paper.year ORDER BY paper.year

"
How many keyphrases are associated with each paper?,"SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS keyphrase_count FROM paperkeyphrase GROUP BY paperkeyphrase.paperid ORDER BY keyphrase_count DESC NULLS LAST;",scholar,group_by,"EXAMPLE QUESTION 1: What is the total number of keyphrases associated with each paper, ordered by the paper ID in ascending order?
EXAMPLE QUERY 1: SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS total_keyphrases FROM paperkeyphrase GROUP BY paperkeyphrase.paperid ORDER BY paperkeyphrase.paperid ASC NULLS LAST

EXAMPLE QUESTION 2: How many keyphrases are associated with papers published between 2020 and 2035?
EXAMPLE QUERY 2: SELECT count(DISTINCT pk.keyphraseid) AS num_keyphrases FROM paper p JOIN paperkeyphrase pk ON p.paperid = pk.paperid WHERE p.year >= 2020 AND p.year <= 2035

"
How many authors have published more than 2 papers?,SELECT COUNT(*) AS number_of_authors FROM (SELECT writes.authorid FROM writes GROUP BY writes.authorid HAVING COUNT(writes.paperid) > 2) AS subquery;,scholar,group_by,"EXAMPLE QUESTION 1: How many authors wrote papers that were published in the journal ""Science"" in the year 2020?
EXAMPLE QUERY 1: SELECT COUNT(DISTINCT writes.authorid) AS number_of_authors FROM writes JOIN paper ON writes.paperid = paper.paperid JOIN journal ON paper.journalid = journal.journalid WHERE journal.journalname ILIKE '%Science%' AND paper.year = 2020

EXAMPLE QUESTION 2: What is the ratio of authors who have written 3 or more papers to authors who have written less than 3 papers?
EXAMPLE QUERY 2: SELECT CAST(COUNT(DISTINCT CASE WHEN paper_count >= 3 THEN subquery.authorid END) AS FLOAT) / NULLIF(COUNT(DISTINCT CASE WHEN paper_count < 3 THEN subquery.authorid END), 0) AS ratio FROM (SELECT writes.authorid, COUNT(writes.paperid) AS paper_count FROM writes GROUP BY writes.authorid) AS subquery

"
"Which papers have the highest number of authors, ordered by the number of authors in descending order?","SELECT writes.paperid, COUNT(writes.authorid) AS num_authors FROM writes GROUP BY writes.paperid ORDER BY num_authors DESC NULLS LAST;SELECT paper.title, COUNT(DISTINCT writes.authorid) AS num_authors FROM paper JOIN writes ON paper.paperid = writes.paperid GROUP BY paper.title ORDER BY num_authors DESC;",scholar,order_by,"EXAMPLE QUESTION 1: Which authors have published the most papers, ordered by the number of papers they have published in descending order?
EXAMPLE QUERY 1: SELECT author.authorname, COUNT(DISTINCT writes.paperid) AS number_of_papers FROM author JOIN writes ON author.authorid = writes.authorid GROUP BY author.authorname ORDER BY number_of_papers DESC NULLS LAST

EXAMPLE QUESTION 2: For each paper that cites other papers, how many other papers does it cite? Sort by the number of papers cited in descending order
EXAMPLE QUERY 2: SELECT cite.citingpaperid, COUNT(*) AS citation_count FROM cite GROUP BY cite.citingpaperid ORDER BY citation_count DESC NULLS LAST

"
"What is the total number of keyphrases associated with each paper, ordered by the paper ID in ascending order?","SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS total_keyphrases FROM paperkeyphrase GROUP BY paperkeyphrase.paperid ORDER BY paperkeyphrase.paperid ASC NULLS LAST;",scholar,order_by,"EXAMPLE QUESTION 1: How many keyphrases are associated with each paper?
EXAMPLE QUERY 1: SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS keyphrase_count FROM paperkeyphrase GROUP BY paperkeyphrase.paperid ORDER BY keyphrase_count DESC NULLS LAST

EXAMPLE QUESTION 2: How many keyphrases are associated with papers published between 2020 and 2035?
EXAMPLE QUERY 2: SELECT count(DISTINCT pk.keyphraseid) AS num_keyphrases FROM paper p JOIN paperkeyphrase pk ON p.paperid = pk.paperid WHERE p.year >= 2020 AND p.year <= 2035

"
"What are the titles of the papers published in the year 2020, ordered alphabetically?",SELECT paper.title FROM paper WHERE paper.year = 2020 ORDER BY paper.title ASC NULLS LAST;,scholar,order_by,"EXAMPLE QUESTION 1: What is the total number of papers published in each journal, ordered by the journal name?
EXAMPLE QUERY 1: SELECT journal.journalname, COUNT(DISTINCT paper.paperid) AS total_papers FROM paper JOIN journal ON paper.journalid = journal.journalid GROUP BY journal.journalname ORDER BY journal.journalname NULLS LAST

EXAMPLE QUESTION 2: Which authors have published the most papers, ordered by the number of papers they have published in descending order?
EXAMPLE QUERY 2: SELECT author.authorname, COUNT(DISTINCT writes.paperid) AS number_of_papers FROM author JOIN writes ON author.authorid = writes.authorid GROUP BY author.authorname ORDER BY number_of_papers DESC NULLS LAST

"
"What are the names of the journals in the database, ordered by the length of the journal name from shortest to longest?",SELECT journal.journalname FROM journal ORDER BY LENGTH(journal.journalname) ASC NULLS LAST;,scholar,order_by,"EXAMPLE QUESTION 1: What is the total number of papers published in each journal, ordered by the journal name?
EXAMPLE QUERY 1: SELECT journal.journalname, COUNT(DISTINCT paper.paperid) AS total_papers FROM paper JOIN journal ON paper.journalid = journal.journalid GROUP BY journal.journalname ORDER BY journal.journalname NULLS LAST

EXAMPLE QUESTION 2: What are the titles of the papers published in the year 2020, ordered alphabetically?
EXAMPLE QUERY 2: SELECT paper.title FROM paper WHERE paper.year = 2020 ORDER BY paper.title ASC NULLS LAST

"
"For each paper that cites other papers, how many other papers does it cite? Sort by the number of papers cited in descending order","SELECT cite.citingpaperid, COUNT(*) AS citation_count FROM cite GROUP BY cite.citingpaperid ORDER BY citation_count DESC NULLS LAST;SELECT p.paperid, p.numciting FROM paper p WHERE p.numciting > 0 ORDER BY p.numciting DESC;",scholar,order_by,"EXAMPLE QUESTION 1: Which authors have published the most papers, ordered by the number of papers they have published in descending order?
EXAMPLE QUERY 1: SELECT author.authorname, COUNT(DISTINCT writes.paperid) AS number_of_papers FROM author JOIN writes ON author.authorid = writes.authorid GROUP BY author.authorname ORDER BY number_of_papers DESC NULLS LAST

EXAMPLE QUESTION 2: What is the total number of papers published in each journal, ordered by the journal name?
EXAMPLE QUERY 2: SELECT journal.journalname, COUNT(DISTINCT paper.paperid) AS total_papers FROM paper JOIN journal ON paper.journalid = journal.journalid GROUP BY journal.journalname ORDER BY journal.journalname NULLS LAST

"
What is the ratio of papers that have more than 1 keyphrases to papers that have 1 keyphrase?,"SELECT CAST(COUNT(DISTINCT CASE WHEN keyphrase_count > 1 THEN subquery.paperid END) AS FLOAT) / NULLIF(COUNT(DISTINCT CASE WHEN keyphrase_count =1 THEN subquery.paperid END), 0) AS ratio FROM (SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS keyphrase_count FROM paperkeyphrase GROUP BY paperkeyphrase.paperid) AS subquery;",scholar,ratio,"EXAMPLE QUESTION 1: How many keyphrases are associated with each paper?
EXAMPLE QUERY 1: SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS keyphrase_count FROM paperkeyphrase GROUP BY paperkeyphrase.paperid ORDER BY keyphrase_count DESC NULLS LAST

EXAMPLE QUESTION 2: How many keyphrases are associated with papers published between 2020 and 2035?
EXAMPLE QUERY 2: SELECT count(DISTINCT pk.keyphraseid) AS num_keyphrases FROM paper p JOIN paperkeyphrase pk ON p.paperid = pk.paperid WHERE p.year >= 2020 AND p.year <= 2035

"
What is the ratio of papers that have been cited by 2 or more papers to papers that have been cited by less than 2 papers?,"SELECT CAST(COUNT(CASE WHEN paper.numcitedby > 1 THEN 1 END) AS FLOAT) / NULLIF(COUNT(CASE WHEN paper.numcitedby < 2 THEN 1 END), 0) AS ratio FROM paper;",scholar,ratio,"EXAMPLE QUESTION 1: What is the ratio of authors who have written 3 or more papers to authors who have written less than 3 papers?
EXAMPLE QUERY 1: SELECT CAST(COUNT(DISTINCT CASE WHEN paper_count >= 3 THEN subquery.authorid END) AS FLOAT) / NULLIF(COUNT(DISTINCT CASE WHEN paper_count < 3 THEN subquery.authorid END), 0) AS ratio FROM (SELECT writes.authorid, COUNT(writes.paperid) AS paper_count FROM writes GROUP BY writes.authorid) AS subquery

EXAMPLE QUESTION 2: What is the ratio of papers that have more than 1 keyphrases to papers that have 1 keyphrase?
EXAMPLE QUERY 2: SELECT CAST(COUNT(DISTINCT CASE WHEN keyphrase_count > 1 THEN subquery.paperid END) AS FLOAT) / NULLIF(COUNT(DISTINCT CASE WHEN keyphrase_count =1 THEN subquery.paperid END), 0) AS ratio FROM (SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS keyphrase_count FROM paperkeyphrase GROUP BY paperkeyphrase.paperid) AS subquery

"
What is the ratio of papers published in the year 2020 to the total number of papers in the database?,"SELECT CAST(COUNT(CASE WHEN paper.year = 2020 THEN 1 END) AS FLOAT) / NULLIF(COUNT(paper.paperid), 0) AS ratio FROM paper;",scholar,ratio,"EXAMPLE QUESTION 1: What is the total number of papers published in each year?
EXAMPLE QUERY 1: SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM paper GROUP BY paper.year ORDER BY paper.year

EXAMPLE QUESTION 2: What is the total number of papers published per year?
EXAMPLE QUERY 2: SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM paper GROUP BY paper.year ORDER BY paper.year NULLS LAST

"
What is the ratio of authors who have written 3 or more papers to authors who have written less than 3 papers?,"SELECT CAST(COUNT(DISTINCT CASE WHEN paper_count >= 3 THEN subquery.authorid END) AS FLOAT) / NULLIF(COUNT(DISTINCT CASE WHEN paper_count < 3 THEN subquery.authorid END), 0) AS ratio FROM (SELECT writes.authorid, COUNT(writes.paperid) AS paper_count FROM writes GROUP BY writes.authorid) AS subquery;",scholar,ratio,"EXAMPLE QUESTION 1: What is the ratio of papers that have been cited by 2 or more papers to papers that have been cited by less than 2 papers?
EXAMPLE QUERY 1: SELECT CAST(COUNT(CASE WHEN paper.numcitedby > 1 THEN 1 END) AS FLOAT) / NULLIF(COUNT(CASE WHEN paper.numcitedby < 2 THEN 1 END), 0) AS ratio FROM paper

EXAMPLE QUESTION 2: How many authors have published more than 2 papers?
EXAMPLE QUERY 2: SELECT COUNT(*) AS number_of_authors FROM (SELECT writes.authorid FROM writes GROUP BY writes.authorid HAVING COUNT(writes.paperid) > 2) AS subquery

"
What is the proportion of papers that belong to more than 1 dataset to papers that belong to1 dataset?,"SELECT CAST(COUNT(CASE WHEN dataset_count > 1 THEN 1 END) AS FLOAT) / NULLIF(COUNT(CASE WHEN dataset_count = 1 THEN 1 END), 0) AS ratio FROM (SELECT paperdataset.paperid, COUNT(paperdataset.datasetid) AS dataset_count FROM paperdataset GROUP BY paperdataset.paperid) AS subquery;",scholar,ratio,"EXAMPLE QUESTION 1: What is the total number of papers associated with each dataset?
EXAMPLE QUERY 1: SELECT paperdataset.datasetid, COUNT(DISTINCT paperdataset.paperid) AS total_papers FROM paperdataset GROUP BY paperdataset.datasetid

EXAMPLE QUESTION 2: What is the ratio of papers that have been cited by 2 or more papers to papers that have been cited by less than 2 papers?
EXAMPLE QUERY 2: SELECT CAST(COUNT(CASE WHEN paper.numcitedby > 1 THEN 1 END) AS FLOAT) / NULLIF(COUNT(CASE WHEN paper.numcitedby < 2 THEN 1 END), 0) AS ratio FROM paper

"
"Which papers are associated with the keyphrase ""Machine Learning""?","SELECT {paper.title,paper.paperid} FROM paper JOIN paperkeyphrase ON paper.paperid = paperkeyphrase.paperid JOIN keyphrase ON paperkeyphrase.keyphraseid = keyphrase.keyphraseid WHERE keyphrase.keyphrasename ILIKE '%Machine Learning%';",scholar,table_join,"EXAMPLE QUESTION 1: How many papers are associated with the keyphrase ""machine learning"" and were published in the journal named ""IEEE Transactions on Pattern Analysis and Machine Intelligence""?
EXAMPLE QUERY 1: SELECT COUNT(DISTINCT paper.paperid) FROM paper JOIN journal ON paper.journalid = journal.journalid JOIN paperkeyphrase ON paper.paperid = paperkeyphrase.paperid JOIN keyphrase ON paperkeyphrase.keyphraseid = keyphrase.keyphraseid WHERE keyphrase.keyphrasename ILIKE '%machine learning%' AND journal.journalname ILIKE '%IEEE Transactions on Pattern Analysis and Machine Intelligence%'

EXAMPLE QUESTION 2: How many keyphrases are associated with each paper?
EXAMPLE QUERY 2: SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS keyphrase_count FROM paperkeyphrase GROUP BY paperkeyphrase.paperid ORDER BY keyphrase_count DESC NULLS LAST

"
"Which authors have published the most papers, ordered by the number of papers they have published in descending order?","SELECT {author.authorname, author.authorid}, COUNT(DISTINCT writes.paperid) AS number_of_papers FROM author JOIN writes ON author.authorid = writes.authorid GROUP BY {} ORDER BY number_of_papers DESC NULLS LAST;",scholar,table_join,"EXAMPLE QUESTION 1: Which papers have the highest number of authors, ordered by the number of authors in descending order?
EXAMPLE QUERY 1: SELECT writes.paperid, COUNT(writes.authorid) AS num_authors FROM writes GROUP BY writes.paperid ORDER BY num_authors DESC NULLS LAST

EXAMPLE QUESTION 2: What is the total number of papers published in each journal, ordered by the journal name?
EXAMPLE QUERY 2: SELECT journal.journalname, COUNT(DISTINCT paper.paperid) AS total_papers FROM paper JOIN journal ON paper.journalid = journal.journalid GROUP BY journal.journalname ORDER BY journal.journalname NULLS LAST

"
"What is the total number of unique keyphrases associated with papers published in the journal with ""IEEE Transactions"" in its name?",SELECT COUNT(DISTINCT paperkeyphrase.keyphraseid) AS total_keyphrases FROM paper JOIN journal ON paper.journalid = journal.journalid JOIN paperkeyphrase ON paper.paperid = paperkeyphrase.paperid WHERE journal.journalname ILIKE '%IEEE Transactions%';,scholar,table_join,"EXAMPLE QUESTION 1: What is the total number of keyphrases associated with each paper, ordered by the paper ID in ascending order?
EXAMPLE QUERY 1: SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS total_keyphrases FROM paperkeyphrase GROUP BY paperkeyphrase.paperid ORDER BY paperkeyphrase.paperid ASC NULLS LAST

EXAMPLE QUESTION 2: How many keyphrases are associated with each paper?
EXAMPLE QUERY 2: SELECT paperkeyphrase.paperid, COUNT(paperkeyphrase.keyphraseid) AS keyphrase_count FROM paperkeyphrase GROUP BY paperkeyphrase.paperid ORDER BY keyphrase_count DESC NULLS LAST

"
"What is the total number of papers published in each journal, ordered by the journal name?","SELECT journal.journalname, COUNT(DISTINCT paper.paperid) AS total_papers FROM paper JOIN journal ON paper.journalid = journal.journalid GROUP BY journal.journalname ORDER BY journal.journalname NULLS LAST;",scholar,table_join,"EXAMPLE QUESTION 1: What is the total number of papers published in each year?
EXAMPLE QUERY 1: SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM paper GROUP BY paper.year ORDER BY paper.year

EXAMPLE QUESTION 2: Which authors have published the most papers, ordered by the number of papers they have published in descending order?
EXAMPLE QUERY 2: SELECT author.authorname, COUNT(DISTINCT writes.paperid) AS number_of_papers FROM author JOIN writes ON author.authorid = writes.authorid GROUP BY author.authorname ORDER BY number_of_papers DESC NULLS LAST

"
"How many papers cite each paper in the dataset named ""COVID-19 Research""?","SELECT paperdataset.paperid, COUNT(cite.citedpaperid) AS citation_count FROM paperdataset JOIN cite ON paperdataset.paperid = cite.citedpaperid WHERE paperdataset.datasetid = (SELECT datasetid FROM dataset WHERE datasetname ILIKE '%COVID-19 Research%') GROUP BY paperdataset.paperid ORDER BY citation_count DESC;",scholar,table_join,"EXAMPLE QUESTION 1: What is the total number of papers associated with each dataset?
EXAMPLE QUERY 1: SELECT paperdataset.datasetid, COUNT(DISTINCT paperdataset.paperid) AS total_papers FROM paperdataset GROUP BY paperdataset.datasetid

EXAMPLE QUESTION 2: For each paper that cites other papers, how many other papers does it cite? Sort by the number of papers cited in descending order
EXAMPLE QUERY 2: SELECT cite.citingpaperid, COUNT(*) AS citation_count FROM cite GROUP BY cite.citingpaperid ORDER BY citation_count DESC NULLS LAST

"
"What is the name of the venue where the paper with paper ID 2 was published, and how many papers were published in total in that venue?","SELECT venue.venuename, COUNT(DISTINCT paper.paperid) FROM paper JOIN venue ON paper.venueid = venue.venueid WHERE paper.venueid = (SELECT venueid FROM paper WHERE paperid = 2 ) GROUP BY venue.venuename;",scholar,where,"EXAMPLE QUESTION 1: How many authors have published more than 2 papers?
EXAMPLE QUERY 1: SELECT COUNT(*) AS number_of_authors FROM (SELECT writes.authorid FROM writes GROUP BY writes.authorid HAVING COUNT(writes.paperid) > 2) AS subquery

EXAMPLE QUESTION 2: How many authors wrote papers that were published in the journal ""Science"" in the year 2020?
EXAMPLE QUERY 2: SELECT COUNT(DISTINCT writes.authorid) AS number_of_authors FROM writes JOIN paper ON writes.paperid = paper.paperid JOIN journal ON paper.journalid = journal.journalid WHERE journal.journalname ILIKE '%Science%' AND paper.year = 2020

"
"What are the names of the authors who wrote the paper with the title ""The Effects of Climate Change on Agriculture""?",SELECT author.authorname FROM author JOIN writes ON author.authorid = writes.authorid JOIN paper ON writes.paperid = paper.paperid WHERE paper.title ILIKE '%The Effects of Climate Change on Agriculture%';,scholar,where,"EXAMPLE QUESTION 1: How many authors have published more than 2 papers?
EXAMPLE QUERY 1: SELECT COUNT(*) AS number_of_authors FROM (SELECT writes.authorid FROM writes GROUP BY writes.authorid HAVING COUNT(writes.paperid) > 2) AS subquery

EXAMPLE QUESTION 2: How many authors wrote papers that were published in the journal ""Science"" in the year 2020?
EXAMPLE QUERY 2: SELECT COUNT(DISTINCT writes.authorid) AS number_of_authors FROM writes JOIN paper ON writes.paperid = paper.paperid JOIN journal ON paper.journalid = journal.journalid WHERE journal.journalname ILIKE '%Science%' AND paper.year = 2020

"
"How many papers were published in the journal ""nature"" in the year 2020?",SELECT COUNT(paper.paperid) FROM paper JOIN journal ON paper.journalid = journal.journalid WHERE paper.year = 2020 AND journal.journalname ILIKE '%nature%';,scholar,where,"EXAMPLE QUESTION 1: What is the total number of papers published in each year?
EXAMPLE QUERY 1: SELECT paper.year, COUNT(paper.paperid) AS total_papers FROM paper GROUP BY paper.year ORDER BY paper.year

EXAMPLE QUESTION 2: How many authors wrote papers that were published in the journal ""Science"" in the year 2020?
EXAMPLE QUERY 2: SELECT COUNT(DISTINCT writes.authorid) AS number_of_authors FROM writes JOIN paper ON writes.paperid = paper.paperid JOIN journal ON paper.journalid = journal.journalid WHERE journal.journalname ILIKE '%Science%' AND paper.year = 2020

"
"How many papers are associated with the keyphrase ""machine learning"" and were published in the journal named ""IEEE Transactions on Pattern Analysis and Machine Intelligence""?",SELECT COUNT(DISTINCT paper.paperid) FROM paper JOIN journal ON paper.journalid = journal.journalid JOIN paperkeyphrase ON paper.paperid = paperkeyphrase.paperid JOIN keyphrase ON paperkeyphrase.keyphraseid = keyphrase.keyphraseid WHERE keyphrase.keyphrasename ILIKE '%machine learning%' AND journal.journalname ILIKE '%IEEE Transactions on Pattern Analysis and Machine Intelligence%';,scholar,where,"EXAMPLE QUESTION 1: Which papers are associated with the keyphrase ""Machine Learning""?
EXAMPLE QUERY 1: SELECT paper.title FROM paper JOIN paperkeyphrase ON paper.paperid = paperkeyphrase.paperid JOIN keyphrase ON paperkeyphrase.keyphraseid = keyphrase.keyphraseid WHERE keyphrase.keyphrasename ILIKE '%Machine Learning%'

EXAMPLE QUESTION 2: How many keyphrases are associated with papers published between 2020 and 2035?
EXAMPLE QUERY 2: SELECT count(DISTINCT pk.keyphraseid) AS num_keyphrases FROM paper p JOIN paperkeyphrase pk ON p.paperid = pk.paperid WHERE p.year >= 2020 AND p.year <= 2035

"
"How many authors wrote papers that were published in the journal ""Science"" in the year 2020?",SELECT COUNT(DISTINCT writes.authorid) AS number_of_authors FROM writes JOIN paper ON writes.paperid = paper.paperid JOIN journal ON paper.journalid = journal.journalid WHERE journal.journalname ILIKE '%Science%' AND paper.year = 2020;,scholar,where,"EXAMPLE QUESTION 1: How many authors have published more than 2 papers?
EXAMPLE QUERY 1: SELECT COUNT(*) AS number_of_authors FROM (SELECT writes.authorid FROM writes GROUP BY writes.authorid HAVING COUNT(writes.paperid) > 2) AS subquery

EXAMPLE QUESTION 2: How many authors have written a paper that was published prior to exactly one year ago from today's date?
EXAMPLE QUERY 2: SELECT count(DISTINCT w.authorid) AS num_authors FROM paper p JOIN writes w ON p.paperid = w.paperid WHERE p.year < extract(YEAR FROM CURRENT_DATE - interval '1 year')

"
How many reviews were written for businesses located in California in the last 1000 months?,"SELECT count(*) AS review_count FROM review r JOIN business b ON r.business_id = b.business_id WHERE b.state = 'CA' AND (r.year * 12 + extract(MONTH FROM to_date(r.month, 'Month'))) >= (extract(YEAR FROM CURRENT_DATE) * 12 + extract(MONTH FROM CURRENT_DATE) - 1000) ;",yelp,date_functions,"EXAMPLE QUESTION 1: How many reviews were posted for each business id in the year 2021?
EXAMPLE QUERY 1: SELECT review.business_id, COUNT(*) AS review_count FROM review WHERE review.year = 2021 GROUP BY review.business_id ORDER BY review_count DESC NULLS LAST

EXAMPLE QUESTION 2: What is the total number of reviews posted in the year 2021 for businesses in the category ""Cafe""?
EXAMPLE QUERY 2: SELECT COUNT(review.rid) AS total_reviews FROM review JOIN category ON review.business_id = category.business_id WHERE review.year = 2021 AND category.category_name ILIKE '%Cafe%'

"
What is the total number of check-ins on the 2 days before Saturday?,"SELECT sum(COUNT) AS total_checkins FROM checkin WHERE DAY IN ('Thursday', 'Friday') ;",yelp,date_functions,"EXAMPLE QUESTION 1: How many check-ins occurred on each day of the week?
EXAMPLE QUERY 1: SELECT checkin.day, SUM(checkin.count) AS total_checkins FROM checkin GROUP BY checkin.day ORDER BY total_checkins DESC NULLS LAST

EXAMPLE QUESTION 2: What is the total number of check-ins for each day of the week for the business with ID ""abc123""?
EXAMPLE QUERY 2: SELECT checkin.day, SUM(checkin.count) AS total_checkins FROM checkin WHERE checkin.business_id = 'abc123' GROUP BY checkin.day ORDER BY total_checkins DESC NULLS LAST

"
How many reviews were there 2 months before the review with id 3?,SELECT count(*) AS review_count FROM review WHERE (cast(review.year AS text) || '-' || review.month || '-01')::date = (SELECT (cast(r.year AS text) || '-' || r.month || '-01')::date - interval '2 months' FROM review r WHERE r.rid = 3) ;,yelp,date_functions,"EXAMPLE QUESTION 1: How many reviews were posted in each month of the year 2021, ordered by the month?
EXAMPLE QUERY 1: SELECT review.month, COUNT(review.rid) AS review_count FROM review WHERE review.year = 2021 GROUP BY review.month ORDER BY TO_DATE(review.month, 'MONTH') NULLS LAST

EXAMPLE QUESTION 2: How many reviews were posted for each business id in the year 2021?
EXAMPLE QUERY 2: SELECT review.business_id, COUNT(*) AS review_count FROM review WHERE review.year = 2021 GROUP BY review.business_id ORDER BY review_count DESC NULLS LAST

"
What was the message that came with the tip made exactly 2 months after March 2021?,SELECT text AS message FROM tip WHERE MONTH ILIKE '%May%' AND YEAR = 2021 LIMIT 1;,yelp,date_functions,"EXAMPLE QUESTION 1: How has the quarter-on-quarter (QoQ) trend in no. of tips evolved from 2021 to 2022? Show all quarters in YYYY-MM format with generate_series even if there are no tips in a quarter. Present information about tip count and their QoQ changes. Order in ascending chronological order. Replace nulls with 0 in the QoQ change column. 
EXAMPLE QUERY 1: WITH quarters AS (SELECT generate_series('2021-01-01'::DATE, '2022-12-31'::DATE, '3 months'::interval) AS quarter_start, to_char(generate_series('2021-01-01'::DATE, '2022-12-31'::DATE, '3 months'::interval), 'YYYY-MM') AS quarter_start_char), tip_counts AS (SELECT date_trunc('quarter', to_date(tip.year::text || ' ' || tip.month, 'YYYY Month')) AS QUARTER, COUNT(tip.tip_id) AS tip_count FROM tip WHERE tip.year BETWEEN 2021 AND 2022 GROUP BY date_trunc('quarter', to_date(tip.year::text || ' ' || tip.month, 'YYYY Month'))) SELECT quarters.quarter_start_char, COALESCE(tip_counts.tip_count, 0) AS tip_count, COALESCE(tip_counts.tip_count, 0) - COALESCE(lag(tip_counts.tip_count, 1) OVER (ORDER BY tip_counts.quarter),0) AS qoq_change FROM quarters LEFT JOIN tip_counts ON quarters.quarter_start = tip_counts.quarter ORDER BY quarters.quarter_start NULLS LAST

EXAMPLE QUESTION 2: How has the month-on-month trend in no. of tips evolved from 2021 to 2022? Show all months in YYYY-Month format with generate_series even if there are no tips in a month. Present information about tip count and their month-on-month changes and order in ascending chronological order. Replace nulls with 0 in the month-on-month change column. 
EXAMPLE QUERY 2: WITH months AS (SELECT generate_series(date '2021-01-01', date '2022-12-31', interval '1 month') AS ALL_MONTHS, to_char(generate_series(date '2021-01-01', date '2022-12-31', interval '1 month'), 'YYYY-Month') AS ALL_YEAR_MONTHS), tips AS (SELECT to_char(date_trunc('month', to_date(tip.year::text || ' ' || tip.month, 'YYYY Month')), 'YYYY-Month') AS YEAR_MONTH, COUNT(tip_id) AS tip_count FROM tip WHERE tip.year BETWEEN 2021 AND 2022 GROUP BY tip.year, tip.month) SELECT months.all_year_months, coalesce(tips.tip_count, 0) AS tip_count, coalesce(tips.tip_count, 0) - coalesce(lag(tips.tip_count) OVER (ORDER BY months.all_months), 0) AS month_on_month_change FROM months LEFT JOIN tips ON months.all_year_months = tips.year_month ORDER BY months.all_months

"
How many months between June 2021 and December 2021 had reviews?,SELECT COUNT(DISTINCT MONTH) AS num_months FROM review WHERE YEAR = 2021 AND CASE MONTH WHEN 'January' THEN 1 WHEN 'February' THEN 2 WHEN 'March' THEN 3 WHEN 'April' THEN 4 WHEN 'May' THEN 5 WHEN 'June' THEN 6 WHEN 'July' THEN 7 WHEN 'August' THEN 8 WHEN 'September' THEN 9 WHEN 'October' THEN 10 WHEN 'November' THEN 11 WHEN 'December' THEN 12 END BETWEEN 6 AND 12;,yelp,date_functions,"EXAMPLE QUESTION 1: How many reviews were posted in each month of the year 2021, ordered by the month?
EXAMPLE QUERY 1: SELECT review.month, COUNT(review.rid) AS review_count FROM review WHERE review.year = 2021 GROUP BY review.month ORDER BY TO_DATE(review.month, 'MONTH') NULLS LAST

EXAMPLE QUESTION 2: How many reviews were there 2 months before the review with id 3?
EXAMPLE QUERY 2: SELECT count(*) AS review_count FROM review WHERE (cast(review.year AS text) || '-' || review.month || '-01')::date = (SELECT (cast(r.year AS text) || '-' || r.month || '-01')::date - interval '2 months' FROM review r WHERE r.rid = 3)

"
"Which neighbourhoods have the highest number of businesses, and how many businesses are located in each neighbourhood?","SELECT {neighbourhood.neighbourhood_name, neighbourhood.id}, COUNT(DISTINCT neighbourhood.business_id) AS business_count FROM neighbourhood GROUP BY {} ORDER BY business_count DESC NULLS LAST;",yelp,group_by,"EXAMPLE QUESTION 1: What is the average rating of businesses in the city of San Francisco?
EXAMPLE QUERY 1: SELECT AVG(sf.average_rating) AS sf_average_rating FROM (SELECT business.business_id, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id WHERE LOWER(business.city) ILIKE '%san francisco%' GROUP BY business.business_id) AS sf

EXAMPLE QUESTION 2: What is the ratio of open businesses to closed businesses in the city of San Francisco?
EXAMPLE QUERY 2: SELECT CAST(SUM(CASE WHEN business.is_open = 1 THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN business.is_open = 0 THEN 1 ELSE 0 END), 0) AS ratio FROM business WHERE LOWER(business.city) ILIKE '%san francisco%'

"
"What is the total number of check-ins for each day of the week for the business with ID ""abc123""?","SELECT checkin.day, SUM(checkin.count) AS total_checkins FROM checkin WHERE checkin.business_id = 'abc123' GROUP BY checkin.day ORDER BY total_checkins DESC NULLS LAST;",yelp,group_by,"EXAMPLE QUESTION 1: What is the total number of check-ins on the 2 days before Saturday?
EXAMPLE QUERY 1: SELECT sum(COUNT) AS total_checkins FROM checkin WHERE DAY IN ('Thursday', 'Friday')

EXAMPLE QUESTION 2: What is the total count of check-ins for each business id?
EXAMPLE QUERY 2: SELECT checkin.business_id, SUM(checkin.count) AS total_checkins FROM checkin GROUP BY checkin.business_id ORDER BY total_checkins DESC NULLS LAST

"
What is the total count of check-ins for each business id?,"SELECT checkin.business_id, SUM(checkin.count) AS total_checkins FROM checkin GROUP BY checkin.business_id ORDER BY total_checkins DESC NULLS LAST;",yelp,group_by,"EXAMPLE QUESTION 1: What is the total number of check-ins for each business in the state of California?
EXAMPLE QUERY 1: SELECT business.business_id, SUM(checkin.count) AS total_checkins FROM business JOIN checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' GROUP BY business.business_id ORDER BY total_checkins DESC NULLS LAST

EXAMPLE QUESTION 2: What is the total number of check-ins for each day of the week for the business with ID ""abc123""?
EXAMPLE QUERY 2: SELECT checkin.day, SUM(checkin.count) AS total_checkins FROM checkin WHERE checkin.business_id = 'abc123' GROUP BY checkin.day ORDER BY total_checkins DESC NULLS LAST

"
Return the name and average rating for each business in new york,"SELECT business.name, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id WHERE business.city ILIKE '%NEW YORK%' GROUP BY business.name;",yelp,group_by,"EXAMPLE QUESTION 1: Please provide a list of business names in New York and their average ratings ordered by the highest average rating first.
EXAMPLE QUERY 1: SELECT business.name, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id WHERE business.city ILIKE '%New York%' GROUP BY business.name ORDER BY average_rating DESC NULLS LAST

EXAMPLE QUESTION 2: How does the average rating of businesses in ""New York"" change year on year from 2019 to 2022? Give all years with generate_series even if there are no ratings in a year. Present the ave rating and the year on year change. Replace nulls with 0 in the YoY change column.
EXAMPLE QUERY 2: WITH yearly_ratings AS (SELECT review.year, AVG(review.rating) AS avg_rating FROM review JOIN business ON business.business_id = review.business_id WHERE business.city ilike '%New%York%' AND review.year BETWEEN 2019 AND 2022 GROUP BY review.year), yearly_ratings_with_series AS (SELECT generate_series AS YEAR, coalesce(yearly_ratings.avg_rating, 0) AS avg_rating FROM generate_series(2019, 2022, 1) LEFT JOIN yearly_ratings ON yearly_ratings.year = generate_series) SELECT YEAR, avg_rating, avg_rating - coalesce(lag(avg_rating, 1) OVER (ORDER BY YEAR), 0) AS year_on_year_change FROM yearly_ratings_with_series ORDER BY YEAR NULLS LAST

"
How many check-ins occurred on each day of the week?,"SELECT checkin.day, SUM(checkin.count) AS total_checkins FROM checkin GROUP BY checkin.day ORDER BY total_checkins DESC NULLS LAST;",yelp,group_by,"EXAMPLE QUESTION 1: What is the total number of check-ins on the 2 days before Saturday?
EXAMPLE QUERY 1: SELECT sum(COUNT) AS total_checkins FROM checkin WHERE DAY IN ('Thursday', 'Friday')

EXAMPLE QUESTION 2: How many check-ins occurred on Mondays at businesses in the state of California?
EXAMPLE QUERY 2: SELECT SUM(checkin.count) AS total_checkins FROM business JOIN checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' AND checkin.day ILIKE '%Monday%'

"
Please provide a list of business names in New York and their average ratings ordered by the highest average rating first.,"SELECT business.name, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id WHERE business.city ILIKE '%New York%' GROUP BY business.name ORDER BY average_rating DESC NULLS LAST;",yelp,order_by,"EXAMPLE QUESTION 1: What are the top 2 categories of businesses with the highest average rating?
EXAMPLE QUERY 1: SELECT category.category_name FROM (SELECT business.name, business.business_id, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id GROUP BY business.name, business.business_id) AS business_rating JOIN category ON business_rating.business_id = category.business_id GROUP BY category.category_name ORDER BY AVG(business_rating.average_rating) DESC NULLS LAST LIMIT 2

EXAMPLE QUESTION 2: What are the top 3 businesses in terms of review count?
EXAMPLE QUERY 2: SELECT business.name, business.review_count FROM business ORDER BY business.review_count DESC NULLS LAST LIMIT 3

"
What is the latitude and longitude of the business with the highest rating?,"SELECT business.latitude, business.longitude FROM business JOIN review ON business.business_id = review.business_id GROUP BY review.rating, business.latitude, business.longitude ORDER BY AVG(review.rating) DESC NULLS LAST LIMIT 1;",yelp,order_by,"EXAMPLE QUESTION 1: What is the average rating of businesses in the city of San Francisco?
EXAMPLE QUERY 1: SELECT AVG(sf.average_rating) AS sf_average_rating FROM (SELECT business.business_id, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id WHERE LOWER(business.city) ILIKE '%san francisco%' GROUP BY business.business_id) AS sf

EXAMPLE QUESTION 2: What are the top 2 categories of businesses with the highest average rating?
EXAMPLE QUERY 2: SELECT category.category_name FROM (SELECT business.name, business.business_id, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id GROUP BY business.name, business.business_id) AS business_rating JOIN category ON business_rating.business_id = category.business_id GROUP BY category.category_name ORDER BY AVG(business_rating.average_rating) DESC NULLS LAST LIMIT 2

"
What are the top 3 businesses in terms of review count?,"SELECT {business.name, business.business_id, business.bid}, business.review_count FROM business ORDER BY business.review_count DESC NULLS LAST LIMIT 3;",yelp,order_by,"EXAMPLE QUESTION 1: What are the top 2 categories of businesses with the highest average rating?
EXAMPLE QUERY 1: SELECT category.category_name FROM (SELECT business.name, business.business_id, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id GROUP BY business.name, business.business_id) AS business_rating JOIN category ON business_rating.business_id = category.business_id GROUP BY category.category_name ORDER BY AVG(business_rating.average_rating) DESC NULLS LAST LIMIT 2

EXAMPLE QUESTION 2: Please provide a list of business names in New York and their average ratings ordered by the highest average rating first.
EXAMPLE QUERY 2: SELECT business.name, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id WHERE business.city ILIKE '%New York%' GROUP BY business.name ORDER BY average_rating DESC NULLS LAST

"
"What are the names of the businesses in the database, ordered alphabetically?",SELECT business.name FROM business ORDER BY business.name ASC NULLS LAST;,yelp,order_by,"EXAMPLE QUESTION 1: Please provide a list of business names in New York and their average ratings ordered by the highest average rating first.
EXAMPLE QUERY 1: SELECT business.name, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id WHERE business.city ILIKE '%New York%' GROUP BY business.name ORDER BY average_rating DESC NULLS LAST

EXAMPLE QUESTION 2: What is the ratio of the number of businesses in each state to the total number of businesses in the database?
EXAMPLE QUERY 2: SELECT business.state, COUNT(business.business_id) / NULLIF(CAST((SELECT COUNT(*) FROM business) AS FLOAT), 0) AS ratio FROM business GROUP BY business.state

"
"How many reviews were posted in each month of the year 2021, ordered by the month?","SELECT review.month, COUNT(review.rid) AS review_count FROM review WHERE review.year = 2021 GROUP BY review.month ORDER BY TO_DATE(review.month, 'MONTH') NULLS LAST;",yelp,order_by,"EXAMPLE QUESTION 1: How many reviews were there 2 months before the review with id 3?
EXAMPLE QUERY 1: SELECT count(*) AS review_count FROM review WHERE (cast(review.year AS text) || '-' || review.month || '-01')::date = (SELECT (cast(r.year AS text) || '-' || r.month || '-01')::date - interval '2 months' FROM review r WHERE r.rid = 3)

EXAMPLE QUESTION 2: How many reviews were posted for each business id in the year 2021?
EXAMPLE QUERY 2: SELECT review.business_id, COUNT(*) AS review_count FROM review WHERE review.year = 2021 GROUP BY review.business_id ORDER BY review_count DESC NULLS LAST

"
What is the ratio of the number of businesses in each state to the total number of businesses in the database?,"SELECT business.state, COUNT(business.business_id) / NULLIF(CAST((SELECT COUNT(*) FROM business) AS FLOAT), 0) AS ratio FROM business GROUP BY business.state;",yelp,ratio,"EXAMPLE QUESTION 1: What is the ratio of businesses in the state of California to businesses in the state of New York?
EXAMPLE QUERY 1: SELECT CAST(COUNT(CASE WHEN business.state = 'CA' THEN 1 END) AS FLOAT) / NULLIF(COUNT(CASE WHEN business.state = 'NY' THEN 1 END), 0) AS ratio FROM business

EXAMPLE QUESTION 2: What is the ratio of open businesses to closed businesses in the city of San Francisco?
EXAMPLE QUERY 2: SELECT CAST(SUM(CASE WHEN business.is_open = 1 THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN business.is_open = 0 THEN 1 ELSE 0 END), 0) AS ratio FROM business WHERE LOWER(business.city) ILIKE '%san francisco%'

"
What is the ratio of open businesses to closed businesses in the city of San Francisco?,"SELECT CAST(SUM(CASE WHEN business.is_open = 1 THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN business.is_open = 0 THEN 1 ELSE 0 END), 0) AS ratio FROM business WHERE LOWER(business.city) ILIKE '%san francisco%';",yelp,ratio,"EXAMPLE QUESTION 1: What is the average rating of businesses in the city of San Francisco?
EXAMPLE QUERY 1: SELECT AVG(sf.average_rating) AS sf_average_rating FROM (SELECT business.business_id, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id WHERE LOWER(business.city) ILIKE '%san francisco%' GROUP BY business.business_id) AS sf

EXAMPLE QUESTION 2: What is the ratio of businesses in the state of California to businesses in the state of New York?
EXAMPLE QUERY 2: SELECT CAST(COUNT(CASE WHEN business.state = 'CA' THEN 1 END) AS FLOAT) / NULLIF(COUNT(CASE WHEN business.state = 'NY' THEN 1 END), 0) AS ratio FROM business

"
"What is the ratio of check-ins on weekends to check-ins on weekdays for the business named ""Mark's Bistro""?","SELECT CAST(SUM(CASE WHEN checkin.day IN ('Saturday', 'Sunday') THEN checkin.count ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN checkin.day NOT IN ('Saturday', 'Sunday') THEN checkin.count ELSE 0 END), 0) AS ratio FROM checkin JOIN business ON checkin.business_id = business.business_id WHERE business.name ILIKE '%Mark''s Bistro%';",yelp,ratio,"EXAMPLE QUESTION 1: What is the total number of check-ins for each day of the week for the business with ID ""abc123""?
EXAMPLE QUERY 1: SELECT checkin.day, SUM(checkin.count) AS total_checkins FROM checkin WHERE checkin.business_id = 'abc123' GROUP BY checkin.day ORDER BY total_checkins DESC NULLS LAST

EXAMPLE QUESTION 2: How many check-ins occurred on Mondays at businesses in the state of California?
EXAMPLE QUERY 2: SELECT SUM(checkin.count) AS total_checkins FROM business JOIN checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' AND checkin.day ILIKE '%Monday%'

"
What is the ratio of businesses in the state of California to businesses in the state of New York?,"SELECT CAST(COUNT(CASE WHEN business.state = 'CA' THEN 1 END) AS FLOAT) / NULLIF(COUNT(CASE WHEN business.state = 'NY' THEN 1 END), 0) AS ratio FROM business;",yelp,ratio,"EXAMPLE QUESTION 1: What is the ratio of the number of businesses in each state to the total number of businesses in the database?
EXAMPLE QUERY 1: SELECT business.state, COUNT(business.business_id) / NULLIF(CAST((SELECT COUNT(*) FROM business) AS FLOAT), 0) AS ratio FROM business GROUP BY business.state

EXAMPLE QUESTION 2: What is the ratio of open businesses to closed businesses in the city of San Francisco?
EXAMPLE QUERY 2: SELECT CAST(SUM(CASE WHEN business.is_open = 1 THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN business.is_open = 0 THEN 1 ELSE 0 END), 0) AS ratio FROM business WHERE LOWER(business.city) ILIKE '%san francisco%'

"
"How does the ratio of positive reviews (rating > 3) to negative reviews (rating < 3) vary across different categories of businesses, ordered by descending ratio?","SELECT {category.category_name, category.id}, CAST(COUNT(CASE WHEN review.rating > 3 THEN 1 END) AS FLOAT) / NULLIF(COUNT(CASE WHEN review.rating < 3 THEN 1 END), 0) AS ratio FROM review JOIN category ON review.business_id = category.business_id GROUP BY {} ORDER BY ratio DESC NULLS LAST;",yelp,ratio,"EXAMPLE QUESTION 1: What is the total number of reviews for each business category?
EXAMPLE QUERY 1: SELECT category.category_name, SUM(business.review_count) AS total_reviews FROM business JOIN category ON business.business_id = category.business_id GROUP BY category.category_name ORDER BY total_reviews DESC NULLS LAST

EXAMPLE QUESTION 2: What are the top 2 categories of businesses with the highest average rating?
EXAMPLE QUERY 2: SELECT category.category_name FROM (SELECT business.name, business.business_id, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id GROUP BY business.name, business.business_id) AS business_rating JOIN category ON business_rating.business_id = category.business_id GROUP BY category.category_name ORDER BY AVG(business_rating.average_rating) DESC NULLS LAST LIMIT 2

"
"Which users have posted reviews for businesses located in the neighbourhood of ""Downtown"" and how many reviews have they posted?","SELECT {users.name, users.user_id}, COUNT(review.rid) AS review_count FROM review JOIN neighbourhood ON review.business_id = neighbourhood.business_id JOIN users ON review.user_id = users.user_id WHERE neighbourhood.neighbourhood_name ILIKE '%Downtown%' GROUP BY {} ORDER BY review_count DESC NULLS LAST;",yelp,table_join,"EXAMPLE QUESTION 1: How many reviews were written for businesses located in California in the last 1000 months?
EXAMPLE QUERY 1: SELECT count(*) AS review_count FROM review r JOIN business b ON r.business_id = b.business_id WHERE b.state = 'CA' AND (r.year * 12 + extract(MONTH FROM to_date(r.month, 'Month'))) >= (extract(YEAR FROM CURRENT_DATE) * 12 + extract(MONTH FROM CURRENT_DATE) - 1000)

EXAMPLE QUESTION 2: What is the total number of reviews posted in the year 2021 for businesses in the category ""Cafe""?
EXAMPLE QUERY 2: SELECT COUNT(review.rid) AS total_reviews FROM review JOIN category ON review.business_id = category.business_id WHERE review.year = 2021 AND category.category_name ILIKE '%Cafe%'

"
"What is the total number of reviews for each category in the state of ""California""?","SELECT {category.category_name, category.id}, SUM(business.review_count) AS total_reviews FROM business JOIN category ON business.business_id = category.business_id WHERE business.state = 'CA' GROUP BY {} ORDER BY total_reviews DESC NULLS LAST;",yelp,table_join,"EXAMPLE QUESTION 1: What is the total number of reviews for each business category?
EXAMPLE QUERY 1: SELECT category.category_name, SUM(business.review_count) AS total_reviews FROM business JOIN category ON business.business_id = category.business_id GROUP BY category.category_name ORDER BY total_reviews DESC NULLS LAST

EXAMPLE QUESTION 2: What is the total number of reviews posted in the year 2021 for businesses in the category ""Cafe""?
EXAMPLE QUERY 2: SELECT COUNT(review.rid) AS total_reviews FROM review JOIN category ON review.business_id = category.business_id WHERE review.year = 2021 AND category.category_name ILIKE '%Cafe%'

"
What is the total number of reviews for each business category?,"SELECT {category.category_name, category.id}, SUM(business.review_count) AS total_reviews FROM business JOIN category ON business.business_id = category.business_id GROUP BY {} ORDER BY total_reviews DESC NULLS LAST;",yelp,table_join,"EXAMPLE QUESTION 1: What is the total number of reviews posted in the year 2021 for businesses in the category ""Cafe""?
EXAMPLE QUERY 1: SELECT COUNT(review.rid) AS total_reviews FROM review JOIN category ON review.business_id = category.business_id WHERE review.year = 2021 AND category.category_name ILIKE '%Cafe%'

EXAMPLE QUESTION 2: What is the total number of reviews for each category in the state of ""California""?
EXAMPLE QUERY 2: SELECT category.category_name, SUM(business.review_count) AS total_reviews FROM business JOIN category ON business.business_id = category.business_id WHERE business.state = 'CA' GROUP BY category.category_name ORDER BY total_reviews DESC NULLS LAST

"
What is the total number of check-ins for each business in the state of California?,"SELECT {business.business_id, business.name, business.bid}, SUM(checkin.count) AS total_checkins FROM business JOIN checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' GROUP BY {} ORDER BY total_checkins DESC NULLS LAST;",yelp,table_join,"EXAMPLE QUESTION 1: What is the total count of check-ins for each business id?
EXAMPLE QUERY 1: SELECT checkin.business_id, SUM(checkin.count) AS total_checkins FROM checkin GROUP BY checkin.business_id ORDER BY total_checkins DESC NULLS LAST

EXAMPLE QUESTION 2: How many check-ins occurred on Mondays at businesses in the state of California?
EXAMPLE QUERY 2: SELECT SUM(checkin.count) AS total_checkins FROM business JOIN checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' AND checkin.day ILIKE '%Monday%'

"
What are the top 2 categories of businesses with the highest average rating?,"SELECT {category.category_name, category.id} FROM (SELECT business.name, business.business_id, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id GROUP BY business.name, business.business_id) AS business_rating JOIN category ON business_rating.business_id = category.business_id GROUP BY {} ORDER BY AVG(business_rating.average_rating) DESC NULLS LAST LIMIT 2;",yelp,table_join,"EXAMPLE QUESTION 1: Please provide a list of business names in New York and their average ratings ordered by the highest average rating first.
EXAMPLE QUERY 1: SELECT business.name, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id WHERE business.city ILIKE '%New York%' GROUP BY business.name ORDER BY average_rating DESC NULLS LAST

EXAMPLE QUESTION 2: What are the top 3 businesses in terms of review count?
EXAMPLE QUERY 2: SELECT business.name, business.review_count FROM business ORDER BY business.review_count DESC NULLS LAST LIMIT 3

"
"What is the total number of reviews posted in the year 2021 for businesses in the category ""Cafe""?",SELECT COUNT(review.rid) AS total_reviews FROM review JOIN category ON review.business_id = category.business_id WHERE review.year = 2021 AND category.category_name ILIKE '%Cafe%';,yelp,where,"EXAMPLE QUESTION 1: What is the total number of reviews for each business category?
EXAMPLE QUERY 1: SELECT category.category_name, SUM(business.review_count) AS total_reviews FROM business JOIN category ON business.business_id = category.business_id GROUP BY category.category_name ORDER BY total_reviews DESC NULLS LAST

EXAMPLE QUESTION 2: How many reviews were posted for each business id in the year 2021?
EXAMPLE QUERY 2: SELECT review.business_id, COUNT(*) AS review_count FROM review WHERE review.year = 2021 GROUP BY review.business_id ORDER BY review_count DESC NULLS LAST

"
What is the average rating of businesses in the city of San Francisco?,"SELECT AVG(sf.average_rating) AS sf_average_rating FROM (SELECT business.business_id, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id WHERE LOWER(business.city) ILIKE '%san francisco%' GROUP BY business.business_id) AS sf;",yelp,where,"EXAMPLE QUESTION 1: What is the ratio of open businesses to closed businesses in the city of San Francisco?
EXAMPLE QUERY 1: SELECT CAST(SUM(CASE WHEN business.is_open = 1 THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(SUM(CASE WHEN business.is_open = 0 THEN 1 ELSE 0 END), 0) AS ratio FROM business WHERE LOWER(business.city) ILIKE '%san francisco%'

EXAMPLE QUESTION 2: What are the top 2 categories of businesses with the highest average rating?
EXAMPLE QUERY 2: SELECT category.category_name FROM (SELECT business.name, business.business_id, AVG(review.rating) AS average_rating FROM business JOIN review ON business.business_id = review.business_id GROUP BY business.name, business.business_id) AS business_rating JOIN category ON business_rating.business_id = category.business_id GROUP BY category.category_name ORDER BY AVG(business_rating.average_rating) DESC NULLS LAST LIMIT 2

"
How many reviews were posted for each business id in the year 2021?,"SELECT review.business_id, COUNT(*) AS review_count FROM review WHERE review.year = 2021 GROUP BY review.business_id ORDER BY review_count DESC NULLS LAST;",yelp,where,"EXAMPLE QUESTION 1: How many reviews were there 2 months before the review with id 3?
EXAMPLE QUERY 1: SELECT count(*) AS review_count FROM review WHERE (cast(review.year AS text) || '-' || review.month || '-01')::date = (SELECT (cast(r.year AS text) || '-' || r.month || '-01')::date - interval '2 months' FROM review r WHERE r.rid = 3)

EXAMPLE QUESTION 2: How many reviews were written for businesses located in California in the last 1000 months?
EXAMPLE QUERY 2: SELECT count(*) AS review_count FROM review r JOIN business b ON r.business_id = b.business_id WHERE b.state = 'CA' AND (r.year * 12 + extract(MONTH FROM to_date(r.month, 'Month'))) >= (extract(YEAR FROM CURRENT_DATE) * 12 + extract(MONTH FROM CURRENT_DATE) - 1000)

"
"How many reviews were posted by users with the name ""Sarah Williams"" in the month of April?",SELECT COUNT(*) FROM review JOIN users ON review.user_id = users.user_id WHERE users.name ILIKE '%Sarah Williams%' AND review.month ILIKE '%April%';,yelp,where,"EXAMPLE QUESTION 1: How many reviews were there 2 months before the review with id 3?
EXAMPLE QUERY 1: SELECT count(*) AS review_count FROM review WHERE (cast(review.year AS text) || '-' || review.month || '-01')::date = (SELECT (cast(r.year AS text) || '-' || r.month || '-01')::date - interval '2 months' FROM review r WHERE r.rid = 3)

EXAMPLE QUESTION 2: How many reviews were posted in each month of the year 2021, ordered by the month?
EXAMPLE QUERY 2: SELECT review.month, COUNT(review.rid) AS review_count FROM review WHERE review.year = 2021 GROUP BY review.month ORDER BY TO_DATE(review.month, 'MONTH') NULLS LAST

"
How many check-ins occurred on Mondays at businesses in the state of California?,SELECT SUM(checkin.count) AS total_checkins FROM business JOIN checkin ON business.business_id = checkin.business_id WHERE business.state = 'CA' AND checkin.day ILIKE '%Monday%';,yelp,where,"EXAMPLE QUESTION 1: How many check-ins occurred on each day of the week?
EXAMPLE QUERY 1: SELECT checkin.day, SUM(checkin.count) AS total_checkins FROM checkin GROUP BY checkin.day ORDER BY total_checkins DESC NULLS LAST

EXAMPLE QUESTION 2: What is the total number of check-ins for each day of the week for the business with ID ""abc123""?
EXAMPLE QUERY 2: SELECT checkin.day, SUM(checkin.count) AS total_checkins FROM checkin WHERE checkin.business_id = 'abc123' GROUP BY checkin.day ORDER BY total_checkins DESC NULLS LAST

"
