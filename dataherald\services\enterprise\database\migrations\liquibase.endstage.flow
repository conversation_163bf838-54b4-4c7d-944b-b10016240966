##########           LIQUIBASE FLOW FILE               ##########
##########  learn more http://docs.liquibase.com/flow  ##########

## NOTE: This example flowfile is called from the examples/liquibase.advanced.flowfile.yaml file
## While it could be run on its own, this file is designed to show that flow-files can be decomposed
## into separate files as makes sense for your use cases.

stages:

  cleanuptheDB:

    actions:

   #
   # Clear out the database
   #
      - type: liquibase
        command: dropAll

   #
   # Check that database is empty by seeing what is ready to be deployed
   #    
      - type: liquibase
        command: status
        cmdArgs: {verbose: TRUE}


## The endStage ALWAYS RUNS. 
## So put actions here which you desire to perform whether previous stages' actions succeed or fail.
## If you do not want any actions to ALWAYS RUN, simply delete the endStage from your flow file,
## as it has been deleted here in this liquibase.endStage.flow file.
