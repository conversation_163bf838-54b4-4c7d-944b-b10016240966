{"containerDefinitions": [{"name": "ai-slackbot-stage", "image": "422486916789.dkr.ecr.us-east-1.amazonaws.com/ai-slackbot-stage:latest", "cpu": 0, "portMappings": [{"name": "ai-slackbot-stage-3000-tcp", "containerPort": 3000, "hostPort": 3000, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [], "environmentFiles": [{"value": "arn:aws:s3:::ai-slackbot-stage-environment-variables/.env", "type": "s3"}], "mountPoints": [], "volumesFrom": [], "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-create-group": "true", "awslogs-group": "/ecs/ai-slackbot-stage", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}}], "family": "ai-slackbot-stage", "taskRoleArn": "arn:aws:iam::422486916789:role/ecsk2TaskExecutionRole", "executionRoleArn": "arn:aws:iam::422486916789:role/ecsk2TaskExecutionRole", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "3072", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "tags": []}