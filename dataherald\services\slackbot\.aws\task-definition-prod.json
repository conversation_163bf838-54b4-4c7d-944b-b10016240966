{"taskDefinitionArn": "arn:aws:ecs:us-east-1:422486916789:task-definition/ai-slackbot-prod:1", "containerDefinitions": [{"name": "ai-slackbot-prod", "image": "422486916789.dkr.ecr.us-east-1.amazonaws.com/ai-slackbot-prod:latest", "cpu": 0, "portMappings": [{"name": "ai-slackbot-prod-3000-tcp", "containerPort": 3000, "hostPort": 3000, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [], "environmentFiles": [{"value": "arn:aws:s3:::ai-slackbot-prod-environment-variables/.env", "type": "s3"}], "mountPoints": [], "volumesFrom": [], "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-create-group": "true", "awslogs-group": "/ecs/ai-slackbot-prod", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}}], "family": "ai-slackbot-prod", "executionRoleArn": "arn:aws:iam::422486916789:role/ecsk2TaskExecutionRole", "networkMode": "awsvpc", "revision": 1, "volumes": [], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "3072", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "registeredAt": "2023-09-01T20:49:51.671Z", "registeredBy": "arn:aws:iam::422486916789:user/valak", "tags": []}