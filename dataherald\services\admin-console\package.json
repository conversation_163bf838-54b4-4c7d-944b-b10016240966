{"name": "admin-console", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write './**/*.{js,jsx,ts,tsx,json,css,md}'", "format:check": "prettier --check './**/*.{js,jsx,ts,tsx,json,css,md}'", "test": "jest"}, "dependencies": {"@auth0/nextjs-auth0": "^3.1.0", "@hookform/resolvers": "^3.3.1", "@monaco-editor/react": "^4.5.1", "@radix-ui/react-alert-dialog": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.4", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.6", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.4", "@radix-ui/react-tooltip": "^1.0.7", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.4.0", "@tanstack/react-table": "^8.9.3", "@types/node": "20.4.2", "@types/react": "18.2.15", "@types/react-dom": "18.2.7", "ai": "^3.0.15", "autoprefixer": "10.4.14", "chart.js": "^4.4.1", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "eslint": "8.45.0", "eslint-config-next": "13.4.10", "lucide-react": "^0.305.0", "monaco-editor": "^0.40.0", "next": "13.4.10", "postcss": "8.4.26", "posthog-js": "^1.81.1", "prismjs": "^1.29.0", "react": "18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.46.1", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "rehype-highlight": "^7.0.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "sharp": "^0.33.3", "sql-formatter": "^13.0.0", "swr": "^2.2.0", "tailwind-merge": "^1.14.0", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.6", "yup": "^1.3.0"}, "devDependencies": {"@types/jest": "^29.5.3", "@types/react-syntax-highlighter": "^15.5.11", "@typescript-eslint/eslint-plugin": "^5.62.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.6.1", "jest-fetch-mock": "^3.0.3", "prettier": "^2.8.8", "ts-jest": "^29.1.1", "typescript": "5.1.6"}}