import { API_URL, AUTH } from '@/config'
import { serverFetcher } from '@/lib/api/server-fetcher'
import { User } from '@/models/api'
import {
  AfterCallback,
  HandlerError,
  Session,
  handleAuth,
  handleCallback,
  handleLogin,
} from '@auth0/nextjs-auth0'
import { NextApiRequest, NextApiResponse } from 'next'
import { isDevMode, DEV_USER, DEV_TOKEN, createDevSession } from '@/middleware/dev-auth'

/**
 * The signup and login will be handled by auth0. We would have to deal with both in the backend and do each flow depending on if the user exists or not.
 */
const afterCallback: AfterCallback = async (
  req: NextApiRequest,
  res: NextApiResponse,
  session: Session,
) => {
  const { user: auth0User, accessToken: token } = session
  try {
    const user: User = await serverFetcher<User>(`${API_URL}/auth/login`, {
      body: JSON.stringify(auth0User),
      method: 'POST',
      token,
    })
    const sessionUser: User = {
      ...auth0User,
      ...user,
    }
    session.user = sessionUser
  } catch (e: unknown) {
    const error = e as Error
    console.error(error)
    res.writeHead(302, {
      Location: `/auth/error?message=${encodeURIComponent(e as string)}`,
    })
    res.end()
  }
  return session
}

// Development authentication handlers
const devLogin = async (req: NextApiRequest, res: NextApiResponse) => {
  if (isDevMode()) {
    console.log('Development mode: bypassing Auth0 login')
    // Create a development session
    const session = createDevSession()

    // Set session cookie manually for development
    res.setHeader('Set-Cookie', [
      `appSession=${JSON.stringify(session)}; Path=/; HttpOnly; SameSite=Lax`,
    ])

    res.writeHead(302, {
      Location: '/databases',
    })
    res.end()
    return
  }

  // Fall back to normal Auth0 login
  return handleLogin({
    authorizationParams: {
      scope: AUTH.scope,
      audience: AUTH.audience,
    },
  })(req, res)
}

const devCallback = async (req: NextApiRequest, res: NextApiResponse) => {
  if (isDevMode()) {
    console.log('Development mode: bypassing Auth0 callback')
    res.writeHead(302, {
      Location: '/databases',
    })
    res.end()
    return
  }

  // Fall back to normal Auth0 callback
  return handleCallback({ afterCallback })(req, res)
}

export default handleAuth({
  login: devLogin,
  signup: devLogin, // Use same dev login for signup in dev mode
  callback: devCallback,
  onError(_: NextApiRequest, res: NextApiResponse, error: HandlerError) {
    console.error(error)
    res.writeHead(302, {
      Location: `/auth/error?message=${encodeURIComponent(error.message)}`,
    })
    res.end()
  },
})
