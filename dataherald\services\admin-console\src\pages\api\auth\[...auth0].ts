import { API_URL, AUTH } from '@/config'
import { serverFetcher } from '@/lib/api/server-fetcher'
import { User } from '@/models/api'
import {
  AfterCallback,
  HandlerError,
  Session,
  handleAuth,
  handleCallback,
  handleLogin,
} from '@auth0/nextjs-auth0'
import { NextApiRequest, NextApiResponse } from 'next'
import { isDevMode, DEV_USER, DEV_TOKEN, createDevSession } from '@/middleware/dev-auth'

/**
 * The signup and login will be handled by auth0. We would have to deal with both in the backend and do each flow depending on if the user exists or not.
 */
const afterCallback: AfterCallback = async (
  req: NextApiRequest,
  res: NextApiResponse,
  session: Session,
) => {
  const { user: auth0User, accessToken: token } = session
  try {
    const user: User = await serverFetcher<User>(`${API_URL}/auth/login`, {
      body: JSON.stringify(auth0User),
      method: 'POST',
      token,
    })
    const sessionUser: User = {
      ...auth0User,
      ...user,
    }
    session.user = sessionUser
  } catch (e: unknown) {
    const error = e as Error
    console.error(error)
    res.writeHead(302, {
      Location: `/auth/error?message=${encodeURIComponent(e as string)}`,
    })
    res.end()
  }
  return session
}

// Complete development authentication bypass
const devAuthHandler = async (req: NextApiRequest, res: NextApiResponse) => {
  // Always bypass in development - make this more robust
  const nodeEnv = process.env.NODE_ENV
  const skipAuth = process.env.SKIP_AUTH
  const authDisabled = process.env.AUTH0_DISABLED

  console.log('Auth handler debug:', { nodeEnv, skipAuth, authDisabled })

  // Multiple conditions to ensure development bypass works
  const isDevelopmentMode = nodeEnv === 'development' ||
                           skipAuth === 'true' ||
                           authDisabled === 'true' ||
                           authDisabled === true

  if (isDevelopmentMode) {
    console.log('🎯 DEVELOPMENT MODE: Completely bypassing Auth0 - redirecting to databases')

    // Set a simple session cookie for development
    res.setHeader('Set-Cookie', [
      'dev-session=authenticated; Path=/; HttpOnly; SameSite=Lax; Max-Age=86400',
    ])

    // Always redirect to databases page in development
    res.writeHead(302, {
      Location: '/databases',
    })
    res.end()
    return
  }

  // Production mode - this should not be reached in development
  console.log('🔒 PRODUCTION MODE: Using Auth0 handlers')

  try {
    // Use the default Auth0 handler for production
    return handleAuth({
      login: handleLogin({
        authorizationParams: {
          scope: AUTH.scope,
          audience: AUTH.audience,
        },
      }),
      signup: handleLogin({
        authorizationParams: {
          scope: AUTH.scope,
          audience: AUTH.audience,
          screen_hint: 'signup',
        },
      }),
      callback: handleCallback({ afterCallback }),
      onError(_: NextApiRequest, res: NextApiResponse, error: HandlerError) {
        console.error('Auth0 Error:', error)
        res.writeHead(302, {
          Location: `/auth/error?message=${encodeURIComponent(error.message)}`,
        })
        res.end()
      },
    })(req, res)
  } catch (error) {
    console.error('Auth handler error:', error)
    res.status(500).json({ error: 'Authentication error' })
  }
}

// Export the complete development bypass handler
export default devAuthHandler
